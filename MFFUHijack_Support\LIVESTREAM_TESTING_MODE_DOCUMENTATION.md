# 🧪 Live Stream Testing Mode for MFFUHijack

## 🎯 Overview

The Live Stream Testing Mode is a comprehensive testing system that allows you to test code detection on previous livestreams with configurable frame intervals and code validation **without actually purchasing**. This mode is perfect for testing OCR accuracy, validating code detection algorithms, and analyzing historical stream data.

## ✨ **KEY FEATURES**

### 🎥 **Previous Stream Testing**
- **YouTube Stream Download**: Download previous livestreams for testing
- **Local Video Support**: Test with any MP4/MKV/WebM video files
- **Stream Information**: View stream metadata, duration, and quality
- **Batch Processing**: Process multiple streams sequentially

### ⏱️ **Configurable Frame Intervals**
- **Flexible Intervals**: Set scanning intervals from 1-60 seconds
- **Frame Skipping**: Efficiently skip frames based on interval settings
- **Progress Tracking**: Real-time progress monitoring with timestamps
- **Optimized Processing**: Smart frame extraction for performance

### 🔍 **Advanced OCR Processing**
- **Multiple OCR Engines**: Support for EasyOCR and PaddleOCR
- **Configurable OCR Region**: Adjust scanning area with percentage-based coordinates
- **Real-time Processing**: Live OCR results during testing
- **Confidence Scoring**: OCR confidence analysis and filtering

### ✅ **Code Validation (Test Mode)**
- **Non-Purchase Testing**: Apply codes to checkout without buying
- **Headless Browser**: Automated browser testing with Selenium
- **Multiple Account Types**: Test Reset, Starter, Expert, Starter Plus codes
- **Validation Results**: Real-time validation status and messages
- **Error Handling**: Comprehensive error detection and reporting

## 🏗️ **Technical Architecture**

### **Core Components**
```
Live Stream Testing Mode
├── 🎥 StreamProcessor - Video processing and frame extraction
├── 🔍 OCR Integration - EasyOCR/PaddleOCR processing
├── ✅ CodeValidationTester - Browser-based code validation
├── 📥 YouTubeStreamDownloader - Stream downloading with yt-dlp
├── 📊 TestingStatistics - Comprehensive testing analytics
└── 🖥️ LiveStreamTestingWindow - Main testing interface
```

### **Testing Workflow**
```
1. Stream Selection → 2. Configuration → 3. Processing → 4. Validation → 5. Results
     ↓                    ↓                ↓               ↓              ↓
• YouTube URL         • Frame interval   • OCR processing • Code testing  • Statistics
• Local files         • OCR engine       • Code detection • Validation    • Export
• Stream info         • OCR region       • Progress track • Results log   • Analysis
```

## 🎮 **User Interface**

### **Configuration Panel**
- **Stream Selection**: YouTube URL input and local file browser
- **Testing Parameters**: Frame interval, OCR engine, validation settings
- **OCR Region**: Adjustable scanning area with percentage coordinates
- **Control Buttons**: Start/stop testing with real-time control

### **Results Panel**
- **Real-time Status**: Current progress, frames processed, codes detected
- **Detection Log**: Live code detection events with timestamps
- **Validation Results**: Code validation status with success/failure indicators
- **Statistics Dashboard**: Comprehensive testing analytics and metrics

### **Testing Statistics**
- **Session Metrics**: Duration, frames processed, detection rates
- **Code Analysis**: Valid/invalid codes, success rates, error analysis
- **Performance Data**: Processing times, OCR confidence, validation speed
- **Export Options**: CSV/JSON export for detailed analysis

## 🔧 **Configuration Options**

### **Frame Interval Settings**
```python
# Configurable intervals
intervals = {
    "Fast Testing": 2.0,      # Every 2 seconds
    "Standard": 5.0,          # Every 5 seconds  
    "Thorough": 10.0,         # Every 10 seconds
    "Conservative": 15.0,     # Every 15 seconds
    "Custom": 1.0-60.0        # Any value 1-60 seconds
}
```

### **OCR Region Configuration**
```python
# Percentage-based coordinates
ocr_region = {
    "x_position": 0,          # 0-100% from left
    "y_position": 67,         # 0-100% from top
    "width": 100,             # 1-100% width
    "height": 33              # 1-100% height
}
```

### **Code Validation Settings**
```python
validation_config = {
    "enabled": True,          # Enable/disable validation
    "headless": True,         # Headless browser mode
    "timeout": 10,            # Page load timeout
    "account_types": [        # Supported account types
        "Reset", "Starter", "Expert", 
        "Starter Plus", "Free Reset Code"
    ]
}
```

## 🚀 **Getting Started**

### **Quick Start**
```bash
# Run the testing mode demo
python test_livestream_testing_mode.py

# Or access from main monitor
# Tools → Live Stream Testing Mode
```

### **Step-by-Step Guide**

#### 1. **Prepare Test Content**
```python
# Option A: Download YouTube stream
url = "https://www.youtube.com/watch?v=VIDEO_ID"
# Enter URL in testing mode and click "Download"

# Option B: Use existing video files
# Place MP4/MKV/WebM files in test_streams/ directory
```

#### 2. **Configure Testing Parameters**
```python
# Set frame interval (recommended: 5-10 seconds)
frame_interval = 5.0

# Choose OCR engine
ocr_engine = "easyocr"  # or "paddleocr"

# Configure OCR region (default works for most streams)
ocr_region = (0, 67, 100, 33)  # x, y, width, height %

# Enable code validation
validate_codes = True
```

#### 3. **Start Testing**
```python
# Click "Start Testing" in the interface
# Monitor real-time results
# View comprehensive statistics
```

## 📊 **Testing Results Analysis**

### **Real-time Metrics**
- **Frames Processed**: Total frames analyzed
- **Codes Detected**: Number of codes found
- **Detection Rate**: Codes per frame ratio
- **Processing Speed**: Frames per minute
- **OCR Confidence**: Average confidence scores

### **Validation Results**
- **Valid Codes**: Successfully validated codes
- **Invalid Codes**: Failed validation attempts
- **Validation Errors**: Technical validation failures
- **Success Rate**: Percentage of valid codes
- **Response Analysis**: Validation message analysis

### **Performance Analytics**
- **Processing Time**: Average time per frame
- **OCR Performance**: Engine-specific metrics
- **Memory Usage**: Resource consumption tracking
- **Error Rates**: Detection and validation errors

## 🎯 **Use Cases**

### **1. Historical Analysis**
```python
# Analyze past streams for missed opportunities
# Test different OCR settings on known content
# Validate detection accuracy over time
```

### **2. Algorithm Testing**
```python
# Test new OCR engines or settings
# Validate code detection patterns
# Compare different scanning intervals
```

### **3. Training Data Generation**
```python
# Generate labeled datasets from streams
# Create OCR training examples
# Build code pattern databases
```

### **4. Performance Optimization**
```python
# Find optimal scanning intervals
# Test OCR region configurations
# Benchmark different engines
```

## 🔧 **Advanced Features**

### **Batch Testing**
```python
# Process multiple streams automatically
# Compare results across different streams
# Generate comprehensive reports
```

### **Custom OCR Integration**
```python
# Add support for additional OCR engines
# Implement custom preprocessing
# Integrate with cloud OCR services
```

### **Advanced Validation**
```python
# Custom validation logic
# Multiple validation endpoints
# Validation result caching
```

## 📈 **Performance Optimization**

### **Frame Processing**
- **Smart Skipping**: Skip frames based on interval settings
- **Efficient Decoding**: Optimized video frame extraction
- **Memory Management**: Automatic cleanup and resource management
- **Parallel Processing**: Multi-threaded OCR processing

### **OCR Optimization**
- **Region Optimization**: Focus on relevant screen areas
- **Preprocessing**: Image enhancement for better OCR
- **Confidence Filtering**: Skip low-confidence results
- **Engine Selection**: Choose optimal OCR engine

### **Validation Efficiency**
- **Browser Reuse**: Persistent browser sessions
- **Request Caching**: Cache validation results
- **Batch Validation**: Group validation requests
- **Error Recovery**: Automatic retry mechanisms

## 🧪 **Testing Examples**

### **Example 1: Quick Stream Test**
```python
# Test a 1-hour stream with 5-second intervals
# Expected: ~720 frames processed
# Typical results: 10-50 codes detected
# Validation time: 2-5 minutes
```

### **Example 2: Thorough Analysis**
```python
# Test with 2-second intervals for maximum coverage
# Higher processing time but better detection
# Useful for critical testing scenarios
```

### **Example 3: Performance Benchmark**
```python
# Compare EasyOCR vs PaddleOCR performance
# Test different OCR regions
# Analyze processing speed vs accuracy
```

## 🎉 **Benefits**

### **Risk-Free Testing**
- **No Purchase Required**: Test codes without buying
- **Safe Validation**: Browser automation without completion
- **Historical Analysis**: Test on past content safely

### **Comprehensive Analysis**
- **Detailed Statistics**: Complete testing metrics
- **Performance Insights**: OCR and validation performance
- **Export Capabilities**: Data export for further analysis

### **Optimization Tools**
- **Parameter Tuning**: Find optimal settings
- **Algorithm Testing**: Validate detection improvements
- **Efficiency Analysis**: Optimize scanning strategies

---

## 📋 **Quick Reference**

### **File Structure**
```
livestream_testing_mode.py          # Core testing components
livestream_testing_gui.py           # Main testing interface
test_livestream_testing_mode.py     # Demo and testing script
test_streams/                       # Downloaded/sample videos
screenshots/                        # Testing screenshots
```

### **Key Classes**
- `LiveStreamTestingWindow` - Main testing interface
- `TestingWorker` - Background testing thread
- `StreamProcessor` - Video processing engine
- `CodeValidationTester` - Browser-based validation
- `TestingStatistics` - Analytics and metrics

### **Dependencies**
- `PyQt6` - GUI framework
- `opencv-python` - Video processing
- `yt-dlp` - YouTube downloading
- `selenium` - Browser automation
- `easyocr/paddleocr` - OCR engines

**Ready to test your code detection on previous livestreams? Launch the testing mode and start analyzing!**
