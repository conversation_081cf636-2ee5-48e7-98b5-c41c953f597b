"""
Code Submission Module for MFFUHijack
Handles HTTP POST requests to submit detected codes with deduplication
"""

import requests
import json
import time
import os
from datetime import datetime
from typing import Set, Dict, Any, Optional
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class CodeSubmitter:
    """Handles code submission with deduplication and logging"""
    
    def __init__(self, api_endpoint: str = "https://myfundedfutures.com/mock-code-submit"):
        self.api_endpoint = api_endpoint
        self.submitted_codes: Set[str] = set()
        self.log_file = "logs/run_log.txt"
        self.session = requests.Session()
        
        # Set up session headers
        self.session.headers.update({
            'User-Agent': 'MFFUHijack/1.0.0',
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        })
        
        # Load previously submitted codes from log
        self._load_submitted_codes()
        
        # Ensure log directory exists
        os.makedirs(os.path.dirname(self.log_file), exist_ok=True)
    
    def _load_submitted_codes(self):
        """Load previously submitted codes from log file to avoid duplicates"""
        if os.path.exists(self.log_file):
            try:
                with open(self.log_file, 'r', encoding='utf-8') as f:
                    for line in f:
                        if 'SUBMITTED:' in line:
                            # Extract code from log line
                            parts = line.split('SUBMITTED:')
                            if len(parts) > 1:
                                code = parts[1].split()[0].strip()
                                self.submitted_codes.add(code)
                logger.info(f"Loaded {len(self.submitted_codes)} previously submitted codes")
            except Exception as e:
                logger.error(f"Failed to load submitted codes from log: {e}")
    
    def _log_event(self, event_type: str, message: str, code: Optional[str] = None):
        """Log events to file and console"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_message = f"[{timestamp}] {event_type}: {message}"
        
        if code:
            log_message += f" (Code: {code})"
        
        # Log to console
        logger.info(log_message)
        
        # Log to file
        try:
            with open(self.log_file, 'a', encoding='utf-8') as f:
                f.write(log_message + '\n')
        except Exception as e:
            logger.error(f"Failed to write to log file: {e}")
    
    def is_code_submitted(self, code: str) -> bool:
        """Check if code has already been submitted"""
        return code in self.submitted_codes
    
    def submit_code(self, code_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Submit a detected code to the API endpoint
        
        Args:
            code_data: Dictionary containing code information
                - code: The actual code string
                - type: Code type (STARTER, RESETS, EVALUATION)
                - confidence: OCR confidence score
                - timestamp: When the code was detected
        
        Returns:
            Dictionary with submission result
        """
        code = code_data.get('code', '').strip()
        
        if not code:
            return {'success': False, 'error': 'Empty code provided'}
        
        # Check for duplicates
        if self.is_code_submitted(code):
            self._log_event('DUPLICATE', f'Code already submitted: {code}', code)
            return {'success': False, 'error': 'Code already submitted', 'duplicate': True}
        
        # Prepare submission payload
        payload = {
            'code': code,
            'type': code_data.get('type', 'UNKNOWN'),
            'confidence': code_data.get('confidence', 0.0),
            'timestamp': code_data.get('timestamp', datetime.now().isoformat()),
            'source': 'MFFUHijack',
            'version': '1.0.0'
        }
        
        try:
            # Submit to API
            self._log_event('SUBMITTING', f'Attempting to submit code: {code}', code)
            
            response = self.session.post(
                self.api_endpoint,
                json=payload,
                timeout=10
            )
            
            # Process response
            if response.status_code == 200:
                # Success
                self.submitted_codes.add(code)
                self._log_event('SUBMITTED', f'Successfully submitted code: {code}', code)
                
                try:
                    response_data = response.json()
                except:
                    response_data = {'message': 'Success'}
                
                return {
                    'success': True,
                    'code': code,
                    'response': response_data,
                    'status_code': response.status_code
                }
            
            else:
                # API error
                error_msg = f'API returned status {response.status_code}'
                try:
                    error_data = response.json()
                    error_msg += f': {error_data.get("message", "Unknown error")}'
                except:
                    error_msg += f': {response.text[:100]}'
                
                self._log_event('ERROR', f'Failed to submit code {code} - {error_msg}', code)
                
                return {
                    'success': False,
                    'error': error_msg,
                    'status_code': response.status_code
                }
        
        except requests.exceptions.Timeout:
            error_msg = 'Request timeout'
            self._log_event('ERROR', f'Timeout submitting code {code}', code)
            return {'success': False, 'error': error_msg}
        
        except requests.exceptions.ConnectionError:
            error_msg = 'Connection error'
            self._log_event('ERROR', f'Connection error submitting code {code}', code)
            return {'success': False, 'error': error_msg}
        
        except Exception as e:
            error_msg = f'Unexpected error: {str(e)}'
            self._log_event('ERROR', f'Unexpected error submitting code {code} - {error_msg}', code)
            return {'success': False, 'error': error_msg}
    
    def submit_multiple_codes(self, codes_list: list) -> Dict[str, Any]:
        """
        Submit multiple codes in sequence
        
        Args:
            codes_list: List of code dictionaries
        
        Returns:
            Summary of submission results
        """
        results = {
            'total': len(codes_list),
            'submitted': 0,
            'duplicates': 0,
            'errors': 0,
            'details': []
        }
        
        for code_data in codes_list:
            result = self.submit_code(code_data)
            results['details'].append(result)
            
            if result.get('success'):
                results['submitted'] += 1
            elif result.get('duplicate'):
                results['duplicates'] += 1
            else:
                results['errors'] += 1
            
            # Small delay between submissions to be respectful
            time.sleep(0.1)
        
        self._log_event('BATCH_COMPLETE', 
                       f'Batch submission complete: {results["submitted"]} submitted, '
                       f'{results["duplicates"]} duplicates, {results["errors"]} errors')
        
        return results
    
    def get_submission_stats(self) -> Dict[str, Any]:
        """Get statistics about submissions"""
        return {
            'total_submitted': len(self.submitted_codes),
            'submitted_codes': list(self.submitted_codes),
            'log_file': self.log_file
        }
    
    def clear_submitted_codes(self):
        """Clear the submitted codes cache (for testing)"""
        self.submitted_codes.clear()
        self._log_event('RESET', 'Cleared submitted codes cache')


# Global submitter instance
code_submitter = CodeSubmitter()
