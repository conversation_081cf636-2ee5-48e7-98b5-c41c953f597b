"""
Modern PyQt6 GUI for MFFUHijack
Elegant, simple design with optimized sizing and smooth animations
"""

import logging
from datetime import datetime
from PyQt6.QtWidgets import (
    QMainWindow, QTabWidget, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QLineEdit, QPushButton, QComboBox, QTextEdit,
    QProgressBar, QGroupBox, QGridLayout, QCheckBox, QSplitter,
    QMenuBar, QMenu, QFrame, QScrollArea, QSpacerItem, QSizePolicy
)
from PyQt6.QtCore import Qt, pyqtSignal, QObject, QTimer, QSize, QPropertyAnimation, QEasingCurve, QRect
from PyQt6.QtGui import QPixmap, QAction, QFont, QPalette, QColor, QIcon

# Import our modules
from ocr_utils import ocr_manager

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


# Optimized UI Constants
class UIConstants:
    """Elegant UI design constants with proper sizing"""

    # Colors - Refined palette
    PRIMARY_COLOR = "#2563EB"      # Modern blue
    SECONDARY_COLOR = "#F59E0B"    # Amber
    SUCCESS_COLOR = "#10B981"      # Emerald
    WARNING_COLOR = "#F59E0B"      # Amber
    ERROR_COLOR = "#EF4444"        # Red
    BACKGROUND_COLOR = "#F8FAFC"   # Slate 50
    SURFACE_COLOR = "#FFFFFF"      # White
    TEXT_PRIMARY = "#1E293B"       # Slate 800
    TEXT_SECONDARY = "#64748B"     # Slate 500
    BORDER_COLOR = "#E2E8F0"       # Slate 200
    ACCENT_COLOR = "#06B6D4"       # Cyan

    # Typography - Optimized sizes
    FONT_FAMILY = "Segoe UI, system-ui, sans-serif"
    FONT_SIZE_LARGE = 18
    FONT_SIZE_MEDIUM = 14
    FONT_SIZE_SMALL = 12
    FONT_SIZE_TINY = 11

    # Spacing - Consistent system
    MARGIN_LARGE = 20
    MARGIN_MEDIUM = 12
    MARGIN_SMALL = 6
    PADDING_LARGE = 16
    PADDING_MEDIUM = 12
    PADDING_SMALL = 8

    # Component sizes - Optimized
    BUTTON_HEIGHT = 36
    INPUT_HEIGHT = 32
    ICON_SIZE = 20

    # Border radius
    BORDER_RADIUS = 6


def get_elegant_stylesheet():
    """Get elegant, optimized stylesheet with smooth animations"""
    return f"""
    /* Main Application */
    QMainWindow {{
        background-color: {UIConstants.BACKGROUND_COLOR};
        color: {UIConstants.TEXT_PRIMARY};
        font-family: {UIConstants.FONT_FAMILY};
        font-size: {UIConstants.FONT_SIZE_MEDIUM}px;
    }}

    /* Tab Widget - Simplified */
    QTabWidget::pane {{
        border: 1px solid {UIConstants.BORDER_COLOR};
        background-color: {UIConstants.SURFACE_COLOR};
        border-radius: {UIConstants.BORDER_RADIUS}px;
        margin-top: 1px;
    }}

    QTabBar::tab {{
        background-color: transparent;
        color: {UIConstants.TEXT_SECONDARY};
        padding: {UIConstants.PADDING_SMALL}px {UIConstants.PADDING_MEDIUM}px;
        margin-right: 2px;
        border-radius: {UIConstants.BORDER_RADIUS}px;
        font-weight: 500;
        min-width: 100px;
        min-height: {UIConstants.BUTTON_HEIGHT - 8}px;
    }}

    QTabBar::tab:selected {{
        background-color: {UIConstants.PRIMARY_COLOR};
        color: white;
        font-weight: 600;
    }}

    QTabBar::tab:hover:!selected {{
        background-color: {UIConstants.BORDER_COLOR};
        color: {UIConstants.TEXT_PRIMARY};
    }}

    /* Group Boxes - Clean */
    QGroupBox {{
        font-weight: 600;
        font-size: {UIConstants.FONT_SIZE_MEDIUM}px;
        color: {UIConstants.TEXT_PRIMARY};
        border: 1px solid {UIConstants.BORDER_COLOR};
        border-radius: {UIConstants.BORDER_RADIUS}px;
        margin-top: {UIConstants.MARGIN_SMALL}px;
        padding-top: {UIConstants.PADDING_MEDIUM}px;
        background-color: {UIConstants.SURFACE_COLOR};
    }}

    QGroupBox::title {{
        subcontrol-origin: margin;
        left: {UIConstants.PADDING_SMALL}px;
        padding: 0 {UIConstants.PADDING_SMALL}px;
        background-color: {UIConstants.SURFACE_COLOR};
        color: {UIConstants.PRIMARY_COLOR};
    }}

    /* Buttons - Elegant */
    QPushButton {{
        background-color: {UIConstants.PRIMARY_COLOR};
        color: white;
        border: none;
        border-radius: {UIConstants.BORDER_RADIUS}px;
        padding: {UIConstants.PADDING_SMALL}px {UIConstants.PADDING_MEDIUM}px;
        font-weight: 500;
        font-size: {UIConstants.FONT_SIZE_MEDIUM}px;
        min-height: {UIConstants.BUTTON_HEIGHT}px;
        min-width: 80px;
    }}

    QPushButton:hover {{
        background-color: #1D4ED8;
    }}

    QPushButton:pressed {{
        background-color: #1E40AF;
    }}

    QPushButton:disabled {{
        background-color: {UIConstants.BORDER_COLOR};
        color: {UIConstants.TEXT_SECONDARY};
    }}

    /* Input Fields - Refined */
    QLineEdit {{
        border: 1px solid {UIConstants.BORDER_COLOR};
        border-radius: {UIConstants.BORDER_RADIUS}px;
        padding: {UIConstants.PADDING_SMALL}px {UIConstants.PADDING_SMALL}px;
        font-size: {UIConstants.FONT_SIZE_MEDIUM}px;
        background-color: {UIConstants.SURFACE_COLOR};
        min-height: {UIConstants.INPUT_HEIGHT}px;
        color: {UIConstants.TEXT_PRIMARY};
    }}

    QLineEdit:focus {{
        border-color: {UIConstants.PRIMARY_COLOR};
        border-width: 2px;
    }}

    /* Combo Boxes - Simplified */
    QComboBox {{
        border: 1px solid {UIConstants.BORDER_COLOR};
        border-radius: {UIConstants.BORDER_RADIUS}px;
        padding: {UIConstants.PADDING_SMALL}px;
        font-size: {UIConstants.FONT_SIZE_MEDIUM}px;
        background-color: {UIConstants.SURFACE_COLOR};
        min-height: {UIConstants.INPUT_HEIGHT}px;
        color: {UIConstants.TEXT_PRIMARY};
    }}

    QComboBox:focus {{
        border-color: {UIConstants.PRIMARY_COLOR};
        border-width: 2px;
    }}

    QComboBox::drop-down {{
        border: none;
        width: 24px;
    }}

    QComboBox::down-arrow {{
        image: none;
        border-left: 4px solid transparent;
        border-right: 4px solid transparent;
        border-top: 4px solid {UIConstants.TEXT_SECONDARY};
        margin-right: 6px;
    }}

    /* Text Areas - Clean */
    QTextEdit {{
        border: 1px solid {UIConstants.BORDER_COLOR};
        border-radius: {UIConstants.BORDER_RADIUS}px;
        background-color: {UIConstants.SURFACE_COLOR};
        font-family: 'Consolas', 'Monaco', monospace;
        font-size: {UIConstants.FONT_SIZE_SMALL}px;
        padding: {UIConstants.PADDING_SMALL}px;
        color: {UIConstants.TEXT_PRIMARY};
        line-height: 1.4;
    }}

    QTextEdit:focus {{
        border-color: {UIConstants.PRIMARY_COLOR};
        border-width: 2px;
    }}

    /* Progress Bars - Minimal */
    QProgressBar {{
        border: 1px solid {UIConstants.BORDER_COLOR};
        border-radius: {UIConstants.BORDER_RADIUS}px;
        text-align: center;
        font-weight: 500;
        background-color: {UIConstants.BACKGROUND_COLOR};
        color: {UIConstants.TEXT_PRIMARY};
        min-height: 20px;
    }}

    QProgressBar::chunk {{
        background-color: {UIConstants.PRIMARY_COLOR};
        border-radius: {UIConstants.BORDER_RADIUS - 1}px;
        margin: 1px;
    }}

    /* Labels - Simple */
    QLabel {{
        color: {UIConstants.TEXT_PRIMARY};
        font-size: {UIConstants.FONT_SIZE_MEDIUM}px;
    }}

    /* Checkboxes - Clean */
    QCheckBox {{
        color: {UIConstants.TEXT_PRIMARY};
        font-size: {UIConstants.FONT_SIZE_MEDIUM}px;
        spacing: {UIConstants.PADDING_SMALL}px;
    }}

    QCheckBox::indicator {{
        width: 16px;
        height: 16px;
        border: 1px solid {UIConstants.BORDER_COLOR};
        border-radius: 3px;
        background-color: {UIConstants.SURFACE_COLOR};
    }}

    QCheckBox::indicator:checked {{
        background-color: {UIConstants.PRIMARY_COLOR};
        border-color: {UIConstants.PRIMARY_COLOR};
    }}

    QCheckBox::indicator:hover {{
        border-color: {UIConstants.PRIMARY_COLOR};
    }}
    """


class MessageLogger(QObject):
    """Centralized message logger that emits signals for GUI updates"""
    
    message_logged = pyqtSignal(str, str)  # message, level
    
    def __init__(self):
        super().__init__()
        self._enabled = True
    
    def log(self, message: str, level: str = "INFO"):
        """Log a message with timestamp and level"""
        if not self._enabled:
            return
            
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {level}: {message}"
        
        self.message_logged.emit(formatted_message, level)
        
        # Also log to Python logger
        if level == "ERROR":
            logger.error(message)
        elif level == "WARNING":
            logger.warning(message)
        else:
            logger.info(message)
    
    def info(self, message: str):
        self.log(message, "INFO")
    
    def warning(self, message: str):
        self.log(message, "WARNING")
    
    def error(self, message: str):
        self.log(message, "ERROR")
    
    def status(self, message: str):
        self.log(message, "STATUS")
    
    def success(self, message: str):
        self.log(message, "SUCCESS")


# Global message logger instance
message_logger = MessageLogger()


class ModernMessageLoggerWindow(QMainWindow):
    """Modern message logger window with clean, professional design"""

    def __init__(self, message_logger: MessageLogger):
        super().__init__()
        self.message_logger = message_logger
        self.max_messages = 1000
        self.all_messages = []
        self.init_ui()
        self.connect_signals()
        self.apply_styling()

    def init_ui(self):
        """Initialize modern logger window UI"""
        self.setWindowTitle("MFFUHijack - Message Logger")
        self.setMinimumSize(1000, 700)
        self.resize(1000, 700)

        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Main layout
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(UIConstants.MARGIN_LARGE, UIConstants.MARGIN_LARGE,
                                     UIConstants.MARGIN_LARGE, UIConstants.MARGIN_LARGE)
        main_layout.setSpacing(UIConstants.MARGIN_MEDIUM)

        # Header
        header = self.create_header()
        main_layout.addWidget(header)

        # Controls
        controls = self.create_controls()
        main_layout.addWidget(controls)

        # Message display
        display = self.create_display()
        main_layout.addWidget(display, 1)

        # Status bar
        status = self.create_status_bar()
        main_layout.addWidget(status)

    def create_header(self):
        """Create header section"""
        header_widget = QWidget()
        header_layout = QVBoxLayout(header_widget)
        header_layout.setContentsMargins(0, 0, 0, 0)
        header_layout.setSpacing(UIConstants.MARGIN_SMALL)

        # Title
        title = QLabel("📋 Message Logger")
        title.setStyleSheet(f"""
            QLabel {{
                font-size: {UIConstants.FONT_SIZE_LARGE + 6}px;
                font-weight: 700;
                color: {UIConstants.PRIMARY_COLOR};
                padding: {UIConstants.PADDING_MEDIUM}px 0;
            }}
        """)
        header_layout.addWidget(title)

        # Subtitle
        subtitle = QLabel("Real-time application messages, warnings, and status updates")
        subtitle.setStyleSheet(f"""
            QLabel {{
                font-size: {UIConstants.FONT_SIZE_MEDIUM}px;
                color: {UIConstants.TEXT_SECONDARY};
                margin-bottom: {UIConstants.MARGIN_SMALL}px;
            }}
        """)
        header_layout.addWidget(subtitle)

        return header_widget

    def create_controls(self):
        """Create controls section"""
        controls_group = QGroupBox("🎛️ Controls & Filters")
        controls_layout = QVBoxLayout(controls_group)
        controls_layout.setSpacing(UIConstants.MARGIN_MEDIUM)

        # Action buttons
        actions_layout = QHBoxLayout()
        actions_layout.setSpacing(UIConstants.MARGIN_MEDIUM)

        # Clear button
        self.clear_button = QPushButton("🗑️ Clear Messages")
        self.clear_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {UIConstants.ERROR_COLOR};
                min-width: 140px;
            }}
            QPushButton:hover {{
                background-color: #D32F2F;
            }}
        """)
        self.clear_button.clicked.connect(self.clear_messages)
        actions_layout.addWidget(self.clear_button)

        # Auto-scroll
        self.auto_scroll_checkbox = QCheckBox("📜 Auto-scroll to latest")
        self.auto_scroll_checkbox.setChecked(True)
        actions_layout.addWidget(self.auto_scroll_checkbox)

        actions_layout.addStretch()
        controls_layout.addLayout(actions_layout)

        # Filters
        filters_layout = QHBoxLayout()
        filters_layout.setSpacing(UIConstants.MARGIN_LARGE)

        filter_label = QLabel("Show message types:")
        filter_label.setStyleSheet(f"font-weight: 600; color: {UIConstants.TEXT_PRIMARY};")
        filters_layout.addWidget(filter_label)

        # Filter checkboxes
        self.filter_checkboxes = {}
        filter_configs = [
            ("INFO", "ℹ️", UIConstants.TEXT_PRIMARY),
            ("STATUS", "🔄", UIConstants.PRIMARY_COLOR),
            ("SUCCESS", "✅", UIConstants.SUCCESS_COLOR),
            ("WARNING", "⚠️", UIConstants.WARNING_COLOR),
            ("ERROR", "❌", UIConstants.ERROR_COLOR)
        ]

        for level, icon, color in filter_configs:
            checkbox = QCheckBox(f"{icon} {level}")
            checkbox.setChecked(True)
            checkbox.setStyleSheet(f"""
                QCheckBox {{
                    font-weight: 500;
                    color: {color};
                }}
            """)
            checkbox.stateChanged.connect(self.apply_filters)
            self.filter_checkboxes[level] = checkbox
            filters_layout.addWidget(checkbox)

        filters_layout.addStretch()
        controls_layout.addLayout(filters_layout)

        return controls_group

    def create_display(self):
        """Create message display section"""
        display_group = QGroupBox("💬 Messages")
        display_layout = QVBoxLayout(display_group)
        display_layout.setContentsMargins(UIConstants.PADDING_MEDIUM, UIConstants.PADDING_LARGE,
                                        UIConstants.PADDING_MEDIUM, UIConstants.PADDING_MEDIUM)

        # Message display
        self.message_display = QTextEdit()
        self.message_display.setReadOnly(True)
        self.message_display.setStyleSheet(f"""
            QTextEdit {{
                background-color: #1E1E1E;
                color: #FFFFFF;
                font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
                font-size: {UIConstants.FONT_SIZE_SMALL + 1}px;
                line-height: 1.5;
                border: 1px solid {UIConstants.BORDER_COLOR};
                border-radius: {UIConstants.BORDER_RADIUS}px;
                padding: {UIConstants.PADDING_MEDIUM}px;
            }}
        """)

        self.message_display.setPlaceholderText("Messages will appear here as the application runs...")
        display_layout.addWidget(self.message_display)

        return display_group

    def create_status_bar(self):
        """Create status bar"""
        status_widget = QWidget()
        status_layout = QHBoxLayout(status_widget)
        status_layout.setContentsMargins(0, 0, 0, 0)

        # Message count
        self.message_count_label = QLabel("Messages: 0")
        self.message_count_label.setStyleSheet(f"""
            QLabel {{
                color: {UIConstants.TEXT_SECONDARY};
                font-size: {UIConstants.FONT_SIZE_SMALL}px;
                padding: {UIConstants.PADDING_SMALL}px;
            }}
        """)
        status_layout.addWidget(self.message_count_label)

        status_layout.addStretch()

        # Connection status
        self.connection_status_label = QLabel("🟢 Connected")
        self.connection_status_label.setStyleSheet(f"""
            QLabel {{
                color: {UIConstants.SUCCESS_COLOR};
                font-size: {UIConstants.FONT_SIZE_SMALL}px;
                font-weight: 500;
                padding: {UIConstants.PADDING_SMALL}px;
            }}
        """)
        status_layout.addWidget(self.connection_status_label)

        return status_widget

    def apply_styling(self):
        """Apply modern styling"""
        self.setStyleSheet(get_modern_stylesheet())

    def connect_signals(self):
        """Connect signals"""
        self.message_logger.message_logged.connect(self.add_message)

    def add_message(self, message: str, level: str):
        """Add message with modern formatting"""
        self.all_messages.append((message, level))

        if len(self.all_messages) > self.max_messages:
            self.all_messages = self.all_messages[-self.max_messages:]

        self.update_status()
        self.apply_filters()

    def apply_filters(self):
        """Apply filters with modern styling"""
        show_levels = set()
        for level, checkbox in self.filter_checkboxes.items():
            if checkbox.isChecked():
                show_levels.add(level)

        formatted_messages = []
        for message, level in self.all_messages:
            if level in show_levels:
                color_map = {
                    "ERROR": "#FF6B6B",
                    "WARNING": "#FFB347",
                    "SUCCESS": "#51CF66",
                    "STATUS": "#74C0FC",
                    "INFO": "#E9ECEF"
                }

                color = color_map.get(level, "#E9ECEF")

                formatted_message = f'''
                <div style="
                    margin: 6px 0;
                    padding: 12px 16px;
                    background-color: rgba(255,255,255,0.08);
                    border-left: 4px solid {color};
                    border-radius: 6px;
                    font-family: 'Consolas', monospace;
                ">
                    <span style="color: {color}; font-weight: 700; font-size: 11px;">[{level}]</span>
                    <span style="color: #E9ECEF; margin-left: 12px; font-size: 13px;">{message.split('] ', 1)[-1] if '] ' in message else message}</span>
                </div>
                '''
                formatted_messages.append(formatted_message)

        if formatted_messages:
            html_content = f'''
            <div style="
                background-color: #1E1E1E;
                color: #E9ECEF;
                font-family: 'Consolas', monospace;
                line-height: 1.5;
                padding: 12px;
            ">
                {"".join(formatted_messages)}
            </div>
            '''
            self.message_display.setHtml(html_content)
        else:
            self.message_display.clear()

        if self.auto_scroll_checkbox.isChecked():
            scrollbar = self.message_display.verticalScrollBar()
            scrollbar.setValue(scrollbar.maximum())

    def update_status(self):
        """Update status information"""
        total_messages = len(self.all_messages)
        self.message_count_label.setText(f"Messages: {total_messages}")

        if total_messages > 0:
            recent_messages = self.all_messages[-5:] if len(self.all_messages) >= 5 else self.all_messages
            has_errors = any(level == "ERROR" for _, level in recent_messages)

            if has_errors:
                self.connection_status_label.setText("🔴 Issues Detected")
                self.connection_status_label.setStyleSheet(f"""
                    QLabel {{
                        color: {UIConstants.ERROR_COLOR};
                        font-size: {UIConstants.FONT_SIZE_SMALL}px;
                        font-weight: 500;
                        padding: {UIConstants.PADDING_SMALL}px;
                    }}
                """)
            else:
                self.connection_status_label.setText("🟢 Running Normally")
                self.connection_status_label.setStyleSheet(f"""
                    QLabel {{
                        color: {UIConstants.SUCCESS_COLOR};
                        font-size: {UIConstants.FONT_SIZE_SMALL}px;
                        font-weight: 500;
                        padding: {UIConstants.PADDING_SMALL}px;
                    }}
                """)

    def clear_messages(self):
        """Clear all messages"""
        self.all_messages.clear()
        self.message_display.clear()
        self.update_status()

        self.message_display.setHtml(f'''
        <div style="
            text-align: center;
            color: {UIConstants.TEXT_SECONDARY};
            font-style: italic;
            margin-top: 100px;
            font-size: 14px;
        ">
            ✨ Messages cleared. New messages will appear here...
        </div>
        ''')


class ModernLivestreamTab(QWidget):
    """Modern Livestream Bot Interface with clean design"""

    # Signals
    start_bot_signal = pyqtSignal(str, str, float)
    stop_bot_signal = pyqtSignal()

    def __init__(self):
        super().__init__()
        self.ocr_region = (0, 67, 100, 33)
        self.init_ui()
        self.apply_styling()

    def init_ui(self):
        """Initialize modern livestream tab UI"""
        # Main layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(UIConstants.MARGIN_LARGE, UIConstants.MARGIN_LARGE,
                                     UIConstants.MARGIN_LARGE, UIConstants.MARGIN_LARGE)
        main_layout.setSpacing(UIConstants.MARGIN_MEDIUM)

        # Configuration section
        config_section = self.create_configuration_section()
        main_layout.addWidget(config_section)

        # Control section
        control_section = self.create_control_section()
        main_layout.addWidget(control_section)

        # Preview section
        preview_section = self.create_preview_section()
        main_layout.addWidget(preview_section, 1)

        # Activity section
        activity_section = self.create_activity_section()
        main_layout.addWidget(activity_section)

    def create_configuration_section(self):
        """Create configuration section"""
        config_group = QGroupBox("🔧 Bot Configuration")
        config_layout = QGridLayout(config_group)
        config_layout.setSpacing(UIConstants.MARGIN_MEDIUM)
        config_layout.setContentsMargins(UIConstants.PADDING_LARGE, UIConstants.PADDING_LARGE,
                                       UIConstants.PADDING_LARGE, UIConstants.PADDING_LARGE)

        # Row 1: Livestream URL
        url_label = QLabel("📺 Livestream URL:")
        url_label.setStyleSheet(f"font-weight: 600; color: {UIConstants.TEXT_PRIMARY};")
        config_layout.addWidget(url_label, 0, 0)

        self.url_input = QLineEdit()
        self.url_input.setPlaceholderText("https://www.youtube.com/watch?v=... or channel URL")
        self.url_input.setText("https://www.youtube.com/@MyFundedFuturesPropFirm/streams")
        config_layout.addWidget(self.url_input, 0, 1, 1, 3)

        # Row 2: OCR Engine and Interval
        ocr_label = QLabel("🔍 OCR Engine:")
        ocr_label.setStyleSheet(f"font-weight: 600; color: {UIConstants.TEXT_PRIMARY};")
        config_layout.addWidget(ocr_label, 1, 0)

        self.ocr_combo = QComboBox()
        self.ocr_combo.addItems(["EasyOCR", "Custom"])
        config_layout.addWidget(self.ocr_combo, 1, 1)

        interval_label = QLabel("⏱️ Interval (s):")
        interval_label.setStyleSheet(f"font-weight: 600; color: {UIConstants.TEXT_PRIMARY};")
        config_layout.addWidget(interval_label, 1, 2)

        self.interval_input = QLineEdit("1.0")
        self.interval_input.setMaximumWidth(100)
        config_layout.addWidget(self.interval_input, 1, 3)

        # Row 3: Pattern Configuration
        pattern_label = QLabel("🎯 Detection Patterns:")
        pattern_label.setStyleSheet(f"font-weight: 600; color: {UIConstants.TEXT_PRIMARY};")
        config_layout.addWidget(pattern_label, 2, 0)

        self.pattern_button = QPushButton("⚙️ Configure Patterns")
        self.pattern_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {UIConstants.SECONDARY_COLOR};
                min-width: 160px;
            }}
            QPushButton:hover {{
                background-color: #F57C00;
            }}
        """)
        self.pattern_button.clicked.connect(self.configure_patterns)
        config_layout.addWidget(self.pattern_button, 2, 1)

        # Account types display
        self.account_types_label = QLabel("Account Types: Loading...")
        self.account_types_label.setStyleSheet(f"""
            QLabel {{
                color: {UIConstants.TEXT_SECONDARY};
                font-size: {UIConstants.FONT_SIZE_SMALL}px;
                padding: {UIConstants.PADDING_SMALL}px;
                background-color: {UIConstants.BACKGROUND_COLOR};
                border-radius: {UIConstants.BORDER_RADIUS}px;
                border: 1px solid {UIConstants.BORDER_COLOR};
            }}
        """)
        self.account_types_label.setWordWrap(True)
        config_layout.addWidget(self.account_types_label, 2, 2, 1, 2)

        # Row 4: OCR Region
        region_label = QLabel("📐 OCR Region:")
        region_label.setStyleSheet(f"font-weight: 600; color: {UIConstants.TEXT_PRIMARY};")
        config_layout.addWidget(region_label, 3, 0)

        self.region_button = QPushButton("🎯 Select OCR Region")
        self.region_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {UIConstants.SUCCESS_COLOR};
                min-width: 160px;
            }}
            QPushButton:hover {{
                background-color: #388E3C;
            }}
        """)
        self.region_button.clicked.connect(self.open_region_selector)
        config_layout.addWidget(self.region_button, 3, 1)

        # Region display
        self.region_label = QLabel("Region: Bottom third (0%, 67%, 100% × 33%)")
        self.region_label.setStyleSheet(f"""
            QLabel {{
                color: {UIConstants.TEXT_SECONDARY};
                font-size: {UIConstants.FONT_SIZE_SMALL}px;
                padding: {UIConstants.PADDING_SMALL}px;
                background-color: {UIConstants.BACKGROUND_COLOR};
                border-radius: {UIConstants.BORDER_RADIUS}px;
                border: 1px solid {UIConstants.BORDER_COLOR};
            }}
        """)
        config_layout.addWidget(self.region_label, 3, 2, 1, 2)

        # Update displays
        self.update_account_types_display()

        return config_group

    def create_control_section(self):
        """Create bot control section"""
        control_group = QGroupBox("🎮 Bot Control")
        control_layout = QHBoxLayout(control_group)
        control_layout.setSpacing(UIConstants.MARGIN_LARGE)
        control_layout.setContentsMargins(UIConstants.PADDING_LARGE, UIConstants.PADDING_LARGE,
                                        UIConstants.PADDING_LARGE, UIConstants.PADDING_LARGE)

        # Start button
        self.start_button = QPushButton("▶️ Start Bot")
        self.start_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {UIConstants.SUCCESS_COLOR};
                min-height: {UIConstants.BUTTON_HEIGHT + 8}px;
                min-width: 160px;
                font-size: {UIConstants.FONT_SIZE_MEDIUM + 1}px;
                font-weight: 700;
            }}
            QPushButton:hover {{
                background-color: #388E3C;
            }}
        """)
        self.start_button.clicked.connect(self.start_bot)
        control_layout.addWidget(self.start_button)

        # Stop button
        self.stop_button = QPushButton("⏹️ Stop Bot")
        self.stop_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {UIConstants.ERROR_COLOR};
                min-height: {UIConstants.BUTTON_HEIGHT + 8}px;
                min-width: 160px;
                font-size: {UIConstants.FONT_SIZE_MEDIUM + 1}px;
                font-weight: 700;
            }}
            QPushButton:hover {{
                background-color: #D32F2F;
            }}
            QPushButton:disabled {{
                background-color: {UIConstants.BORDER_COLOR};
                color: {UIConstants.TEXT_SECONDARY};
            }}
        """)
        self.stop_button.setEnabled(False)
        self.stop_button.clicked.connect(self.stop_bot)
        control_layout.addWidget(self.stop_button)

        # Status indicator
        self.status_label = QLabel("🔴 Status: Idle")
        self.status_label.setStyleSheet(f"""
            QLabel {{
                font-weight: 600;
                font-size: {UIConstants.FONT_SIZE_MEDIUM + 1}px;
                color: {UIConstants.TEXT_PRIMARY};
                padding: {UIConstants.PADDING_MEDIUM}px {UIConstants.PADDING_LARGE}px;
                background-color: {UIConstants.BACKGROUND_COLOR};
                border-radius: {UIConstants.BORDER_RADIUS}px;
                border: 2px solid {UIConstants.BORDER_COLOR};
                min-height: {UIConstants.BUTTON_HEIGHT}px;
            }}
        """)
        control_layout.addWidget(self.status_label)

        control_layout.addStretch()

        return control_group

    def create_preview_section(self):
        """Create live preview section"""
        preview_group = QGroupBox("📺 Live Preview")
        preview_layout = QVBoxLayout(preview_group)
        preview_layout.setContentsMargins(UIConstants.PADDING_LARGE, UIConstants.PADDING_LARGE,
                                        UIConstants.PADDING_LARGE, UIConstants.PADDING_LARGE)

        # Frame preview
        self.frame_preview = QLabel("🎬 No frame loaded - Start the bot to see live preview")
        self.frame_preview.setMinimumSize(600, 340)
        self.frame_preview.setStyleSheet(f"""
            QLabel {{
                border: 3px dashed {UIConstants.BORDER_COLOR};
                background-color: {UIConstants.BACKGROUND_COLOR};
                border-radius: {UIConstants.BORDER_RADIUS + 2}px;
                color: {UIConstants.TEXT_SECONDARY};
                font-size: {UIConstants.FONT_SIZE_MEDIUM + 1}px;
                font-weight: 500;
            }}
        """)
        self.frame_preview.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.frame_preview.setScaledContents(True)
        preview_layout.addWidget(self.frame_preview)

        return preview_group

    def create_activity_section(self):
        """Create activity log section"""
        activity_group = QGroupBox("📋 Activity Log")
        activity_layout = QVBoxLayout(activity_group)
        activity_layout.setContentsMargins(UIConstants.PADDING_LARGE, UIConstants.PADDING_LARGE,
                                         UIConstants.PADDING_LARGE, UIConstants.PADDING_LARGE)

        # Activity log
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(140)
        self.log_text.setReadOnly(True)
        self.log_text.setStyleSheet(f"""
            QTextEdit {{
                background-color: #F8F9FA;
                border: 2px solid {UIConstants.BORDER_COLOR};
                border-radius: {UIConstants.BORDER_RADIUS}px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: {UIConstants.FONT_SIZE_SMALL}px;
                padding: {UIConstants.PADDING_MEDIUM}px;
                color: {UIConstants.TEXT_PRIMARY};
                line-height: 1.4;
            }}
        """)
        self.log_text.setPlaceholderText("Activity messages will appear here...")
        activity_layout.addWidget(self.log_text)

        return activity_group

    def apply_styling(self):
        """Apply modern styling"""
        self.setStyleSheet(get_modern_stylesheet())

    # Methods for functionality
    def update_account_types_display(self):
        """Update account types display"""
        try:
            enabled_types = ocr_manager.get_enabled_account_types()
            if enabled_types:
                types_text = ", ".join(enabled_types)
                self.account_types_label.setText(f"✅ Enabled: {types_text}")
            else:
                self.account_types_label.setText("⚠️ No account types configured")
        except Exception as e:
            self.account_types_label.setText("❌ Error loading account types")

    def configure_patterns(self):
        """Configure detection patterns"""
        try:
            from pattern_config_dialog import show_pattern_config_dialog
            new_patterns = show_pattern_config_dialog(self)
            if new_patterns:
                ocr_manager.update_pattern_config(new_patterns)
                self.update_account_types_display()
                self.log_message("✅ Pattern configuration updated successfully")
        except Exception as e:
            self.log_message(f"❌ Error configuring patterns: {str(e)}")

    def open_region_selector(self):
        """Open OCR region selector"""
        try:
            from region_selector import RegionSelectorWidget
            from PyQt6.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QPushButton

            dialog = QDialog(self)
            dialog.setWindowTitle("Select OCR Region")
            dialog.setModal(True)
            dialog.resize(800, 600)

            layout = QVBoxLayout(dialog)

            # Create region selector
            region_selector = RegionSelectorWidget()
            region_selector.set_region_percent(*self.ocr_region)

            # Load sample frame
            sample_frame = self.create_sample_livestream_frame()
            region_selector.set_frame(sample_frame)

            layout.addWidget(region_selector)

            # Buttons
            button_layout = QHBoxLayout()

            apply_button = QPushButton("Apply Region")
            apply_button.clicked.connect(lambda: self.apply_live_region(region_selector, dialog))
            button_layout.addWidget(apply_button)

            cancel_button = QPushButton("Cancel")
            cancel_button.clicked.connect(dialog.reject)
            button_layout.addWidget(cancel_button)

            layout.addLayout(button_layout)

            dialog.exec()

        except Exception as e:
            self.log_message(f"❌ Error opening region selector: {str(e)}")

    def apply_live_region(self, region_selector, dialog):
        """Apply selected region"""
        self.ocr_region = region_selector.get_region_percent()
        x, y, w, h = self.ocr_region
        self.region_label.setText(f"Region: {x:.1f}%, {y:.1f}%, {w:.1f}% × {h:.1f}%")
        self.log_message(f"✅ OCR region updated: {x:.1f}%, {y:.1f}%, {w:.1f}% × {h:.1f}%")
        dialog.accept()

    def create_sample_livestream_frame(self):
        """Create sample frame for region selection"""
        import numpy as np
        import cv2

        # Create realistic livestream frame
        sample_frame = np.ones((720, 1280, 3), dtype=np.uint8) * 240

        # Add black bars
        sample_frame[:, :160] = 0
        sample_frame[:, 1120:] = 0

        # Add sample text areas
        cv2.putText(sample_frame, "SAMPLE LIVESTREAM", (400, 100),
                   cv2.FONT_HERSHEY_SIMPLEX, 1.5, (255, 255, 255), 2)
        cv2.putText(sample_frame, "x5 FREE RESETS USE CODE: RESET3J", (300, 600),
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)

        return sample_frame

    def start_bot(self):
        """Start the livestream bot"""
        url = self.url_input.text().strip()
        if not url:
            self.log_message("❌ ERROR: Please enter a livestream URL")
            return

        ocr_engine = self.ocr_combo.currentText()
        try:
            interval = float(self.interval_input.text())
        except ValueError:
            interval = 1.0
            self.interval_input.setText("1.0")

        # Update UI state
        self.start_button.setEnabled(False)
        self.stop_button.setEnabled(True)
        self.status_label.setText("🟡 Status: Starting...")
        self.status_label.setStyleSheet(f"""
            QLabel {{
                font-weight: 600;
                font-size: {UIConstants.FONT_SIZE_MEDIUM + 1}px;
                color: {UIConstants.WARNING_COLOR};
                padding: {UIConstants.PADDING_MEDIUM}px {UIConstants.PADDING_LARGE}px;
                background-color: {UIConstants.BACKGROUND_COLOR};
                border-radius: {UIConstants.BORDER_RADIUS}px;
                border: 2px solid {UIConstants.WARNING_COLOR};
                min-height: {UIConstants.BUTTON_HEIGHT}px;
            }}
        """)

        # Emit signal
        self.start_bot_signal.emit(url, ocr_engine, interval)

        self.log_message(f"🚀 Starting bot with URL: {url}")
        self.log_message(f"🔍 OCR Engine: {ocr_engine}, Interval: {interval}s")

    def stop_bot(self):
        """Stop the livestream bot"""
        # Update UI state
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.status_label.setText("🔴 Status: Stopping...")

        # Emit signal
        self.stop_bot_signal.emit()

        self.log_message("⏹️ Stopping bot...")

    def update_status(self, status: str):
        """Update status label"""
        if "running" in status.lower():
            color = UIConstants.SUCCESS_COLOR
            icon = "🟢"
        elif "error" in status.lower():
            color = UIConstants.ERROR_COLOR
            icon = "🔴"
        else:
            color = UIConstants.TEXT_SECONDARY
            icon = "🔴"

        self.status_label.setText(f"{icon} Status: {status}")
        self.status_label.setStyleSheet(f"""
            QLabel {{
                font-weight: 600;
                font-size: {UIConstants.FONT_SIZE_MEDIUM + 1}px;
                color: {color};
                padding: {UIConstants.PADDING_MEDIUM}px {UIConstants.PADDING_LARGE}px;
                background-color: {UIConstants.BACKGROUND_COLOR};
                border-radius: {UIConstants.BORDER_RADIUS}px;
                border: 2px solid {color};
                min-height: {UIConstants.BUTTON_HEIGHT}px;
            }}
        """)

    def update_frame_preview(self, pixmap: QPixmap):
        """Update frame preview"""
        self.frame_preview.setPixmap(pixmap)

    def log_message(self, message: str):
        """Add message to activity log"""
        self.log_text.append(message)
        # Auto-scroll
        scrollbar = self.log_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())

        # Also send to centralized logger
        if "ERROR" in message.upper() or "❌" in message:
            message_logger.error(message)
        elif "WARNING" in message.upper() or "⚠️" in message:
            message_logger.warning(message)
        elif "✅" in message or "DETECTED" in message:
            message_logger.success(message)
        elif "STATUS" in message.upper() or "🚀" in message:
            message_logger.status(message)
        else:
            message_logger.info(message)


class ModernDatasetTab(QWidget):
    """Modern Dataset Building and Training Interface"""

    # Signals
    start_dataset_signal = pyqtSignal(str, int, str)
    start_training_signal = pyqtSignal()

    def __init__(self):
        super().__init__()
        self.init_ui()
        self.apply_styling()

    def init_ui(self):
        """Initialize modern dataset tab UI"""
        # Main layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(UIConstants.MARGIN_LARGE, UIConstants.MARGIN_LARGE,
                                     UIConstants.MARGIN_LARGE, UIConstants.MARGIN_LARGE)
        main_layout.setSpacing(UIConstants.MARGIN_MEDIUM)

        # Dataset building section
        dataset_section = self.create_dataset_section()
        main_layout.addWidget(dataset_section)

        # Training section
        training_section = self.create_training_section()
        main_layout.addWidget(training_section)

        # Progress section
        progress_section = self.create_progress_section()
        main_layout.addWidget(progress_section, 1)

    def create_dataset_section(self):
        """Create dataset building section"""
        dataset_group = QGroupBox("📊 Dataset Building")
        dataset_layout = QGridLayout(dataset_group)
        dataset_layout.setSpacing(UIConstants.MARGIN_MEDIUM)
        dataset_layout.setContentsMargins(UIConstants.PADDING_LARGE, UIConstants.PADDING_LARGE,
                                        UIConstants.PADDING_LARGE, UIConstants.PADDING_LARGE)

        # Channel URL
        url_label = QLabel("📺 YouTube Channel:")
        url_label.setStyleSheet(f"font-weight: 600; color: {UIConstants.TEXT_PRIMARY};")
        dataset_layout.addWidget(url_label, 0, 0)

        self.channel_input = QLineEdit()
        self.channel_input.setPlaceholderText("https://www.youtube.com/@channel or channel URL")
        dataset_layout.addWidget(self.channel_input, 0, 1, 1, 2)

        # Number of frames
        frames_label = QLabel("🖼️ Target Frames:")
        frames_label.setStyleSheet(f"font-weight: 600; color: {UIConstants.TEXT_PRIMARY};")
        dataset_layout.addWidget(frames_label, 1, 0)

        self.frames_input = QLineEdit("100")
        self.frames_input.setMaximumWidth(100)
        dataset_layout.addWidget(self.frames_input, 1, 1)

        # Region selection
        region_label = QLabel("📐 OCR Region:")
        region_label.setStyleSheet(f"font-weight: 600; color: {UIConstants.TEXT_PRIMARY};")
        dataset_layout.addWidget(region_label, 1, 2)

        self.region_combo = QComboBox()
        self.region_combo.addItems(["Full Frame", "Bottom Third", "Custom"])
        self.region_combo.setCurrentText("Bottom Third")
        dataset_layout.addWidget(self.region_combo, 1, 3)

        # Start dataset building button
        self.start_dataset_button = QPushButton("🚀 Start Dataset Building")
        self.start_dataset_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {UIConstants.PRIMARY_COLOR};
                min-height: {UIConstants.BUTTON_HEIGHT}px;
                min-width: 180px;
                font-weight: 700;
            }}
            QPushButton:hover {{
                background-color: #1565C0;
            }}
        """)
        self.start_dataset_button.clicked.connect(self.start_dataset_building)
        dataset_layout.addWidget(self.start_dataset_button, 2, 0, 1, 2)

        # Dataset progress
        self.dataset_progress = QProgressBar()
        self.dataset_progress.setVisible(False)
        dataset_layout.addWidget(self.dataset_progress, 2, 2, 1, 2)

        return dataset_group

    def create_training_section(self):
        """Create model training section"""
        training_group = QGroupBox("🧠 Model Training")
        training_layout = QGridLayout(training_group)
        training_layout.setSpacing(UIConstants.MARGIN_MEDIUM)
        training_layout.setContentsMargins(UIConstants.PADDING_LARGE, UIConstants.PADDING_LARGE,
                                         UIConstants.PADDING_LARGE, UIConstants.PADDING_LARGE)

        # Training parameters
        epochs_label = QLabel("🔄 Epochs:")
        epochs_label.setStyleSheet(f"font-weight: 600; color: {UIConstants.TEXT_PRIMARY};")
        training_layout.addWidget(epochs_label, 0, 0)

        self.epochs_input = QLineEdit("10")
        self.epochs_input.setMaximumWidth(80)
        training_layout.addWidget(self.epochs_input, 0, 1)

        batch_label = QLabel("📦 Batch Size:")
        batch_label.setStyleSheet(f"font-weight: 600; color: {UIConstants.TEXT_PRIMARY};")
        training_layout.addWidget(batch_label, 0, 2)

        self.batch_input = QLineEdit("32")
        self.batch_input.setMaximumWidth(80)
        training_layout.addWidget(self.batch_input, 0, 3)

        # Start training button
        self.start_training_button = QPushButton("🎯 Start Training")
        self.start_training_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {UIConstants.SUCCESS_COLOR};
                min-height: {UIConstants.BUTTON_HEIGHT}px;
                min-width: 140px;
                font-weight: 700;
            }}
            QPushButton:hover {{
                background-color: #388E3C;
            }}
        """)
        self.start_training_button.clicked.connect(self.start_training)
        training_layout.addWidget(self.start_training_button, 1, 0, 1, 2)

        # Training progress
        self.training_progress = QProgressBar()
        self.training_progress.setVisible(False)
        training_layout.addWidget(self.training_progress, 1, 2, 1, 2)

        return training_group

    def create_progress_section(self):
        """Create progress log section"""
        progress_group = QGroupBox("📈 Progress Log")
        progress_layout = QVBoxLayout(progress_group)
        progress_layout.setContentsMargins(UIConstants.PADDING_LARGE, UIConstants.PADDING_LARGE,
                                         UIConstants.PADDING_LARGE, UIConstants.PADDING_LARGE)

        # Progress log
        self.progress_log = QTextEdit()
        self.progress_log.setReadOnly(True)
        self.progress_log.setStyleSheet(f"""
            QTextEdit {{
                background-color: #F8F9FA;
                border: 2px solid {UIConstants.BORDER_COLOR};
                border-radius: {UIConstants.BORDER_RADIUS}px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: {UIConstants.FONT_SIZE_SMALL}px;
                padding: {UIConstants.PADDING_MEDIUM}px;
                color: {UIConstants.TEXT_PRIMARY};
                line-height: 1.4;
            }}
        """)
        self.progress_log.setPlaceholderText("Progress messages will appear here...")
        progress_layout.addWidget(self.progress_log)

        return progress_group

    def apply_styling(self):
        """Apply modern styling"""
        self.setStyleSheet(get_modern_stylesheet())

    def start_dataset_building(self):
        """Start dataset building"""
        channel_url = self.channel_input.text().strip()
        if not channel_url:
            self.log_progress("❌ ERROR: Please enter a YouTube channel URL")
            return

        try:
            num_frames = int(self.frames_input.text())
        except ValueError:
            num_frames = 100
            self.frames_input.setText("100")

        region = self.region_combo.currentText()

        # Update UI
        self.start_dataset_button.setEnabled(False)
        self.dataset_progress.setVisible(True)
        self.dataset_progress.setValue(0)

        # Emit signal
        self.start_dataset_signal.emit(channel_url, num_frames, region)

        self.log_progress(f"🚀 Starting dataset building for: {channel_url}")
        self.log_progress(f"📊 Target frames: {num_frames}, Region: {region}")

    def start_training(self):
        """Start model training"""
        try:
            epochs = int(self.epochs_input.text())
            batch_size = int(self.batch_input.text())
        except ValueError:
            epochs = 10
            batch_size = 32
            self.epochs_input.setText("10")
            self.batch_input.setText("32")

        # Update UI
        self.start_training_button.setEnabled(False)
        self.training_progress.setVisible(True)
        self.training_progress.setValue(0)

        # Emit signal
        self.start_training_signal.emit()

        self.log_progress(f"🧠 Starting model training...")
        self.log_progress(f"⚙️ Epochs: {epochs}, Batch size: {batch_size}")

    def reset_ui_state(self):
        """Reset UI to initial state"""
        self.start_dataset_button.setEnabled(True)
        self.start_training_button.setEnabled(True)
        self.dataset_progress.setVisible(False)
        self.training_progress.setVisible(False)

    def update_dataset_progress(self, value: int):
        """Update dataset progress"""
        self.dataset_progress.setValue(value)

    def update_training_progress(self, value: int):
        """Update training progress"""
        self.training_progress.setValue(value)

    def log_progress(self, message: str):
        """Add message to progress log"""
        self.progress_log.append(message)
        # Auto-scroll
        scrollbar = self.progress_log.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())

        # Also send to centralized logger
        if "ERROR" in message.upper() or "❌" in message:
            message_logger.error(message)
        elif "WARNING" in message.upper() or "⚠️" in message:
            message_logger.warning(message)
        elif "✅" in message or "complete" in message.lower():
            message_logger.success(message)
        elif "Starting" in message or "🚀" in message:
            message_logger.status(message)
        else:
            message_logger.info(message)


class ModernMFFUHijackGUI(QMainWindow):
    """Modern main application window with clean, professional design"""

    def __init__(self):
        super().__init__()
        self.message_logger_window = None
        self.live_scan_monitor = None
        self.init_ui()
        self.apply_styling()
        self.connect_signals()

    def init_ui(self):
        """Initialize modern main window UI"""
        self.setWindowTitle("MFFUHijack - Real-Time OCR Livestream Code Detection")
        self.setMinimumSize(1200, 800)
        self.resize(1200, 800)

        # Create menu bar
        self.create_menu_bar()

        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Main layout
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(UIConstants.MARGIN_MEDIUM, UIConstants.MARGIN_MEDIUM,
                                     UIConstants.MARGIN_MEDIUM, UIConstants.MARGIN_MEDIUM)
        main_layout.setSpacing(UIConstants.MARGIN_MEDIUM)

        # Header section
        header = self.create_header()
        main_layout.addWidget(header)

        # Tab widget
        self.tab_widget = QTabWidget()
        self.tab_widget.setStyleSheet(f"""
            QTabWidget::pane {{
                border: 2px solid {UIConstants.BORDER_COLOR};
                background-color: {UIConstants.SURFACE_COLOR};
                border-radius: {UIConstants.BORDER_RADIUS}px;
                margin-top: 4px;
            }}
        """)

        # Create tabs
        self.livestream_tab = ModernLivestreamTab()
        self.dataset_tab = ModernDatasetTab()

        # Add tabs with icons
        self.tab_widget.addTab(self.livestream_tab, "🎯 Livestream Bot")
        self.tab_widget.addTab(self.dataset_tab, "📊 Dataset & Training")

        main_layout.addWidget(self.tab_widget, 1)

        # Create message logger window
        self.message_logger_window = ModernMessageLoggerWindow(message_logger)
        self.message_logger_window.show()
        self.logger_action.setChecked(True)

        # Position logger window
        self.position_logger_window()

        # Log startup
        message_logger.info("MFFUHijack application started with modern interface")
        message_logger.info("Message logger window opened")

    def create_header(self):
        """Create application header"""
        header_widget = QWidget()
        header_layout = QHBoxLayout(header_widget)
        header_layout.setContentsMargins(UIConstants.PADDING_LARGE, UIConstants.PADDING_MEDIUM,
                                       UIConstants.PADDING_LARGE, UIConstants.PADDING_MEDIUM)

        # Title and description
        title_layout = QVBoxLayout()

        title_label = QLabel("🎯 MFFUHijack")
        title_label.setStyleSheet(f"""
            QLabel {{
                font-size: {UIConstants.FONT_SIZE_LARGE + 8}px;
                font-weight: 700;
                color: {UIConstants.PRIMARY_COLOR};
                margin: 0;
            }}
        """)
        title_layout.addWidget(title_label)

        subtitle_label = QLabel("Real-Time OCR Livestream Code Detection & Dataset Building")
        subtitle_label.setStyleSheet(f"""
            QLabel {{
                font-size: {UIConstants.FONT_SIZE_MEDIUM}px;
                color: {UIConstants.TEXT_SECONDARY};
                margin: 0;
            }}
        """)
        title_layout.addWidget(subtitle_label)

        header_layout.addLayout(title_layout)
        header_layout.addStretch()

        # Status indicator
        self.app_status_label = QLabel("🟢 Ready")
        self.app_status_label.setStyleSheet(f"""
            QLabel {{
                font-size: {UIConstants.FONT_SIZE_MEDIUM}px;
                font-weight: 600;
                color: {UIConstants.SUCCESS_COLOR};
                padding: {UIConstants.PADDING_SMALL}px {UIConstants.PADDING_MEDIUM}px;
                background-color: {UIConstants.BACKGROUND_COLOR};
                border-radius: {UIConstants.BORDER_RADIUS}px;
                border: 2px solid {UIConstants.SUCCESS_COLOR};
            }}
        """)
        header_layout.addWidget(self.app_status_label)

        # Style header
        header_widget.setStyleSheet(f"""
            QWidget {{
                background-color: {UIConstants.SURFACE_COLOR};
                border-radius: {UIConstants.BORDER_RADIUS}px;
                border: 1px solid {UIConstants.BORDER_COLOR};
            }}
        """)

        return header_widget

    def create_menu_bar(self):
        """Create modern menu bar"""
        menubar = self.menuBar()
        menubar.setStyleSheet(f"""
            QMenuBar {{
                background-color: {UIConstants.SURFACE_COLOR};
                color: {UIConstants.TEXT_PRIMARY};
                border-bottom: 1px solid {UIConstants.BORDER_COLOR};
                padding: 4px;
            }}
            QMenuBar::item {{
                background-color: transparent;
                padding: 8px 16px;
                border-radius: 4px;
            }}
            QMenuBar::item:selected {{
                background-color: {UIConstants.PRIMARY_COLOR};
                color: white;
            }}
        """)

        # View menu
        view_menu = menubar.addMenu('View')

        # Message Logger action
        logger_action = QAction('📋 Show Message Logger', self)
        logger_action.setCheckable(True)
        logger_action.setChecked(False)
        logger_action.triggered.connect(self.toggle_message_logger)
        view_menu.addAction(logger_action)

        self.logger_action = logger_action

        # Help menu
        help_menu = menubar.addMenu('Help')

        about_action = QAction('ℹ️ About MFFUHijack', self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

    def position_logger_window(self):
        """Position logger window relative to main window"""
        main_geometry = self.geometry()
        logger_x = main_geometry.x() + main_geometry.width() + 20
        logger_y = main_geometry.y()
        self.message_logger_window.setGeometry(logger_x, logger_y, 1000, 700)

    def apply_styling(self):
        """Apply modern styling"""
        self.setStyleSheet(get_modern_stylesheet())

    def connect_signals(self):
        """Connect tab signals"""
        # Create Live Scan Monitor window
        try:
            from live_scan_monitor import LiveScanMonitorWindow
            self.live_scan_monitor = LiveScanMonitorWindow()
        except ImportError:
            message_logger.warning("Live Scan Monitor not available")

        # Livestream tab signals
        self.livestream_tab.start_bot_signal.connect(self.start_livestream_bot)
        self.livestream_tab.stop_bot_signal.connect(self.stop_livestream_bot)

        # Dataset tab signals
        self.dataset_tab.start_dataset_signal.connect(self.start_dataset_builder)
        self.dataset_tab.start_training_signal.connect(self.start_model_training)

    def toggle_message_logger(self):
        """Toggle message logger window"""
        if self.message_logger_window is None:
            return

        if self.message_logger_window.isVisible():
            self.message_logger_window.hide()
            self.logger_action.setChecked(False)
            message_logger.info("Message logger window hidden")
        else:
            self.message_logger_window.show()
            self.message_logger_window.raise_()
            self.message_logger_window.activateWindow()
            self.logger_action.setChecked(True)
            message_logger.info("Message logger window shown")

    def show_about(self):
        """Show about dialog"""
        from PyQt6.QtWidgets import QMessageBox

        about_text = """
        <h2>🎯 MFFUHijack</h2>
        <p><b>Real-Time OCR Livestream Code Detection</b></p>
        <p>Version 2.0 - Modern Interface</p>
        <br>
        <p><b>Features:</b></p>
        <ul>
        <li>🎯 Real-time livestream code detection</li>
        <li>📊 Automated dataset building</li>
        <li>🧠 Custom OCR model training</li>
        <li>📋 Advanced message logging</li>
        <li>🎨 Modern, professional interface</li>
        </ul>
        <br>
        <p><b>Built with:</b> PyQt6, OpenCV, EasyOCR, PyTorch</p>
        """

        msg_box = QMessageBox(self)
        msg_box.setWindowTitle("About MFFUHijack")
        msg_box.setTextFormat(Qt.TextFormat.RichText)
        msg_box.setText(about_text)
        msg_box.setStyleSheet(get_modern_stylesheet())
        msg_box.exec()

    # Placeholder methods for backend integration
    def start_livestream_bot(self, url: str, ocr_engine: str, interval: float):
        """Start livestream bot"""
        message_logger.info(f"Starting livestream bot: {url}")
        self.app_status_label.setText("🟡 Bot Starting...")
        self.app_status_label.setStyleSheet(f"""
            QLabel {{
                font-size: {UIConstants.FONT_SIZE_MEDIUM}px;
                font-weight: 600;
                color: {UIConstants.WARNING_COLOR};
                padding: {UIConstants.PADDING_SMALL}px {UIConstants.PADDING_MEDIUM}px;
                background-color: {UIConstants.BACKGROUND_COLOR};
                border-radius: {UIConstants.BORDER_RADIUS}px;
                border: 2px solid {UIConstants.WARNING_COLOR};
            }}
        """)

    def stop_livestream_bot(self):
        """Stop livestream bot"""
        message_logger.info("Stopping livestream bot")
        self.app_status_label.setText("🟢 Ready")
        self.app_status_label.setStyleSheet(f"""
            QLabel {{
                font-size: {UIConstants.FONT_SIZE_MEDIUM}px;
                font-weight: 600;
                color: {UIConstants.SUCCESS_COLOR};
                padding: {UIConstants.PADDING_SMALL}px {UIConstants.PADDING_MEDIUM}px;
                background-color: {UIConstants.BACKGROUND_COLOR};
                border-radius: {UIConstants.BORDER_RADIUS}px;
                border: 2px solid {UIConstants.SUCCESS_COLOR};
            }}
        """)

    def start_dataset_builder(self, channel_url: str, num_frames: int, region: str):
        """Start dataset builder"""
        message_logger.info(f"Starting dataset builder: {channel_url}")
        self.app_status_label.setText("📊 Building Dataset...")
        self.app_status_label.setStyleSheet(f"""
            QLabel {{
                font-size: {UIConstants.FONT_SIZE_MEDIUM}px;
                font-weight: 600;
                color: {UIConstants.PRIMARY_COLOR};
                padding: {UIConstants.PADDING_SMALL}px {UIConstants.PADDING_MEDIUM}px;
                background-color: {UIConstants.BACKGROUND_COLOR};
                border-radius: {UIConstants.BORDER_RADIUS}px;
                border: 2px solid {UIConstants.PRIMARY_COLOR};
            }}
        """)

    def start_model_training(self):
        """Start model training"""
        message_logger.info("Starting model training")
        self.app_status_label.setText("🧠 Training Model...")
        self.app_status_label.setStyleSheet(f"""
            QLabel {{
                font-size: {UIConstants.FONT_SIZE_MEDIUM}px;
                font-weight: 600;
                color: {UIConstants.ACCENT_COLOR};
                padding: {UIConstants.PADDING_SMALL}px {UIConstants.PADDING_MEDIUM}px;
                background-color: {UIConstants.BACKGROUND_COLOR};
                border-radius: {UIConstants.BORDER_RADIUS}px;
                border: 2px solid {UIConstants.ACCENT_COLOR};
            }}
        """)

    def closeEvent(self, event):
        """Handle application close"""
        message_logger.info("Shutting down MFFUHijack application")

        if self.message_logger_window:
            self.message_logger_window.close()

        message_logger.info("Application shutdown complete")
        event.accept()
