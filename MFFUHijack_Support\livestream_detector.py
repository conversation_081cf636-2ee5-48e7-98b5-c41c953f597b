#!/usr/bin/env python3
"""
Livestream Detector for MFFUHijack
Automatically detects the latest active livestream from a YouTube channel
"""

import subprocess
import json
import re
from typing import Optional, Dict, List
from datetime import datetime
import logging

logger = logging.getLogger(__name__)


class LivestreamDetector:
    """Detects active livestreams from YouTube channels"""
    
    def __init__(self):
        self.default_channel = "https://www.youtube.com/@MyFundedFuturesPropFirm"

    def _is_currently_live(self, live_status: str, is_live: str, was_live: str, availability: str, title: str) -> bool:
        """
        Determine if a video is CURRENTLY LIVE (not upcoming or past)

        Args:
            live_status: Live status from yt-dlp
            is_live: Is live flag from yt-dlp
            was_live: Was live flag from yt-dlp
            availability: Availability status from yt-dlp
            title: Video title

        Returns:
            True if the video is currently live, False otherwise
        """
        # Primary indicators for LIVE status
        if live_status == 'is_live':
            print(f"      ✅ LIVE: live_status is 'is_live'")
            return True

        if is_live in ['True', 'true', '1']:
            print(f"      ✅ LIVE: is_live is {is_live}")
            return True

        # Check if it was live but isn't anymore
        if was_live in ['True', 'true', '1'] and live_status != 'is_live':
            print(f"      ❌ NOT LIVE: was_live={was_live} but live_status={live_status} (past stream)")
            return False

        # Check for upcoming streams
        if live_status in ['is_upcoming', 'upcoming']:
            print(f"      ❌ NOT LIVE: live_status is '{live_status}' (upcoming stream)")
            return False

        # Check availability for private/unlisted/unavailable streams
        if availability in ['private', 'unlisted', 'subscriber_only', 'needs_auth']:
            print(f"      ❌ NOT LIVE: availability is '{availability}' (restricted access)")
            return False

        # Title-based detection (less reliable, use as last resort)
        title_upper = title.upper()
        live_keywords = ['🔴 LIVE', '🔴LIVE', 'LIVE NOW', 'STREAMING NOW', 'LIVE STREAM']
        upcoming_keywords = ['UPCOMING', 'PREMIERES', 'SCHEDULED', 'STARTS AT', 'COMING SOON']

        # Check for upcoming indicators in title
        for keyword in upcoming_keywords:
            if keyword in title_upper:
                print(f"      ❌ NOT LIVE: title contains '{keyword}' (upcoming stream)")
                return False

        # Check for live indicators in title (only if no other clear indicators)
        if live_status == 'NA' and is_live == 'NA':
            for keyword in live_keywords:
                if keyword in title_upper:
                    print(f"      ⚠️  MAYBE LIVE: title contains '{keyword}' (title-based detection)")
                    return True

        print(f"      ❌ NOT LIVE: no clear live indicators found")
        return False
    
    def get_channel_livestreams(self, channel_url: str, max_results: int = 10) -> List[Dict]:
        """
        Get list of videos from a channel and filter for active livestreams
        
        Args:
            channel_url: YouTube channel URL
            max_results: Maximum number of videos to check
            
        Returns:
            List of livestream video information
        """
        print(f"🔍 Searching for active livestreams from: {channel_url}")
        
        try:
            # Use yt-dlp to get channel video list with detailed metadata for LIVE detection
            # Make sure we get the actual YouTube watch URLs
            cmd = [
                'python', '-m', 'yt_dlp',
                '--flat-playlist',
                '--print', '%(id)s|%(title)s|%(live_status)s|%(webpage_url)s|%(is_live)s|%(was_live)s|%(availability)s',
                '--playlist-end', str(max_results),
                channel_url
            ]
            
            print(f"🛠️  Running command: {' '.join(cmd)}")
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            
            print(f"📤 yt-dlp return code: {result.returncode}")
            if result.stderr:
                print(f"📤 yt-dlp stderr: {result.stderr}")
            
            if result.returncode != 0:
                print(f"❌ yt-dlp failed: {result.stderr}")
                return []
            
            # Parse the output
            videos = []
            for line in result.stdout.strip().split('\n'):
                if line.strip():
                    try:
                        parts = line.split('|')
                        if len(parts) >= 4:
                            video_id = parts[0]
                            title = parts[1]
                            live_status = parts[2]
                            webpage_url = parts[3]  # This is the actual YouTube watch URL
                            is_live = parts[4] if len(parts) > 4 else 'NA'
                            was_live = parts[5] if len(parts) > 5 else 'NA'
                            availability = parts[6] if len(parts) > 6 else 'NA'

                            # Ensure we have a proper YouTube URL
                            if not webpage_url.startswith('http'):
                                # Construct YouTube URL from video ID if needed
                                url = f"https://www.youtube.com/watch?v={video_id}"
                                print(f"   🔗 Constructed URL from ID: {url}")
                            else:
                                url = webpage_url
                                print(f"   🔗 Using webpage URL: {url}")

                            # Determine if this is CURRENTLY LIVE (not upcoming or past)
                            is_currently_live = self._is_currently_live(live_status, is_live, was_live, availability, title)

                            videos.append({
                                'id': video_id,
                                'title': title,
                                'live_status': live_status,
                                'url': url,
                                'is_live': is_live,
                                'was_live': was_live,
                                'availability': availability,
                                'is_currently_live': is_currently_live
                            })

                            status_info = f"Status: {live_status}"
                            if is_live != 'NA':
                                status_info += f", is_live: {is_live}"
                            if was_live != 'NA':
                                status_info += f", was_live: {was_live}"
                            if availability != 'NA':
                                status_info += f", availability: {availability}"
                            if is_currently_live:
                                status_info += " [🔴 CURRENTLY LIVE]"

                            print(f"   📺 Found: {title} ({status_info})")
                    except Exception as e:
                        print(f"   ⚠️  Error parsing line: {line} - {e}")

            # Filter for CURRENTLY LIVE streams only (not upcoming or past)
            livestreams = [v for v in videos if v['is_currently_live']]
            
            print(f"🎯 Found {len(livestreams)} active livestreams out of {len(videos)} videos")
            
            return livestreams
            
        except subprocess.TimeoutExpired:
            print("❌ yt-dlp command timed out (30 seconds)")
            return []
        except Exception as e:
            print(f"❌ Error getting channel livestreams: {e}")
            return []
    
    def search_live_videos(self, channel_url: str) -> List[Dict]:
        """
        Alternative method: Search for live videos using yt-dlp search

        Args:
            channel_url: YouTube channel URL

        Returns:
            List of live videos found
        """
        print(f"🔍 Searching for live videos using alternative method...")

        try:
            # Extract channel name/ID for search
            channel_name = channel_url.split('@')[-1] if '@' in channel_url else channel_url.split('/')[-1]

            # Search for live videos from this channel with detailed metadata
            cmd = [
                'python', '-m', 'yt_dlp',
                '--flat-playlist',
                '--print', '%(id)s|%(title)s|%(live_status)s|%(webpage_url)s|%(is_live)s|%(was_live)s|%(availability)s',
                '--playlist-end', '5',
                f'ytsearch5:{channel_name} live'
            ]

            print(f"🛠️  Running search command: {' '.join(cmd)}")

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)

            if result.returncode != 0:
                print(f"❌ Search failed: {result.stderr}")
                return []

            # Parse results and filter for CURRENTLY LIVE videos
            live_videos = []
            for line in result.stdout.strip().split('\n'):
                if line.strip():
                    try:
                        parts = line.split('|')
                        if len(parts) >= 4:
                            video_id = parts[0]
                            title = parts[1]
                            live_status = parts[2]
                            webpage_url = parts[3]  # This is the actual YouTube watch URL
                            is_live = parts[4] if len(parts) > 4 else 'NA'
                            was_live = parts[5] if len(parts) > 5 else 'NA'
                            availability = parts[6] if len(parts) > 6 else 'NA'

                            # Ensure we have a proper YouTube URL
                            if not webpage_url.startswith('http'):
                                # Construct YouTube URL from video ID if needed
                                url = f"https://www.youtube.com/watch?v={video_id}"
                                print(f"   🔗 Constructed search URL from ID: {url}")
                            else:
                                url = webpage_url
                                print(f"   🔗 Using search webpage URL: {url}")

                            # Check if this is CURRENTLY LIVE (not upcoming or past)
                            is_currently_live = self._is_currently_live(live_status, is_live, was_live, availability, title)

                            if is_currently_live:
                                live_videos.append({
                                    'id': video_id,
                                    'title': title,
                                    'live_status': live_status,
                                    'url': url,
                                    'is_live': is_live,
                                    'was_live': was_live,
                                    'availability': availability
                                })
                                print(f"   🎯 Found CURRENTLY LIVE video: {title}")
                            else:
                                print(f"   ⏭️  Skipped non-live video: {title}")
                    except Exception as e:
                        print(f"   ⚠️  Error parsing search result: {line} - {e}")

            return live_videos

        except Exception as e:
            print(f"❌ Error searching for live videos: {e}")
            return []

    def get_latest_livestream_url(self, channel_url: str) -> Optional[str]:
        """
        Get the URL of the latest active livestream from a channel

        Args:
            channel_url: YouTube channel URL

        Returns:
            URL of the latest livestream, or None if no active livestream found
        """
        print(f"🚀 Auto-detecting latest livestream from channel...")

        # Method 1: Get livestreams from the channel directly
        livestreams = self.get_channel_livestreams(channel_url)

        if livestreams:
            # Get the first (most recent) livestream
            latest_stream = livestreams[0]
            stream_url = latest_stream['url']
            stream_title = latest_stream['title']

            print(f"✅ Found latest livestream (Method 1):")
            print(f"   Title: {stream_title}")
            print(f"   URL: {stream_url}")

            return stream_url

        # Method 2: Search for live videos if direct method failed
        print("🔄 Trying alternative search method...")
        live_videos = self.search_live_videos(channel_url)

        if live_videos:
            latest_video = live_videos[0]
            stream_url = latest_video['url']
            stream_title = latest_video['title']

            print(f"✅ Found live video (Method 2):")
            print(f"   Title: {stream_title}")
            print(f"   URL: {stream_url}")

            return stream_url

        print("❌ No active livestreams found with either method")
        return None
    
    def validate_channel_url(self, url: str) -> bool:
        """
        Validate that the URL is a valid YouTube channel URL
        
        Args:
            url: URL to validate
            
        Returns:
            True if valid channel URL, False otherwise
        """
        # YouTube channel URL patterns
        channel_patterns = [
            r'https?://(?:www\.)?youtube\.com/@[\w\-\.]+',
            r'https?://(?:www\.)?youtube\.com/c/[\w\-\.]+',
            r'https?://(?:www\.)?youtube\.com/channel/[\w\-\.]+',
            r'https?://(?:www\.)?youtube\.com/user/[\w\-\.]+',
        ]
        
        for pattern in channel_patterns:
            if re.match(pattern, url):
                return True
        
        return False
    
    def get_channel_info(self, channel_url: str) -> Optional[Dict]:
        """
        Get basic information about a YouTube channel
        
        Args:
            channel_url: YouTube channel URL
            
        Returns:
            Dictionary with channel information, or None if failed
        """
        try:
            print(f"📋 Getting channel information for: {channel_url}")
            
            cmd = [
                'python', '-m', 'yt_dlp',
                '--flat-playlist',
                '--print', '%(channel)s|%(channel_id)s|%(uploader)s',
                '--playlist-end', '1',
                channel_url
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0 and result.stdout.strip():
                line = result.stdout.strip().split('\n')[0]
                parts = line.split('|')
                if len(parts) >= 2:
                    return {
                        'channel_name': parts[0] if parts[0] != 'NA' else parts[2],
                        'channel_id': parts[1],
                        'uploader': parts[2] if len(parts) > 2 else parts[0]
                    }
            
            return None
            
        except Exception as e:
            print(f"❌ Error getting channel info: {e}")
            return None


def auto_detect_livestream(channel_url: str = None) -> Optional[str]:
    """
    Convenience function to auto-detect the latest livestream
    
    Args:
        channel_url: YouTube channel URL (uses default if None)
        
    Returns:
        URL of the latest livestream, or None if not found
    """
    detector = LivestreamDetector()
    
    if channel_url is None:
        channel_url = detector.default_channel
        print(f"🔧 Using default channel: {channel_url}")
    
    # Validate channel URL
    if not detector.validate_channel_url(channel_url):
        print(f"❌ Invalid channel URL: {channel_url}")
        return None
    
    # Get channel info
    channel_info = detector.get_channel_info(channel_url)
    if channel_info:
        print(f"📺 Channel: {channel_info['channel_name']}")
    
    # Find latest livestream
    return detector.get_latest_livestream_url(channel_url)


def test_livestream_detection():
    """Test the livestream detection functionality"""
    print("🧪 Testing Livestream Detection")
    print("=" * 50)
    
    # Test with default channel
    print("Testing with default channel...")
    stream_url = auto_detect_livestream()
    
    if stream_url:
        print(f"✅ Success! Found livestream: {stream_url}")
        return True
    else:
        print("❌ No active livestream found (this may be normal)")
        return False


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        # Test with provided channel URL
        channel_url = sys.argv[1]
        print(f"Testing with provided channel: {channel_url}")
        stream_url = auto_detect_livestream(channel_url)
    else:
        # Test with default channel
        stream_url = auto_detect_livestream()
    
    if stream_url:
        print(f"\n🎉 Latest livestream URL: {stream_url}")
        sys.exit(0)
    else:
        print(f"\n⚠️  No active livestream found")
        sys.exit(1)
