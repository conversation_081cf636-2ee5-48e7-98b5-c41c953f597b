#!/usr/bin/env python3
"""
Test script for the main MFFUHijack application
Verifies all components load correctly
"""

import sys
import os

def test_imports():
    """Test all imports"""
    print("🧪 Testing MFFUHijack Main Application")
    print("=" * 50)
    
    try:
        print("📦 Testing imports...")
        
        # Test PyQt6
        from PyQt6.QtWidgets import QApplication
        print("  ✅ PyQt6 available")
        
        # Test main application
        from mffuhijack_main import MFFUHijackMainWindow
        print("  ✅ Main application class imported")
        
        # Test individual tabs
        from mffuhijack_main import LivestreamTab, DatasetTab, LivestreamTestingTab
        print("  ✅ All tab classes imported")
        
        print("\n🚀 Creating test application...")
        
        # Create application
        app = QApplication(sys.argv)
        
        # Create main window
        main_window = MFFUHijackMainWindow()
        print("  ✅ Main window created successfully")
        
        # Test window properties
        print(f"  📏 Window size: {main_window.size().width()}x{main_window.size().height()}")
        print(f"  📝 Window title: {main_window.windowTitle()}")
        
        # Test tabs
        tab_widget = main_window.tab_widget
        print(f"  📑 Number of tabs: {tab_widget.count()}")
        
        for i in range(tab_widget.count()):
            tab_text = tab_widget.tabText(i)
            print(f"    • Tab {i+1}: {tab_text}")
        
        print("\n✅ All tests passed!")
        print("🎯 The application is ready to run")
        print("\nTo start the application:")
        print("  • Run: python mffuhijack_main.py")
        print("  • Or double-click: run_mffuhijack.bat")
        
        return True
        
    except ImportError as e:
        print(f"  ❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"  ❌ Error: {e}")
        return False


def main():
    """Main test function"""
    success = test_imports()
    
    if success:
        print("\n" + "=" * 50)
        print("🎉 MFFUHijack is ready to use!")
        print("=" * 50)
    else:
        print("\n" + "=" * 50)
        print("❌ There are issues that need to be resolved")
        print("=" * 50)
    
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
