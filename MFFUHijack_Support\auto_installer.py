#!/usr/bin/env python3
"""
Automatic Installer for MFFUHijack
Handles automatic installation of all required dependencies including external tools
"""

import sys
import subprocess
import importlib
import platform
from typing import List, Dict, Tuple, Optional
from pathlib import Path

# Import our installers
from ffmpeg_installer import install_ffmpeg_if_needed
from yt_dlp_installer import install_yt_dlp_if_needed


class AutoInstaller:
    """Handles automatic installation of all MFFUHijack dependencies"""
    
    def __init__(self):
        self.system = platform.system().lower()
        self.installed_packages = []
        self.failed_packages = []
        
    def check_package(self, package_name: str, import_name: str = None) -> bool:
        """Check if a Python package is installed"""
        if import_name is None:
            import_name = package_name
        
        try:
            importlib.import_module(import_name)
            return True
        except ImportError:
            return False
    
    def install_package(self, package_name: str, timeout: int = 300) -> bool:
        """Install a Python package using pip"""
        try:
            print(f"📦 Installing {package_name}...")
            
            # Use pip to install the package
            cmd = [sys.executable, "-m", "pip", "install", package_name, "--upgrade"]
            
            result = subprocess.run(
                cmd, 
                capture_output=True, 
                text=True, 
                timeout=timeout
            )
            
            if result.returncode == 0:
                print(f"✅ {package_name} installed successfully")
                self.installed_packages.append(package_name)
                return True
            else:
                print(f"❌ Failed to install {package_name}")
                if result.stderr:
                    error_lines = result.stderr.strip().split('\n')[-3:]  # Last 3 lines
                    print(f"   Error: {' '.join(error_lines)}")
                self.failed_packages.append(package_name)
                return False
                
        except subprocess.TimeoutExpired:
            print(f"❌ Installation of {package_name} timed out")
            self.failed_packages.append(package_name)
            return False
        except Exception as e:
            print(f"❌ Error installing {package_name}: {e}")
            self.failed_packages.append(package_name)
            return False
    
    def get_required_packages(self) -> List[Tuple[str, str, str]]:
        """Get list of required packages (package_name, import_name, description)"""
        return [
            ('PyQt6', 'PyQt6', 'GUI framework'),
            ('opencv-python', 'cv2', 'Computer vision library'),
            ('numpy', 'numpy', 'Numerical computing'),
            ('requests', 'requests', 'HTTP library'),
            ('Pillow', 'PIL', 'Image processing'),
            ('yt-dlp', 'yt_dlp', 'YouTube video downloader'),
        ]
    
    def get_optional_packages(self) -> List[Tuple[str, str, str]]:
        """Get list of optional packages for enhanced functionality"""
        return [
            ('easyocr', 'easyocr', 'OCR engine (recommended)'),
            ('torch', 'torch', 'Machine learning framework'),
            ('torchvision', 'torchvision', 'Computer vision utilities'),
            ('tqdm', 'tqdm', 'Progress bars'),
        ]
    
    def install_python_packages(self, auto_install_optional: bool = True) -> Tuple[bool, List[str]]:
        """Install all required and optional Python packages"""
        print("🐍 Installing Python packages...")
        print("=" * 50)
        
        # Check and install required packages
        required_packages = self.get_required_packages()
        missing_required = []
        
        for package_name, import_name, description in required_packages:
            if not self.check_package(package_name, import_name):
                missing_required.append((package_name, import_name, description))
        
        if missing_required:
            print(f"📋 Found {len(missing_required)} missing required packages")
            for package_name, import_name, description in missing_required:
                print(f"   • {package_name} - {description}")
            
            print("\n🚀 Installing required packages...")
            for package_name, import_name, description in missing_required:
                self.install_package(package_name)
        else:
            print("✅ All required packages are already installed")
        
        # Check and install optional packages
        if auto_install_optional:
            optional_packages = self.get_optional_packages()
            missing_optional = []
            
            for package_name, import_name, description in optional_packages:
                if not self.check_package(package_name, import_name):
                    missing_optional.append((package_name, import_name, description))
            
            if missing_optional:
                print(f"\n📋 Found {len(missing_optional)} missing optional packages")
                for package_name, import_name, description in missing_optional:
                    print(f"   • {package_name} - {description}")
                
                print("\n🚀 Installing optional packages...")
                for package_name, import_name, description in missing_optional:
                    # For PyTorch, use special handling due to size
                    if package_name in ['torch', 'torchvision']:
                        print(f"⚠️  {package_name} is large (~1GB+) and may take several minutes...")
                        self.install_package(package_name, timeout=600)  # 10 minutes
                    else:
                        self.install_package(package_name)
            else:
                print("✅ All optional packages are already installed")
        
        # Check if critical packages are now available
        critical_missing = []
        for package_name, import_name, description in required_packages:
            if not self.check_package(package_name, import_name):
                critical_missing.append(package_name)
        
        success = len(critical_missing) == 0
        return success, critical_missing
    
    def install_external_tools(self) -> bool:
        """Install external tools like yt-dlp and FFmpeg"""
        print("\n🛠️  Installing external tools...")
        print("=" * 50)

        # Install yt-dlp (comprehensive installation with fallbacks)
        print("📺 Installing yt-dlp...")
        yt_dlp_success = install_yt_dlp_if_needed(auto_download=True)

        # Install FFmpeg
        print("\n🎬 Installing FFmpeg...")
        ffmpeg_success = install_ffmpeg_if_needed()

        return yt_dlp_success and ffmpeg_success
    
    def run_full_installation(self, auto_install_optional: bool = True) -> bool:
        """Run complete automatic installation"""
        print("🚀 MFFUHijack Automatic Installer")
        print("=" * 50)
        print(f"Platform: {platform.system()} {platform.machine()}")
        print(f"Python: {sys.version}")
        print()
        
        # Install Python packages
        python_success, missing_critical = self.install_python_packages(auto_install_optional)
        
        # Install external tools
        external_success = self.install_external_tools()
        
        # Summary
        print("\n📊 Installation Summary")
        print("=" * 50)
        
        if self.installed_packages:
            print(f"✅ Successfully installed {len(self.installed_packages)} packages:")
            for package in self.installed_packages:
                print(f"   • {package}")
        
        if self.failed_packages:
            print(f"\n❌ Failed to install {len(self.failed_packages)} packages:")
            for package in self.failed_packages:
                print(f"   • {package}")
        
        if missing_critical:
            print(f"\n⚠️  Critical packages still missing:")
            for package in missing_critical:
                print(f"   • {package}")
        
        # Overall success
        overall_success = python_success and external_success
        
        if overall_success:
            print("\n🎉 Installation completed successfully!")
            print("You can now run MFFUHijack with: python main.py")
        else:
            print("\n⚠️  Installation completed with some issues.")
            if not python_success:
                print("   • Some critical Python packages are missing")
            if not external_success:
                print("   • External tools (FFmpeg) may not be available")
            print("   • The application may have limited functionality")
        
        return overall_success
    
    def get_installation_status(self) -> Dict[str, bool]:
        """Get current installation status of all dependencies"""
        status = {}
        
        # Check Python packages
        all_packages = self.get_required_packages() + self.get_optional_packages()
        for package_name, import_name, description in all_packages:
            status[package_name] = self.check_package(package_name, import_name)
        
        # Check external tools
        # Check yt-dlp
        try:
            result = subprocess.run(['python', '-m', 'yt_dlp', '--version'],
                                  capture_output=True, text=True, timeout=5)
            status['yt-dlp'] = result.returncode == 0
        except:
            status['yt-dlp'] = False

        # Check FFmpeg
        try:
            result = subprocess.run(['ffmpeg', '-version'],
                                  capture_output=True, text=True, timeout=5)
            status['ffmpeg'] = result.returncode == 0
        except:
            status['ffmpeg'] = False
        
        return status


def run_auto_installer(install_optional: bool = True) -> bool:
    """Run the automatic installer"""
    installer = AutoInstaller()
    return installer.run_full_installation(install_optional)


def check_installation_status() -> Dict[str, bool]:
    """Check current installation status"""
    installer = AutoInstaller()
    return installer.get_installation_status()


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='MFFUHijack Automatic Installer')
    parser.add_argument('--no-optional', action='store_true',
                       help='Skip optional packages')
    parser.add_argument('--status-only', action='store_true',
                       help='Only check installation status')
    
    args = parser.parse_args()
    
    if args.status_only:
        status = check_installation_status()
        print("📊 Installation Status:")
        print("=" * 30)
        for package, installed in status.items():
            status_icon = "✅" if installed else "❌"
            print(f"{status_icon} {package}")
    else:
        install_optional = not args.no_optional
        success = run_auto_installer(install_optional)
        sys.exit(0 if success else 1)
