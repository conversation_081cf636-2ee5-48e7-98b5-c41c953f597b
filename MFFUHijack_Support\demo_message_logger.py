#!/usr/bin/env python3
"""
Demonstration script showing the message logger integration
This simulates typical MFFUHijack operations and shows how messages are logged
"""

import sys
import os
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QTimer

# Add current directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from gui import MFFUHijackGUI, message_logger


class MessageLoggerDemo:
    """Demo class that simulates MFFUHijack operations"""
    
    def __init__(self, gui):
        self.gui = gui
        self.demo_step = 0
        self.timer = QTimer()
        self.timer.timeout.connect(self.run_demo_step)
        
    def start_demo(self):
        """Start the demonstration"""
        message_logger.info("Starting MFFUHijack Message Logger Demonstration")
        message_logger.status("Demo initialized - will simulate typical operations")
        self.timer.start(3000)  # Every 3 seconds
        
    def run_demo_step(self):
        """Run the next step of the demonstration"""
        self.demo_step += 1
        
        if self.demo_step == 1:
            message_logger.info("Simulating livestream bot startup...")
            message_logger.status("Connecting to YouTube livestream")
            
        elif self.demo_step == 2:
            message_logger.success("Successfully connected to livestream")
            message_logger.info("Starting OCR processing with EasyOCR engine")
            message_logger.status("Bot status: Running")
            
        elif self.demo_step == 3:
            message_logger.info("Processing video frames...")
            message_logger.info("Frame 1: No codes detected")
            message_logger.info("Frame 2: No codes detected")
            
        elif self.demo_step == 4:
            message_logger.success("🎯 CODE DETECTED: RESET3J | Type: Free Reset Code | Confidence: 0.95")
            message_logger.info("Submitting code to server...")
            
        elif self.demo_step == 5:
            message_logger.success("Code submitted successfully!")
            message_logger.info("Continuing to monitor livestream...")
            
        elif self.demo_step == 6:
            message_logger.warning("OCR confidence below threshold (0.6) - skipping potential code")
            message_logger.info("Frame processing continues...")
            
        elif self.demo_step == 7:
            message_logger.info("Starting dataset builder demonstration...")
            message_logger.status("Scanning YouTube channel for training data")
            
        elif self.demo_step == 8:
            message_logger.info("Found 15 potential training videos")
            message_logger.status("Extracting frames from videos...")
            
        elif self.demo_step == 9:
            message_logger.info("Extracted frame 1/50 - Code: STARTER5X")
            message_logger.info("Extracted frame 2/50 - Code: EXPERT2Y")
            message_logger.info("Extracted frame 3/50 - Code: RESET9Z")
            
        elif self.demo_step == 10:
            message_logger.success("Dataset building complete! Collected 50 training frames")
            message_logger.info("Starting custom OCR model training...")
            
        elif self.demo_step == 11:
            message_logger.status("Training epoch 1/10 - Loss: 0.245")
            message_logger.status("Training epoch 2/10 - Loss: 0.198")
            message_logger.status("Training epoch 3/10 - Loss: 0.156")
            
        elif self.demo_step == 12:
            message_logger.success("Model training complete! Saved to: ocr_models/custom_model_v1.pth")
            message_logger.info("Model accuracy: 94.2%")
            
        elif self.demo_step == 13:
            message_logger.warning("Simulating connection issue...")
            message_logger.error("Failed to connect to livestream - retrying in 5 seconds")
            
        elif self.demo_step == 14:
            message_logger.success("Connection restored successfully")
            message_logger.status("Bot status: Running")
            
        elif self.demo_step == 15:
            message_logger.info("Demonstration complete!")
            message_logger.success("Message Logger Demo finished successfully")
            message_logger.status("All systems operational")
            self.timer.stop()
            
        else:
            # Reset demo
            self.demo_step = 0
            message_logger.info("Restarting demonstration...")


def main():
    """Run the message logger demonstration"""
    print("🚀 Starting MFFUHijack Message Logger Demo...")
    print("This will show the main application with the message logger window.")
    print("The demo will simulate typical operations to show how messages are logged.")
    print("Close the application windows when you're done viewing the demo.")
    
    # Create QApplication
    app = QApplication(sys.argv)
    
    # Create main GUI (this will also create and show the message logger window)
    gui = MFFUHijackGUI()
    gui.show()
    
    # Create and start demo
    demo = MessageLoggerDemo(gui)
    
    # Start demo after a short delay
    QTimer.singleShot(2000, demo.start_demo)
    
    # Start event loop
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
