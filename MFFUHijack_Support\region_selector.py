"""
Region Selector Widget for MFFUHijack
Allows users to select OCR scanning regions with resizable rectangular boxes
"""

import cv2
import numpy as np
from typing import Tuple, Optional, Callable
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QSlider, QSpinBox, QGroupBox, QFrame
)
from PyQt6.QtCore import Qt, pyqtSignal, QRect, QPoint
from PyQt6.QtGui import QPixmap, QImage, QPainter, QPen, QColor, QMouseEvent


def detect_black_bars(image: np.ndarray, threshold: int = 30) -> Tuple[int, int, int, int]:
    """
    Detect black bars in an image and return the content area coordinates.
    Returns (x, y, width, height) of the content area excluding black bars.
    """
    if len(image.shape) == 3:
        # Convert to grayscale for analysis
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    else:
        gray = image

    height, width = gray.shape

    # Detect left black bar
    left_edge = 0
    for x in range(width // 4):  # Check first quarter
        column = gray[:, x]
        if np.mean(column) > threshold:
            left_edge = x
            break

    # Detect right black bar
    right_edge = width
    for x in range(width - 1, width * 3 // 4, -1):  # Check last quarter
        column = gray[:, x]
        if np.mean(column) > threshold:
            right_edge = x + 1
            break

    # Detect top black bar
    top_edge = 0
    for y in range(height // 4):  # Check first quarter
        row = gray[y, :]
        if np.mean(row) > threshold:
            top_edge = y
            break

    # Detect bottom black bar
    bottom_edge = height
    for y in range(height - 1, height * 3 // 4, -1):  # Check last quarter
        row = gray[y, :]
        if np.mean(row) > threshold:
            bottom_edge = y + 1
            break

    # Return content area
    content_width = right_edge - left_edge
    content_height = bottom_edge - top_edge

    return (left_edge, top_edge, content_width, content_height)


class SelectableImageLabel(QLabel):
    """QLabel that allows drawing and resizing selection rectangles"""
    
    region_changed = pyqtSignal(tuple)  # Emits (x, y, width, height) as percentages
    
    def __init__(self):
        super().__init__()
        self.setMinimumSize(400, 300)
        self.setStyleSheet("border: 2px solid #ccc; background-color: #f0f0f0;")
        self.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.setScaledContents(False)
        
        # Selection rectangle (in widget coordinates)
        self.selection_rect = QRect()
        self.is_selecting = False
        self.is_resizing = False
        self.resize_handle = None
        self.start_point = QPoint()
        
        # Handle size for resize corners
        self.handle_size = 8
        
        # Original image for calculations
        self.original_image = None
        self.scaled_pixmap = None
        
        # Default selection (center 50% of image)
        self.default_selection_percent = (25, 25, 50, 50)  # x%, y%, w%, h%
        
    def set_image(self, image: np.ndarray):
        """Set the image and create default selection"""
        self.original_image = image.copy()
        
        # Convert to QPixmap
        height, width, channel = image.shape
        bytes_per_line = 3 * width
        q_image = QImage(image.data, width, height, bytes_per_line, QImage.Format.Format_RGB888)
        pixmap = QPixmap.fromImage(q_image)
        
        # Scale to fit widget while maintaining aspect ratio
        self.scaled_pixmap = pixmap.scaled(
            self.size(), Qt.AspectRatioMode.KeepAspectRatio, 
            Qt.TransformationMode.SmoothTransformation
        )
        
        # Set default selection
        self.set_selection_percent(*self.default_selection_percent)
        
        self.update()
    
    def set_selection_percent(self, x_percent: float, y_percent: float, 
                            width_percent: float, height_percent: float):
        """Set selection rectangle using percentages of image size"""
        if not self.scaled_pixmap:
            return
        
        # Calculate selection in widget coordinates
        pixmap_rect = self.get_pixmap_rect()
        
        x = pixmap_rect.x() + (x_percent / 100.0) * pixmap_rect.width()
        y = pixmap_rect.y() + (y_percent / 100.0) * pixmap_rect.height()
        width = (width_percent / 100.0) * pixmap_rect.width()
        height = (height_percent / 100.0) * pixmap_rect.height()
        
        self.selection_rect = QRect(int(x), int(y), int(width), int(height))
        self.update()
        self.emit_region_changed()
    
    def get_selection_percent(self) -> Tuple[float, float, float, float]:
        """Get selection rectangle as percentages of image size"""
        if not self.scaled_pixmap or self.selection_rect.isEmpty():
            return self.default_selection_percent
        
        pixmap_rect = self.get_pixmap_rect()
        if pixmap_rect.width() == 0 or pixmap_rect.height() == 0:
            return self.default_selection_percent
        
        # Convert to percentages
        x_percent = ((self.selection_rect.x() - pixmap_rect.x()) / pixmap_rect.width()) * 100
        y_percent = ((self.selection_rect.y() - pixmap_rect.y()) / pixmap_rect.height()) * 100
        width_percent = (self.selection_rect.width() / pixmap_rect.width()) * 100
        height_percent = (self.selection_rect.height() / pixmap_rect.height()) * 100
        
        return (max(0, x_percent), max(0, y_percent), 
                max(1, width_percent), max(1, height_percent))
    
    def get_pixmap_rect(self) -> QRect:
        """Get the rectangle where the scaled pixmap is drawn"""
        if not self.scaled_pixmap:
            return QRect()
        
        # Calculate centered position
        x = (self.width() - self.scaled_pixmap.width()) // 2
        y = (self.height() - self.scaled_pixmap.height()) // 2
        
        return QRect(x, y, self.scaled_pixmap.width(), self.scaled_pixmap.height())
    
    def get_resize_handle(self, pos: QPoint) -> Optional[str]:
        """Check if position is on a resize handle"""
        if self.selection_rect.isEmpty():
            return None
        
        rect = self.selection_rect
        handle_size = self.handle_size
        
        # Check corners and edges
        if QRect(rect.topLeft(), QRect(0, 0, handle_size, handle_size).size()).contains(pos):
            return "top_left"
        elif QRect(rect.topRight() - QPoint(handle_size, 0), QRect(0, 0, handle_size, handle_size).size()).contains(pos):
            return "top_right"
        elif QRect(rect.bottomLeft() - QPoint(0, handle_size), QRect(0, 0, handle_size, handle_size).size()).contains(pos):
            return "bottom_left"
        elif QRect(rect.bottomRight() - QPoint(handle_size, handle_size), QRect(0, 0, handle_size, handle_size).size()).contains(pos):
            return "bottom_right"
        
        return None
    
    def mousePressEvent(self, event: QMouseEvent):
        """Handle mouse press for selection and resizing"""
        if event.button() != Qt.MouseButton.LeftButton:
            return
        
        pos = event.position().toPoint()
        pixmap_rect = self.get_pixmap_rect()
        
        # Only allow interaction within the image area
        if not pixmap_rect.contains(pos):
            return
        
        # Check if clicking on resize handle
        handle = self.get_resize_handle(pos)
        if handle:
            self.is_resizing = True
            self.resize_handle = handle
            self.start_point = pos
        elif self.selection_rect.contains(pos):
            # Start moving the selection
            self.is_selecting = True
            self.start_point = pos
        else:
            # Start new selection
            self.is_selecting = True
            self.start_point = pos
            self.selection_rect = QRect(pos, pos)
        
        self.update()
    
    def mouseMoveEvent(self, event: QMouseEvent):
        """Handle mouse move for selection and resizing"""
        pos = event.position().toPoint()
        pixmap_rect = self.get_pixmap_rect()
        
        if self.is_resizing and self.resize_handle:
            # Resize the selection rectangle
            self.resize_selection(pos)
        elif self.is_selecting:
            if self.selection_rect.contains(self.start_point):
                # Move the selection
                delta = pos - self.start_point
                new_rect = self.selection_rect.translated(delta)
                
                # Keep within image bounds
                if pixmap_rect.contains(new_rect):
                    self.selection_rect = new_rect
                    self.start_point = pos
            else:
                # Create new selection
                self.selection_rect = QRect(self.start_point, pos).normalized()
                
                # Keep within image bounds
                self.selection_rect = self.selection_rect.intersected(pixmap_rect)
        
        self.update()
    
    def mouseReleaseEvent(self, event: QMouseEvent):
        """Handle mouse release"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.is_selecting = False
            self.is_resizing = False
            self.resize_handle = None
            self.emit_region_changed()
    
    def resize_selection(self, pos: QPoint):
        """Resize selection rectangle based on handle"""
        if not self.resize_handle:
            return
        
        rect = self.selection_rect
        pixmap_rect = self.get_pixmap_rect()
        
        # Constrain position to image bounds
        pos.setX(max(pixmap_rect.left(), min(pixmap_rect.right(), pos.x())))
        pos.setY(max(pixmap_rect.top(), min(pixmap_rect.bottom(), pos.y())))
        
        if self.resize_handle == "top_left":
            rect.setTopLeft(pos)
        elif self.resize_handle == "top_right":
            rect.setTopRight(pos)
        elif self.resize_handle == "bottom_left":
            rect.setBottomLeft(pos)
        elif self.resize_handle == "bottom_right":
            rect.setBottomRight(pos)
        
        # Ensure minimum size
        if rect.width() < 20:
            rect.setWidth(20)
        if rect.height() < 20:
            rect.setHeight(20)
        
        self.selection_rect = rect.normalized()
    
    def emit_region_changed(self):
        """Emit signal with current selection as percentages"""
        if self.scaled_pixmap:
            x_percent, y_percent, width_percent, height_percent = self.get_selection_percent()
            self.region_changed.emit((x_percent, y_percent, width_percent, height_percent))
    
    def paintEvent(self, event):
        """Custom paint event to draw image and selection"""
        super().paintEvent(event)
        
        if not self.scaled_pixmap:
            return
        
        painter = QPainter(self)
        
        # Draw the scaled image centered
        pixmap_rect = self.get_pixmap_rect()
        painter.drawPixmap(pixmap_rect, self.scaled_pixmap)
        
        # Draw selection rectangle
        if not self.selection_rect.isEmpty():
            # Semi-transparent overlay outside selection
            painter.fillRect(self.rect(), QColor(0, 0, 0, 100))
            painter.fillRect(self.selection_rect, QColor(0, 0, 0, 0))
            
            # Selection border
            pen = QPen(QColor(255, 0, 0), 2)
            painter.setPen(pen)
            painter.drawRect(self.selection_rect)
            
            # Resize handles
            handle_color = QColor(255, 255, 255)
            handle_border = QColor(255, 0, 0)
            
            handles = [
                self.selection_rect.topLeft(),
                self.selection_rect.topRight(),
                self.selection_rect.bottomLeft(),
                self.selection_rect.bottomRight()
            ]
            
            for handle_pos in handles:
                handle_rect = QRect(
                    handle_pos.x() - self.handle_size // 2,
                    handle_pos.y() - self.handle_size // 2,
                    self.handle_size, self.handle_size
                )
                painter.fillRect(handle_rect, handle_color)
                painter.setPen(QPen(handle_border, 1))
                painter.drawRect(handle_rect)
    
    def resizeEvent(self, event):
        """Handle widget resize"""
        super().resizeEvent(event)
        if self.original_image is not None:
            # Recreate scaled pixmap and adjust selection
            self.set_image(self.original_image)


class RegionSelectorWidget(QWidget):
    """Complete region selector with controls and preview"""
    
    region_changed = pyqtSignal(tuple)  # Emits (x%, y%, w%, h%)
    
    def __init__(self, title: str = "Select OCR Region"):
        super().__init__()
        self.title = title
        self.current_image = None
        self.content_area = None  # Will store detected content area (x, y, w, h) as percentages
        self.init_ui()
    
    def init_ui(self):
        """Initialize the user interface"""
        layout = QVBoxLayout()
        
        # Title
        title_label = QLabel(self.title)
        title_label.setStyleSheet("font-weight: bold; font-size: 14px; color: #333;")
        layout.addWidget(title_label)
        
        # Image selector
        self.image_label = SelectableImageLabel()
        self.image_label.region_changed.connect(self.on_region_changed)
        layout.addWidget(self.image_label)
        
        # Controls
        controls_layout = QHBoxLayout()
        
        # Preset buttons
        preset_group = QGroupBox("Quick Presets")
        preset_layout = QHBoxLayout()
        
        presets = [
            ("Full Screen", "full_screen"),
            ("Bottom Half", "bottom_half"),
            ("Bottom Third", "bottom_third"),
            ("Center", "center"),
            ("Bottom Center", "bottom_center")
        ]

        for name, preset_type in presets:
            btn = QPushButton(name)
            btn.clicked.connect(lambda checked, preset=preset_type: self.set_smart_preset(preset))
            preset_layout.addWidget(btn)
        
        preset_group.setLayout(preset_layout)
        controls_layout.addWidget(preset_group)
        
        layout.addLayout(controls_layout)
        
        # Region info
        self.region_info = QLabel("Region: Not set")
        self.region_info.setStyleSheet("color: #666; font-size: 11px;")
        layout.addWidget(self.region_info)
        
        self.setLayout(layout)
    
    def set_image(self, image: np.ndarray):
        """Set the image for region selection"""
        # Convert BGR to RGB if needed
        if len(image.shape) == 3 and image.shape[2] == 3:
            image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        else:
            image_rgb = image

        self.current_image = image_rgb

        # Detect content area (excluding black bars)
        content_x, content_y, content_w, content_h = detect_black_bars(image)
        height, width = image.shape[:2]

        # Convert to percentages
        self.content_area = (
            (content_x / width) * 100,
            (content_y / height) * 100,
            (content_w / width) * 100,
            (content_h / height) * 100
        )

        self.image_label.set_image(image_rgb)
    
    def set_preset(self, x: float, y: float, w: float, h: float):
        """Set a preset region"""
        self.image_label.set_selection_percent(x, y, w, h)

    def set_smart_preset(self, preset_type: str):
        """Set a smart preset that adapts to content area"""
        if not self.content_area:
            # Fallback to basic presets if no content area detected
            basic_presets = {
                "full_screen": (0, 0, 100, 100),
                "bottom_half": (0, 50, 100, 50),
                "bottom_third": (0, 67, 100, 33),
                "center": (25, 25, 50, 50),
                "bottom_center": (25, 67, 50, 33)
            }
            if preset_type in basic_presets:
                x, y, w, h = basic_presets[preset_type]
                self.set_preset(x, y, w, h)
            return

        content_x, content_y, content_w, content_h = self.content_area

        if preset_type == "full_screen":
            # Use detected content area, excluding black bars
            self.set_preset(content_x, content_y, content_w, content_h)
        elif preset_type == "bottom_half":
            # Bottom half of content area
            half_height = content_h / 2
            self.set_preset(content_x, content_y + half_height, content_w, half_height)
        elif preset_type == "bottom_third":
            # Bottom third of content area
            third_height = content_h / 3
            self.set_preset(content_x, content_y + (2 * third_height), content_w, third_height)
        elif preset_type == "center":
            # Center quarter of content area
            quarter_w = content_w / 2
            quarter_h = content_h / 2
            center_x = content_x + quarter_w / 2
            center_y = content_y + quarter_h / 2
            self.set_preset(center_x, center_y, quarter_w, quarter_h)
        elif preset_type == "bottom_center":
            # Bottom center of content area
            half_w = content_w / 2
            third_h = content_h / 3
            center_x = content_x + content_w / 4
            bottom_y = content_y + (2 * third_h)
            self.set_preset(center_x, bottom_y, half_w, third_h)
    
    def get_region_percent(self) -> Tuple[float, float, float, float]:
        """Get current region as percentages"""
        return self.image_label.get_selection_percent()
    
    def get_region_pixels(self, image_width: int, image_height: int) -> Tuple[int, int, int, int]:
        """Get current region in pixel coordinates"""
        x_percent, y_percent, w_percent, h_percent = self.get_region_percent()
        
        x = int((x_percent / 100.0) * image_width)
        y = int((y_percent / 100.0) * image_height)
        w = int((w_percent / 100.0) * image_width)
        h = int((h_percent / 100.0) * image_height)
        
        return (x, y, w, h)
    
    def on_region_changed(self, region_percent: Tuple[float, float, float, float]):
        """Handle region change"""
        x, y, w, h = region_percent
        self.region_info.setText(f"Region: {x:.1f}%, {y:.1f}%, {w:.1f}% × {h:.1f}%")
        self.region_changed.emit(region_percent)


if __name__ == "__main__":
    # Test the region selector
    from PyQt6.QtWidgets import QApplication
    import sys
    
    app = QApplication(sys.argv)
    
    # Create test image
    test_image = np.ones((480, 640, 3), dtype=np.uint8) * 255
    cv2.putText(test_image, "Test Image for Region Selection", (50, 240), 
                cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
    
    # Create widget
    widget = RegionSelectorWidget("Test Region Selector")
    widget.set_image(test_image)
    widget.show()
    
    sys.exit(app.exec())
