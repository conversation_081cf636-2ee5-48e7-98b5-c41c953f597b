@echo off
echo Organizing MFFUHijack files...
echo.

REM Create support folder if it doesn't exist
if not exist "MFFUHijack_Support" mkdir "MFFUHijack_Support"

REM Move support files to the support folder
echo Moving support files...

if exist "smart_features.py" move "smart_features.py" "MFFUHijack_Support\"
if exist "temporal_analytics_dashboard.py" move "temporal_analytics_dashboard.py" "MFFUHijack_Support\"
if exist "manual_ocr_region_selector.py" move "manual_ocr_region_selector.py" "MFFUHijack_Support\"
if exist "livestream_testing_mode.py" move "livestream_testing_mode.py" "MFFUHijack_Support\"
if exist "livestream_testing_gui.py" move "livestream_testing_gui.py" "MFFUHijack_Support\"
if exist "psutil_replacement.py" move "psutil_replacement.py" "MFFUHijack_Support\"
if exist "live_scan_monitor.py" move "live_scan_monitor.py" "MFFUHijack_Support\"

REM Move test files
echo Moving test files...
if exist "test_smart_features.py" move "test_smart_features.py" "MFFUHijack_Support\"
if exist "test_livestream_testing_mode.py" move "test_livestream_testing_mode.py" "MFFUHijack_Support\"
if exist "test_manual_ocr_region.py" move "test_manual_ocr_region.py" "MFFUHijack_Support\"
if exist "test_session_monitor.py" move "test_session_monitor.py" "MFFUHijack_Support\"
if exist "test_main_app.py" move "test_main_app.py" "MFFUHijack_Support\"
if exist "launch_gui_test.py" move "launch_gui_test.py" "MFFUHijack_Support\"

REM Move documentation files
echo Moving documentation files...
if exist "SMART_FEATURES_DOCUMENTATION.md" move "SMART_FEATURES_DOCUMENTATION.md" "MFFUHijack_Support\"
if exist "LIVESTREAM_TESTING_MODE_DOCUMENTATION.md" move "LIVESTREAM_TESTING_MODE_DOCUMENTATION.md" "MFFUHijack_Support\"

REM Move old files
echo Moving old files...
if exist "gui.py" move "gui.py" "MFFUHijack_Support\"
if exist "main.py" move "main.py" "MFFUHijack_Support\"
if exist "ocr_utils.py" move "ocr_utils.py" "MFFUHijack_Support\"
if exist "submitter.py" move "submitter.py" "MFFUHijack_Support\"
if exist "START_MFFUHIJACK.bat" move "START_MFFUHIJACK.bat" "MFFUHijack_Support\"

REM Create logs and test_streams folders in support
if not exist "MFFUHijack_Support\test_logs" mkdir "MFFUHijack_Support\test_logs"
if not exist "MFFUHijack_Support\test_streams" mkdir "MFFUHijack_Support\test_streams"

echo.
echo File organization complete!
echo.
echo Main files in root directory:
echo   - mffuhijack_main.py (MAIN PROGRAM)
echo   - run_mffuhijack.bat (EASY LAUNCHER)
echo.
echo Support files moved to MFFUHijack_Support folder
echo.
pause
