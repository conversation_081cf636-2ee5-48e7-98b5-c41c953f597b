#!/usr/bin/env python3
"""
Enhanced Test script for the Live Scan Monitor Window
Demonstrates all the new enhanced functionality including themes, notifications,
interactive features, performance monitoring, and more.
"""

import sys
import time
import threading
import random
import numpy as np
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton,
    QHBoxLayout, QLabel, QSlider, QCheckBox, QComboBox, QGroupBox,
    QGridLayout, QSpinBox
)
from PyQt6.QtCore import QTimer, Qt
from PyQt6.QtGui import QPixmap, QImage

from live_scan_monitor import LiveScanMonitorWindow


class EnhancedTestControlWindow(QMainWindow):
    """Enhanced control window for testing all Live Scan Monitor features"""

    def __init__(self):
        super().__init__()
        self.monitor_window = None
        self.simulation_timer = None
        self.frame_simulation_timer = None
        self.init_ui()

    def init_ui(self):
        """Initialize the enhanced test control UI"""
        self.setWindowTitle("Live Scan Monitor Enhanced Test Controller")
        self.setGeometry(100, 100, 600, 700)

        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        layout = QVBoxLayout(central_widget)

        # Monitor Control Section
        monitor_group = QGroupBox("Monitor Control")
        monitor_layout = QVBoxLayout(monitor_group)

        # Basic controls
        basic_controls = QHBoxLayout()

        self.create_monitor_btn = QPushButton("🔍 Create Enhanced Monitor")
        self.create_monitor_btn.clicked.connect(self.create_monitor_window)
        basic_controls.addWidget(self.create_monitor_btn)

        self.start_session_btn = QPushButton("🚀 Start Session")
        self.start_session_btn.clicked.connect(self.start_scanning_session)
        self.start_session_btn.setEnabled(False)
        basic_controls.addWidget(self.start_session_btn)

        self.stop_session_btn = QPushButton("⏹️ Stop Session")
        self.stop_session_btn.clicked.connect(self.stop_scanning_session)
        self.stop_session_btn.setEnabled(False)
        basic_controls.addWidget(self.stop_session_btn)

        monitor_layout.addLayout(basic_controls)
        layout.addWidget(monitor_group)

        # Simulation Control Section
        sim_group = QGroupBox("Simulation Controls")
        sim_layout = QVBoxLayout(sim_group)

        # Auto simulation controls
        auto_sim_layout = QHBoxLayout()

        self.auto_sim_checkbox = QCheckBox("Auto-simulate codes")
        self.auto_sim_checkbox.toggled.connect(self.toggle_auto_simulation)
        auto_sim_layout.addWidget(self.auto_sim_checkbox)

        auto_sim_layout.addWidget(QLabel("Interval (s):"))
        self.sim_interval_spin = QSpinBox()
        self.sim_interval_spin.setRange(1, 30)
        self.sim_interval_spin.setValue(3)
        auto_sim_layout.addWidget(self.sim_interval_spin)

        sim_layout.addLayout(auto_sim_layout)

        # Manual simulation buttons
        manual_sim_layout = QHBoxLayout()

        self.simulate_valid_btn = QPushButton("✅ Valid Code")
        self.simulate_valid_btn.clicked.connect(lambda: self.simulate_code_detection(force_valid=True))
        self.simulate_valid_btn.setEnabled(False)
        manual_sim_layout.addWidget(self.simulate_valid_btn)

        self.simulate_invalid_btn = QPushButton("❌ Invalid Code")
        self.simulate_invalid_btn.clicked.connect(lambda: self.simulate_code_detection(force_valid=False))
        self.simulate_invalid_btn.setEnabled(False)
        manual_sim_layout.addWidget(self.simulate_invalid_btn)

        self.simulate_frame_btn = QPushButton("🎥 Simulate Frame")
        self.simulate_frame_btn.clicked.connect(self.simulate_frame_update)
        self.simulate_frame_btn.setEnabled(False)
        manual_sim_layout.addWidget(self.simulate_frame_btn)

        sim_layout.addLayout(manual_sim_layout)
        layout.addWidget(sim_group)

        # Frame Simulation Section
        frame_group = QGroupBox("Frame Simulation")
        frame_layout = QVBoxLayout(frame_group)

        # Frame simulation controls
        frame_controls = QHBoxLayout()

        self.auto_frames_checkbox = QCheckBox("Auto-simulate frames")
        self.auto_frames_checkbox.toggled.connect(self.toggle_frame_simulation)
        frame_controls.addWidget(self.auto_frames_checkbox)

        frame_controls.addWidget(QLabel("FPS:"))
        self.fps_spin = QSpinBox()
        self.fps_spin.setRange(1, 60)
        self.fps_spin.setValue(10)
        frame_controls.addWidget(self.fps_spin)

        frame_layout.addLayout(frame_controls)
        layout.addWidget(frame_group)

        # Test Features Section
        features_group = QGroupBox("Test Enhanced Features")
        features_layout = QGridLayout(features_group)

        # Theme testing
        theme_btn = QPushButton("🎨 Test Theme Toggle")
        theme_btn.clicked.connect(self.test_theme_toggle)
        features_layout.addWidget(theme_btn, 0, 0)

        # Screenshot testing
        screenshot_btn = QPushButton("📷 Test Screenshot")
        screenshot_btn.clicked.connect(self.test_screenshot)
        features_layout.addWidget(screenshot_btn, 0, 1)

        # Settings testing
        settings_btn = QPushButton("⚙️ Test Settings")
        settings_btn.clicked.connect(self.test_settings)
        features_layout.addWidget(settings_btn, 1, 0)

        # History testing
        history_btn = QPushButton("📚 Test History")
        history_btn.clicked.connect(self.test_history)
        features_layout.addWidget(history_btn, 1, 1)

        # Export testing
        export_btn = QPushButton("💾 Test Export")
        export_btn.clicked.connect(self.test_export)
        features_layout.addWidget(export_btn, 2, 0)

        # Fullscreen testing
        fullscreen_btn = QPushButton("⛶ Test Fullscreen")
        fullscreen_btn.clicked.connect(self.test_fullscreen)
        features_layout.addWidget(fullscreen_btn, 2, 1)

        layout.addWidget(features_group)

        # Status Section
        status_group = QGroupBox("Test Status")
        status_layout = QVBoxLayout(status_group)

        self.status_label = QLabel("Ready to test enhanced features")
        self.status_label.setWordWrap(True)
        status_layout.addWidget(self.status_label)

        layout.addWidget(status_group)
    
    def create_monitor_window(self):
        """Create the Enhanced Live Scan Monitor window"""
        if self.monitor_window is None:
            self.monitor_window = LiveScanMonitorWindow()
            self.monitor_window.window_closed.connect(self.on_monitor_closed)
            self.monitor_window.show()

            # Position the monitor window next to this control window
            control_geometry = self.geometry()
            monitor_x = control_geometry.x() + control_geometry.width() + 20
            monitor_y = control_geometry.y()
            self.monitor_window.setGeometry(monitor_x, monitor_y, 1400, 900)

            self.create_monitor_btn.setEnabled(False)
            self.start_session_btn.setEnabled(True)
            self.update_status("✅ Enhanced Live Scan Monitor window created with all features")
            print("✅ Enhanced Live Scan Monitor window created")
    
    def start_scanning_session(self):
        """Start a scanning session"""
        if self.monitor_window:
            test_url = "https://www.youtube.com/@MyFundedFuturesPropFirm"
            self.monitor_window.start_scanning_session(test_url)
            
            self.start_session_btn.setEnabled(False)
            self.simulate_codes_btn.setEnabled(True)
            self.stop_session_btn.setEnabled(True)
            
            # Start automatic simulation
            self.start_simulation()
            print("🚀 Scanning session started")
    
    def stop_scanning_session(self):
        """Stop the scanning session"""
        if self.monitor_window:
            self.monitor_window.stop_scanning_session()
            
            self.start_session_btn.setEnabled(True)
            self.simulate_codes_btn.setEnabled(False)
            self.stop_session_btn.setEnabled(False)
            
            # Stop simulation
            self.stop_simulation()
            print("⏹️ Scanning session stopped")
    
    def simulate_code_detection(self):
        """Simulate a single code detection"""
        if self.monitor_window:
            import random
            
            # Sample codes and types
            sample_codes = [
                {"code": "ABC123", "type": "Starter", "confidence": 0.95},
                {"code": "XYZ789", "type": "Expert", "confidence": 0.87},
                {"code": "RESET5", "type": "Free Reset Code", "confidence": 0.92},
                {"code": "PLUS99", "type": "Starter Plus", "confidence": 0.88},
                {"code": "INVALID", "type": "Unknown", "confidence": 0.3},  # Invalid code
            ]
            
            # Pick a random code
            code_data = random.choice(sample_codes)
            code_data['full_text'] = f"FREE 50 {code_data['type'].upper()}: {code_data['code']}"
            
            self.monitor_window.on_code_detected(code_data)
            print(f"🎯 Simulated code detection: {code_data['code']} ({code_data['type']})")
    
    def start_simulation(self):
        """Start automatic simulation of code detections"""
        self.simulation_timer = QTimer()
        self.simulation_timer.timeout.connect(self.simulate_code_detection)
        self.simulation_timer.start(3000)  # Simulate every 3 seconds
        print("🎭 Started automatic code detection simulation")
    
    def stop_simulation(self):
        """Stop automatic simulation"""
        if self.simulation_timer:
            self.simulation_timer.stop()
            self.simulation_timer = None
            print("⏸️ Stopped automatic simulation")
    
    def on_monitor_closed(self):
        """Handle monitor window being closed"""
        self.monitor_window = None
        self.create_monitor_btn.setEnabled(True)
        self.start_session_btn.setEnabled(False)
        self.simulate_codes_btn.setEnabled(False)
        self.stop_session_btn.setEnabled(False)
        self.stop_simulation()
        print("❌ Monitor window closed")


def main():
    """Main test function"""
    print("🧪 Starting Live Scan Monitor Test")
    print("=" * 50)
    
    app = QApplication(sys.argv)
    
    # Create test control window
    control_window = TestControlWindow()
    control_window.show()
    
    print("📋 Test Instructions:")
    print("1. Click 'Create Monitor Window' to open the Live Scan Monitor")
    print("2. Click 'Start Scanning Session' to begin monitoring")
    print("3. Click 'Simulate Code Detection' to test code detection")
    print("4. Watch the monitor window update in real-time")
    print("5. Click 'Stop Session' to end monitoring")
    print()
    print("The monitor window will automatically simulate code detections every 3 seconds")
    print("during an active scanning session.")
    
    # Run the application
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
