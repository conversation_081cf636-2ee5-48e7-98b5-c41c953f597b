# MFFUHijack Message Logger Guide

## Overview

The MFFUHijack application now includes a separate GUI message logger window that displays all application messages, warnings, errors, and status updates in real-time. This replaces the need for console output and provides a better user experience.

## Features

### Message Logger Window
- **Separate Window**: The message logger opens in its own window alongside the main application
- **Real-time Updates**: All messages are displayed immediately as they occur
- **Color Coding**: Different message types are color-coded for easy identification:
  - **INFO**: Default text color (black)
  - **STATUS**: Blue text
  - **SUCCESS**: Green text  
  - **WARNING**: Orange text
  - **ERROR**: Red text

### Message Filtering
The logger window includes checkboxes to filter which message types are displayed:
- ☑️ **INFO**: General information messages
- ☑️ **STATUS**: Status updates (bot starting/stopping, etc.)
- ☑️ **SUCCESS**: Success messages (code detection, completion, etc.)
- ☑️ **WARNING**: Warning messages
- ☑️ **ERROR**: Error messages

### Controls
- **Clear Messages**: Button to clear all messages from the display
- **Auto-scroll**: Checkbox to automatically scroll to the newest messages (enabled by default)
- **Show/Hide**: Use the View menu in the main window to show/hide the logger window

## Usage

### Opening the Message Logger
1. The message logger window opens automatically when you start MFFUHijack
2. If closed, you can reopen it via **View → Show Message Logger** in the main window menu
3. The window will position itself to the right of the main application window

### Viewing Messages
- All application activities are automatically logged to the message logger
- Messages include timestamps in HH:MM:SS format
- Use the filter checkboxes to show only specific message types
- The window auto-scrolls to show the latest messages (can be disabled)

### Managing Messages
- Click **Clear Messages** to remove all messages from the display
- Close the window using the X button or via the View menu
- The window remembers your filter settings during the session

## Message Types

### INFO Messages
- General application information
- Configuration updates
- Frame processing details
- Dataset building progress

### STATUS Messages  
- Bot status changes (Starting, Running, Stopping, Idle)
- System state updates
- Process initialization

### SUCCESS Messages
- Code detection events
- Successful operations completion
- Training completion
- Dataset building completion

### WARNING Messages
- Non-critical issues
- Performance warnings
- Configuration warnings

### ERROR Messages
- Failed operations
- Connection errors
- Processing errors
- Critical system issues

## Integration

The message logger is fully integrated with all MFFUHijack components:

- **Livestream Bot**: Logs all bot activities, code detections, and errors
- **Dataset Builder**: Logs frame collection progress and completion
- **Model Training**: Logs training progress and results
- **OCR Processing**: Logs OCR engine status and results
- **Configuration**: Logs setting changes and updates

## Technical Details

### Centralized Logging
- Uses a centralized `MessageLogger` class that emits Qt signals
- All GUI components use the same logger instance
- Messages are distributed to both the GUI and Python's logging system

### Performance
- Message display is limited to 1000 messages to prevent memory issues
- Older messages are automatically removed when the limit is reached
- Filtering is performed in real-time without affecting performance

### Customization
- Message colors can be customized by modifying the `MessageLoggerWindow` class
- Additional message types can be added by extending the `MessageLogger` class
- Filter options can be expanded as needed

## Troubleshooting

### Logger Window Not Appearing
- Check the View menu and ensure "Show Message Logger" is checked
- The window may be positioned off-screen; use View menu to show it again

### Messages Not Appearing
- Verify that the appropriate filter checkboxes are enabled
- Check if the message logger is enabled in the code

### Performance Issues
- If too many messages are being generated, consider adjusting the logging level
- Use the Clear Messages button periodically to free up memory

## Future Enhancements

Potential future improvements to the message logger:
- Save messages to log files
- Search functionality within messages
- Message export capabilities
- Custom message categories
- Message statistics and analytics
