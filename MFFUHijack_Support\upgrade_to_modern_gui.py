#!/usr/bin/env python3
"""
<PERSON><PERSON>t to upgrade MFFUHijack to use the modern GUI
Backs up the old GUI and replaces it with the new modern version
"""

import os
import shutil
from datetime import datetime


def backup_old_gui():
    """Backup the existing GUI file"""
    if os.path.exists('gui.py'):
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_name = f'gui_backup_{timestamp}.py'
        shutil.copy2('gui.py', backup_name)
        print(f"✅ Backed up existing GUI to: {backup_name}")
        return True
    else:
        print("ℹ️  No existing gui.py found - proceeding with fresh installation")
        return False


def install_modern_gui():
    """Install the modern GUI as the main GUI"""
    if os.path.exists('modern_gui.py'):
        shutil.copy2('modern_gui.py', 'gui.py')
        print("✅ Installed modern GUI as main GUI")
        return True
    else:
        print("❌ modern_gui.py not found!")
        return False


def update_main_py():
    """Update main.py to use the modern GUI classes"""
    if not os.path.exists('main.py'):
        print("⚠️  main.py not found - skipping update")
        return False
    
    try:
        # Read current main.py
        with open('main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Replace old GUI import and class names
        updated_content = content.replace(
            'from gui import MFFUHijackGUI',
            'from gui import ModernMFFUHijackGUI as MFFUHijackGUI'
        )
        
        # If no changes were made, add the import
        if updated_content == content:
            # Look for existing gui import and replace it
            if 'from gui import' in content:
                updated_content = content.replace(
                    'from gui import',
                    'from gui import ModernMFFUHijackGUI as MFFUHijackGUI, ModernMessageLoggerWindow, message_logger\n# Legacy import:'
                )
            else:
                # Add import at the top
                lines = content.split('\n')
                import_index = 0
                for i, line in enumerate(lines):
                    if line.startswith('import ') or line.startswith('from '):
                        import_index = i + 1
                
                lines.insert(import_index, 'from gui import ModernMFFUHijackGUI as MFFUHijackGUI, ModernMessageLoggerWindow, message_logger')
                updated_content = '\n'.join(lines)
        
        # Write updated content
        with open('main.py', 'w', encoding='utf-8') as f:
            f.write(updated_content)
        
        print("✅ Updated main.py to use modern GUI")
        return True
        
    except Exception as e:
        print(f"❌ Error updating main.py: {e}")
        return False


def create_modern_gui_readme():
    """Create README for the modern GUI upgrade"""
    readme_content = """# MFFUHijack Modern GUI Upgrade

## 🎨 What's New

The MFFUHijack interface has been completely redesigned with a modern, professional appearance:

### ✨ Key Improvements

1. **Modern Design Language**
   - Material Design inspired color scheme
   - Clean, professional appearance
   - Consistent typography and spacing

2. **Enhanced User Experience**
   - Better visual hierarchy
   - Intuitive icon usage
   - Responsive layout design

3. **Improved Message Logger**
   - Dark theme for better readability
   - Color-coded message types
   - Modern formatting and styling

4. **Professional Styling**
   - Rounded corners and modern borders
   - Hover effects and smooth transitions
   - Consistent button and input styling

### 🔧 Technical Improvements

- **Better Sizing**: Proper minimum sizes and responsive scaling
- **Modern Colors**: Professional color palette with good contrast
- **Clean Layout**: Improved spacing and organization
- **Enhanced Typography**: Better font choices and sizing

### 📁 Files

- `gui.py` - Main GUI file (now uses modern design)
- `gui_backup_YYYYMMDD_HHMMSS.py` - Backup of original GUI
- `modern_gui.py` - Source of modern GUI components
- `test_modern_gui.py` - Demo script for modern interface

### 🚀 Usage

The application works exactly the same as before, but with a much cleaner and more professional appearance. All functionality remains the same - only the visual design has been improved.

### 🔄 Rollback

If you need to revert to the old interface:
1. Delete `gui.py`
2. Rename your backup file (e.g., `gui_backup_20241206_143022.py`) to `gui.py`
3. Restart the application

The modern interface provides a much better user experience while maintaining all the original functionality!
"""
    
    with open('MODERN_GUI_README.md', 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("✅ Created modern GUI documentation")


def main():
    """Main upgrade process"""
    print("🎨 MFFUHijack Modern GUI Upgrade")
    print("=" * 50)
    print()
    print("This script will upgrade your MFFUHijack interface to use")
    print("the new modern, professional design.")
    print()
    
    # Confirm upgrade
    try:
        response = input("Continue with upgrade? (y/N): ").strip().lower()
        if response not in ['y', 'yes']:
            print("❌ Upgrade cancelled by user")
            return
    except KeyboardInterrupt:
        print("\n❌ Upgrade cancelled by user")
        return
    
    print("\n🚀 Starting upgrade process...")
    print()
    
    # Step 1: Backup existing GUI
    print("Step 1: Backing up existing GUI...")
    backup_success = backup_old_gui()
    
    # Step 2: Install modern GUI
    print("\nStep 2: Installing modern GUI...")
    install_success = install_modern_gui()
    
    if not install_success:
        print("❌ Failed to install modern GUI")
        return
    
    # Step 3: Update main.py
    print("\nStep 3: Updating main.py...")
    main_update_success = update_main_py()
    
    # Step 4: Create documentation
    print("\nStep 4: Creating documentation...")
    create_modern_gui_readme()
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 UPGRADE SUMMARY")
    print("=" * 50)
    
    if backup_success:
        print("✅ Original GUI backed up")
    
    if install_success:
        print("✅ Modern GUI installed")
    
    if main_update_success:
        print("✅ main.py updated")
    else:
        print("⚠️  main.py may need manual update")
    
    print("✅ Documentation created")
    
    if install_success:
        print("\n🎉 UPGRADE COMPLETE!")
        print()
        print("Your MFFUHijack now features:")
        print("  • Clean, modern interface design")
        print("  • Professional color scheme")
        print("  • Enhanced message logger")
        print("  • Better user experience")
        print()
        print("🚀 Run the application to see the new interface:")
        print("   python main.py")
        print()
        print("🧪 Or test the modern GUI separately:")
        print("   python test_modern_gui.py")
        print()
        print("📖 See MODERN_GUI_README.md for more details")
    else:
        print("\n❌ Upgrade failed - please check the errors above")


if __name__ == "__main__":
    main()
