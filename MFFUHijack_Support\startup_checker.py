"""
Startup Dependency Checker for MFFUHijack
Automatically checks and offers to install missing dependencies on first run
"""

import sys
import subprocess
import importlib
import os
import json
from pathlib import Path
from typing import Dict, List, Tuple, Optional


class StartupChecker:
    """Handles dependency checking and installation on startup"""
    
    def __init__(self):
        self.config_file = "startup_config.json"
        self.config = self.load_config()
        
    def load_config(self) -> Dict:
        """Load startup configuration"""
        default_config = {
            "first_run": True,
            "skip_dependency_check": False,
            "installed_optional": [],
            "last_check": None
        }
        
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r') as f:
                    config = json.load(f)
                    # Merge with defaults for any missing keys
                    for key, value in default_config.items():
                        if key not in config:
                            config[key] = value
                    return config
            except:
                pass
        
        return default_config
    
    def save_config(self):
        """Save startup configuration"""
        try:
            with open(self.config_file, 'w') as f:
                json.dump(self.config, f, indent=2)
        except Exception as e:
            print(f"Warning: Could not save startup config: {e}")
    
    def check_package(self, package_name: str, import_name: Optional[str] = None) -> bool:
        """Check if a package is installed"""
        if import_name is None:
            import_name = package_name

        try:
            importlib.import_module(import_name)
            return True
        except (ImportError, FileNotFoundError, OSError, Exception):
            # Handle various import errors
            return False
    
    def check_external_tool(self, tool_name: str, command: str) -> bool:
        """Check if an external tool is available"""
        try:
            result = subprocess.run(command.split(), 
                                  capture_output=True, 
                                  text=True, 
                                  timeout=10)
            return result.returncode == 0
        except (subprocess.TimeoutExpired, FileNotFoundError):
            return False
    
    def install_package(self, package_name: str) -> bool:
        """Install a package using pip"""
        try:
            print(f"Installing {package_name}...")
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", package_name
            ], stdout=subprocess.DEVNULL, stderr=subprocess.PIPE)
            print(f"✅ {package_name} installed successfully")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install {package_name}: {e.stderr.decode() if e.stderr else 'Unknown error'}")
            return False
    
    def get_missing_dependencies(self) -> Tuple[List[str], List[str], List[str]]:
        """Get lists of missing required, optional, and external dependencies"""
        
        # Required packages (already checked in main requirements.txt)
        required_packages = [
            ('PyQt6', 'PyQt6'),
            ('opencv-python', 'cv2'),
            ('numpy', 'numpy'),
            ('requests', 'requests'),
            ('Pillow', 'PIL'),
        ]
        
        # Optional packages for enhanced functionality
        optional_packages = [
            ('yt-dlp', 'yt_dlp'),
            ('easyocr', 'easyocr'),
            ('torch', 'torch'),
            ('torchvision', 'torchvision'),
        ]


        
        # External tools
        external_tools = [
            ('yt-dlp', 'yt-dlp --version'),
            ('ffmpeg', 'ffmpeg -version'),
        ]
        
        missing_required = []
        missing_optional = []
        missing_external = []
        
        # Check required packages
        for package_name, import_name in required_packages:
            if not self.check_package(package_name, import_name):
                missing_required.append(package_name)
        
        # Check optional packages
        for package_name, import_name in optional_packages:
            if not self.check_package(package_name, import_name):
                missing_optional.append(package_name)


        
        # Check external tools
        for tool_name, command in external_tools:
            if not self.check_external_tool(tool_name, command):
                missing_external.append(tool_name)
        
        return missing_required, missing_optional, missing_external
    
    def prompt_user_choice(self, prompt: str, default: str = 'n') -> bool:
        """Prompt user for yes/no choice"""
        try:
            choice = input(f"{prompt} (y/n, default {default}): ").lower().strip()
            if not choice:
                choice = default
            return choice in ['y', 'yes', '1', 'true']
        except (KeyboardInterrupt, EOFError):
            return False
    
    def show_dependency_info(self, missing_optional: List[str], missing_external: List[str]):
        """Show information about missing dependencies"""
        print("\n" + "="*60)
        print("🔍 DEPENDENCY CHECK")
        print("="*60)
        
        if missing_optional:
            print("\n📦 Missing Optional Python Packages:")
            for package in missing_optional:
                if package == 'yt-dlp':
                    print(f"   • {package} - Required for YouTube livestream processing")
                elif package == 'easyocr':
                    print(f"   • {package} - High-quality OCR engine (recommended)")

                elif package == 'torch':
                    print(f"   • {package} - Required for custom model training")
                elif package == 'torchvision':
                    print(f"   • {package} - Required for custom model training")
                else:
                    print(f"   • {package}")
        
        if missing_external:
            print("\n🛠️  Missing External Tools:")
            for tool in missing_external:
                if tool == 'yt-dlp':
                    print(f"   • {tool} - YouTube video downloader (install via pip)")
                elif tool == 'ffmpeg':
                    print(f"   • {tool} - Video processing tool")
                    print("     Windows: Download from https://ffmpeg.org/")
                    print("     macOS: brew install ffmpeg")
                    print("     Linux: sudo apt install ffmpeg")
        
        print("\n💡 Impact of missing dependencies:")
        if 'yt-dlp' in missing_optional or 'yt-dlp' in missing_external:
            print("   • Cannot process YouTube livestreams")
        if 'easyocr' in missing_optional:
            print("   • No OCR engines available - core functionality disabled")
        if 'torch' in missing_optional:
            print("   • Cannot train custom OCR models")
        if 'ffmpeg' in missing_external:
            print("   • Limited video processing capabilities")
    
    def install_optional_dependencies(self, missing_optional: List[str]) -> List[str]:
        """Install optional dependencies with user confirmation"""
        installed = []
        
        if not missing_optional:
            return installed
        
        print(f"\n🚀 Installing {len(missing_optional)} optional packages...")
        
        for package in missing_optional:
            if self.install_package(package):
                installed.append(package)
        
        return installed
    
    def run_startup_check(self, use_gui: bool = True) -> bool:
        """Run the complete startup check process"""

        # Skip if user has disabled checks
        if self.config.get("skip_dependency_check", False):
            return True

        print("🔍 Checking MFFUHijack dependencies...")

        missing_required, missing_optional, missing_external = self.get_missing_dependencies()

        # Critical error if required packages are missing
        if missing_required:
            print("\n❌ CRITICAL: Missing required packages!")
            print("Please install them first:")
            print(f"   pip install {' '.join(missing_required)}")
            return False

        # If this is first run or we have missing optional dependencies
        if self.config.get("first_run", True) or missing_optional or missing_external:

            if missing_optional or missing_external:

                # Try GUI installer first if available and requested
                if use_gui and missing_optional:
                    try:
                        from dependency_installer_gui import show_dependency_installer

                        # Create package descriptions
                        package_descriptions = {}
                        for package in missing_optional:
                            if package == 'yt-dlp':
                                package_descriptions[package] = "YouTube video processing (required for livestream capture)"
                            elif package == 'easyocr':
                                package_descriptions[package] = "High-quality OCR engine (recommended - works reliably)"

                            elif package == 'torch':
                                package_descriptions[package] = "PyTorch framework (required for custom model training)"
                            elif package == 'torchvision':
                                package_descriptions[package] = "Computer vision utilities (required for custom models)"
                            else:
                                package_descriptions[package] = f"Optional package: {package}"

                        print("\n🖥️  Opening GUI installer...")
                        if show_dependency_installer(package_descriptions):
                            print("✅ GUI installation completed")
                            # Re-check dependencies
                            _, missing_optional, missing_external = self.get_missing_dependencies()
                        else:
                            print("⚠️  GUI installation skipped")

                    except ImportError:
                        print("⚠️  GUI installer not available, falling back to console")
                        use_gui = False
                    except Exception as e:
                        print(f"⚠️  GUI installer failed: {e}")
                        use_gui = False

                # Fallback to console installer
                if not use_gui and missing_optional:
                    self.show_dependency_info(missing_optional, missing_external)

                    print(f"\n🤔 Would you like to install the missing Python packages?")
                    print("   This will enable full MFFUHijack functionality.")

                    if self.prompt_user_choice("Install optional packages?", "y"):
                        installed = self.install_optional_dependencies(missing_optional)
                        self.config["installed_optional"].extend(installed)

                        if installed:
                            print(f"\n✅ Installed {len(installed)} packages successfully!")

                        # Re-check after installation
                        _, remaining_optional, _ = self.get_missing_dependencies()
                        if remaining_optional:
                            print(f"\n⚠️  {len(remaining_optional)} packages still missing:")
                            for pkg in remaining_optional:
                                print(f"   • {pkg}")

                if missing_external:
                    print(f"\n🛠️  Installing external tools...")

                    # Special handling for yt-dlp
                    if 'yt-dlp' in missing_external:
                        try:
                            from yt_dlp_installer import install_yt_dlp_if_needed
                            print("📺 Installing yt-dlp with enhanced installer...")

                            if install_yt_dlp_if_needed(auto_download=True):
                                print("✅ yt-dlp installed successfully!")
                                missing_external.remove('yt-dlp')
                            else:
                                print("⚠️  yt-dlp installation needs manual completion")
                                print("   Please follow the instructions that were displayed")
                        except Exception as e:
                            print(f"⚠️  yt-dlp installer error: {e}")

                    # Handle remaining external tools
                    if missing_external:
                        print(f"\n📋 Remaining external tools need manual installation:")
                        for tool in missing_external:
                            if tool == 'ffmpeg':
                                print(f"   • {tool} - Video processing tool")
                                print("     Windows: Download from https://ffmpeg.org/")
                                print("     macOS: brew install ffmpeg")
                                print("     Linux: sudo apt install ffmpeg")
                            else:
                                print(f"   • {tool}")

                        print("\n💡 You can install these later and restart the application.")

                # Ask if user wants to skip future checks (only in console mode)
                if not use_gui:
                    print(f"\n⚙️  Future startup behavior:")
                    if self.prompt_user_choice("Skip dependency checks on future startups?", "n"):
                        self.config["skip_dependency_check"] = True
                        print("   Dependency checks disabled. Run 'python install.py' to check manually.")

            else:
                print("✅ All dependencies are available!")

            # Mark first run as complete
            self.config["first_run"] = False
            self.save_config()

        return True


def check_startup_dependencies(auto_install: bool = False, use_gui: bool = True) -> bool:
    """Main function to check dependencies on startup"""
    try:
        checker = StartupChecker()

        # If auto_install is enabled, try automatic installation first
        if auto_install:
            print("🚀 Running automatic dependency installation...")
            try:
                from auto_installer import run_auto_installer

                # Run automatic installation
                install_success = run_auto_installer(install_optional=True)

                if install_success:
                    print("✅ Automatic installation completed successfully!")
                    # Mark first run as complete
                    checker.config["first_run"] = False
                    checker.save_config()
                    return True
                else:
                    print("⚠️  Automatic installation had some issues, continuing with manual check...")

            except ImportError as e:
                print(f"⚠️  Automatic installer not available: {e}")
                print("Falling back to manual dependency check...")
            except Exception as e:
                print(f"⚠️  Automatic installation failed: {e}")
                print("Falling back to manual dependency check...")

        # Run normal startup check
        return checker.run_startup_check(use_gui=use_gui)

    except KeyboardInterrupt:
        print("\n\n⚠️  Startup check interrupted by user.")
        print("You can run the application anyway, but some features may not work.")
        return True
    except Exception as e:
        print(f"\n❌ Startup check failed: {e}")
        print("Continuing anyway - some features may not work.")
        return True


if __name__ == "__main__":
    # Allow running this script directly for testing
    success = check_startup_dependencies()
    if success:
        print("\n🎉 Startup check complete!")
    else:
        print("\n❌ Startup check failed!")
        sys.exit(1)
