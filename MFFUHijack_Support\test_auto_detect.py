#!/usr/bin/env python3
"""
Test script for the auto-detect livestream functionality
"""

import sys
from pathlib import Path

# Add current directory to path
sys.path.append(str(Path(__file__).parent))

from livestream_detector import LivestreamDetector, auto_detect_livestream


def test_channel_validation():
    """Test channel URL validation"""
    print("🧪 Testing Channel URL Validation")
    print("=" * 40)
    
    detector = LivestreamDetector()
    
    test_urls = [
        ("https://www.youtube.com/@MyFundedFuturesPropFirm", True),
        ("https://www.youtube.com/c/SomeChannel", True),
        ("https://www.youtube.com/channel/UC1234567890", True),
        ("https://www.youtube.com/user/SomeUser", True),
        ("https://invalid-url.com", False),
        ("not-a-url", False),
        ("", False)
    ]
    
    passed = 0
    for url, expected in test_urls:
        result = detector.validate_channel_url(url)
        status = "✅ PASS" if result == expected else "❌ FAIL"
        print(f"{status} {url} -> {result} (expected {expected})")
        if result == expected:
            passed += 1
    
    print(f"\nValidation tests: {passed}/{len(test_urls)} passed")
    return passed == len(test_urls)


def test_channel_info():
    """Test getting channel information"""
    print("\n🧪 Testing Channel Information")
    print("=" * 40)
    
    detector = LivestreamDetector()
    
    # Test with default channel
    channel_url = "https://www.youtube.com/@MyFundedFuturesPropFirm"
    print(f"Getting info for: {channel_url}")
    
    info = detector.get_channel_info(channel_url)
    
    if info:
        print("✅ Channel info retrieved:")
        for key, value in info.items():
            print(f"   {key}: {value}")
        return True
    else:
        print("❌ Failed to get channel info")
        return False


def test_livestream_detection():
    """Test livestream detection"""
    print("\n🧪 Testing Livestream Detection")
    print("=" * 40)
    
    # Test with default channel
    print("Testing with default MyFundedFuturesPropFirm channel...")
    stream_url = auto_detect_livestream()
    
    if stream_url:
        print(f"✅ Found livestream: {stream_url}")
        return True
    else:
        print("⚠️  No active livestream found (this may be normal if channel is not live)")
        return False


def test_mock_detection():
    """Test detection with a known channel that might have live content"""
    print("\n🧪 Testing with Alternative Channels")
    print("=" * 40)
    
    # Test channels that are more likely to have live content
    test_channels = [
        "https://www.youtube.com/@MyFundedFuturesPropFirm",
        "https://www.youtube.com/@NASA",  # NASA often has live streams
        "https://www.youtube.com/@SpaceX"  # SpaceX sometimes has live streams
    ]
    
    detector = LivestreamDetector()
    
    for channel in test_channels:
        print(f"\nTesting channel: {channel}")
        
        # Get basic info
        info = detector.get_channel_info(channel)
        if info:
            print(f"   Channel: {info.get('channel_name', 'Unknown')}")
        
        # Check for livestreams
        livestreams = detector.get_channel_livestreams(channel, max_results=5)
        
        if livestreams:
            print(f"   ✅ Found {len(livestreams)} active livestreams:")
            for stream in livestreams:
                print(f"      - {stream['title']}")
            return True
        else:
            print(f"   ❌ No active livestreams found")
    
    return False


def test_gui_integration():
    """Test that the GUI can import and use the detector"""
    print("\n🧪 Testing GUI Integration")
    print("=" * 40)
    
    try:
        # Test importing the detector in GUI context
        from gui import LivestreamTab
        print("✅ GUI can import LivestreamTab")
        
        # Test that the detector can be imported
        from livestream_detector import auto_detect_livestream
        print("✅ GUI can import auto_detect_livestream")
        
        # Test basic functionality
        detector = LivestreamDetector()
        is_valid = detector.validate_channel_url("https://www.youtube.com/@MyFundedFuturesPropFirm")
        print(f"✅ Detector validation works: {is_valid}")
        
        return True
        
    except Exception as e:
        print(f"❌ GUI integration error: {e}")
        return False


def main():
    """Run all tests"""
    print("🚀 Auto-Detect Livestream Test Suite")
    print("=" * 60)
    
    tests = [
        ("Channel URL Validation", test_channel_validation),
        ("Channel Information", test_channel_info),
        ("Livestream Detection", test_livestream_detection),
        ("Alternative Channels", test_mock_detection),
        ("GUI Integration", test_gui_integration)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            print(f"\n{'='*60}")
            print(f"Running: {test_name}")
            print(f"{'='*60}")
            
            if test_func():
                print(f"✅ {test_name}: PASSED")
                passed += 1
            else:
                print(f"❌ {test_name}: FAILED")
                
        except Exception as e:
            print(f"❌ {test_name}: ERROR - {e}")
    
    # Summary
    print(f"\n{'='*60}")
    print("📊 Test Results Summary")
    print(f"{'='*60}")
    print(f"Tests passed: {passed}/{total}")
    print(f"Success rate: {passed/total*100:.1f}%")
    
    if passed >= total - 1:  # Allow 1 failure (livestream detection may fail if no streams)
        print("🎉 Auto-detect feature is working correctly!")
        print("\n💡 Usage in GUI:")
        print("   1. Enter a YouTube channel URL in the 'Auto-Detect Channel' field")
        print("   2. Click '🔍 Auto-Detect Latest Stream' button")
        print("   3. If a livestream is found, it will populate the 'Livestream URL' field")
        print("   4. Click 'Start Bot' to begin monitoring")
        return True
    else:
        print("⚠️  Some tests failed. Check the detailed output above.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
