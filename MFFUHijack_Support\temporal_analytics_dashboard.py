"""
Temporal Analytics Dashboard for Enhanced Live Scan Monitor
Provides time-based analysis visualization and insights
"""

import sys
from datetime import datetime, timedelta
from typing import Dict, List
from collections import defaultdict
from PyQt6.QtWidgets import *
from PyQt6.QtCore import *
from PyQt6.QtGui import *
import json


class TimelineWidget(QWidget):
    """Custom widget for displaying detection timeline"""
    
    def __init__(self):
        super().__init__()
        self.setMinimumHeight(100)
        self.detections = []
        self.time_range = timedelta(hours=1)  # Default 1 hour view
        self.colors = {
            'Starter': QColor(76, 175, 80),      # Green
            'Expert': QColor(33, 150, 243),      # Blue
            'Reset': QColor(255, 152, 0),        # Orange
            'Starter Plus': QColor(156, 39, 176), # Purple
            'Free Reset Code': QColor(244, 67, 54), # Red
            'Invalid': QColor(158, 158, 158)     # Gray
        }
    
    def add_detection(self, timestamp: datetime, code_type: str, valid: bool):
        """Add a detection to the timeline"""
        self.detections.append({
            'timestamp': timestamp,
            'type': code_type if valid else 'Invalid',
            'valid': valid
        })
        
        # Keep only recent detections within time range
        cutoff_time = datetime.now() - self.time_range
        self.detections = [d for d in self.detections if d['timestamp'] > cutoff_time]
        
        self.update()
    
    def set_time_range(self, hours: int):
        """Set the time range for the timeline"""
        self.time_range = timedelta(hours=hours)
        
        # Filter existing detections
        cutoff_time = datetime.now() - self.time_range
        self.detections = [d for d in self.detections if d['timestamp'] > cutoff_time]
        
        self.update()
    
    def paintEvent(self, event):
        """Paint the timeline"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        rect = self.rect()
        margin = 10
        timeline_rect = rect.adjusted(margin, margin, -margin, -margin)
        
        # Draw background
        painter.fillRect(rect, QColor(250, 250, 250))
        
        # Draw timeline base
        painter.setPen(QPen(QColor(200, 200, 200), 2))
        painter.drawLine(timeline_rect.left(), timeline_rect.center().y(),
                        timeline_rect.right(), timeline_rect.center().y())
        
        if not self.detections:
            # Draw "No data" message
            painter.setPen(QColor(150, 150, 150))
            painter.drawText(timeline_rect, Qt.AlignmentFlag.AlignCenter, "No detections in time range")
            return
        
        # Calculate time bounds
        now = datetime.now()
        start_time = now - self.time_range
        
        # Draw time markers
        painter.setPen(QColor(150, 150, 150))
        font = QFont("Arial", 8)
        painter.setFont(font)
        
        # Draw hour markers
        for i in range(int(self.time_range.total_seconds() // 3600) + 1):
            marker_time = start_time + timedelta(hours=i)
            x = timeline_rect.left() + (i / (self.time_range.total_seconds() / 3600)) * timeline_rect.width()
            
            painter.drawLine(int(x), timeline_rect.top(), int(x), timeline_rect.bottom())
            painter.drawText(int(x) - 20, timeline_rect.bottom() + 15, 
                           marker_time.strftime("%H:%M"))
        
        # Draw detections
        for detection in self.detections:
            # Calculate position
            time_offset = (detection['timestamp'] - start_time).total_seconds()
            x_ratio = time_offset / self.time_range.total_seconds()
            x = timeline_rect.left() + x_ratio * timeline_rect.width()
            
            # Get color for detection type
            color = self.colors.get(detection['type'], QColor(100, 100, 100))
            
            # Draw detection marker
            painter.setBrush(QBrush(color))
            painter.setPen(QPen(color.darker(), 2))
            
            marker_size = 8
            marker_rect = QRect(int(x) - marker_size//2, 
                              timeline_rect.center().y() - marker_size//2,
                              marker_size, marker_size)
            painter.drawEllipse(marker_rect)


class HourlyDistributionChart(QWidget):
    """Widget for displaying hourly detection distribution"""
    
    def __init__(self):
        super().__init__()
        self.setMinimumHeight(200)
        self.hourly_data = defaultdict(int)
        self.max_count = 1
    
    def update_data(self, hourly_distribution: Dict[str, int]):
        """Update the hourly distribution data"""
        self.hourly_data = defaultdict(int)
        for hour_str, count in hourly_distribution.items():
            self.hourly_data[int(hour_str)] = count
        
        self.max_count = max(self.hourly_data.values()) if self.hourly_data else 1
        self.update()
    
    def paintEvent(self, event):
        """Paint the hourly distribution chart"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        rect = self.rect()
        margin = 30
        chart_rect = rect.adjusted(margin, margin, -margin, -margin)
        
        # Draw background
        painter.fillRect(rect, QColor(255, 255, 255))
        
        # Draw axes
        painter.setPen(QPen(QColor(100, 100, 100), 1))
        painter.drawLine(chart_rect.bottomLeft(), chart_rect.bottomRight())  # X-axis
        painter.drawLine(chart_rect.bottomLeft(), chart_rect.topLeft())      # Y-axis
        
        # Draw hour labels
        painter.setPen(QColor(80, 80, 80))
        font = QFont("Arial", 8)
        painter.setFont(font)
        
        bar_width = chart_rect.width() / 24
        
        for hour in range(24):
            x = chart_rect.left() + hour * bar_width + bar_width / 2
            
            # Draw hour label
            if hour % 3 == 0:  # Show every 3rd hour
                painter.drawText(int(x) - 10, chart_rect.bottom() + 20, f"{hour:02d}")
        
        # Draw bars
        if self.max_count > 0:
            for hour in range(24):
                count = self.hourly_data.get(hour, 0)
                if count > 0:
                    bar_height = (count / self.max_count) * chart_rect.height()
                    x = chart_rect.left() + hour * bar_width
                    
                    # Color based on activity level
                    if count >= self.max_count * 0.8:
                        color = QColor(244, 67, 54)    # High activity - Red
                    elif count >= self.max_count * 0.5:
                        color = QColor(255, 152, 0)    # Medium activity - Orange
                    else:
                        color = QColor(76, 175, 80)    # Low activity - Green
                    
                    painter.fillRect(int(x + 2), int(chart_rect.bottom() - bar_height),
                                   int(bar_width - 4), int(bar_height), color)
        
        # Draw title
        painter.setPen(QColor(50, 50, 50))
        title_font = QFont("Arial", 10, QFont.Weight.Bold)
        painter.setFont(title_font)
        painter.drawText(chart_rect.adjusted(0, -25, 0, 0), 
                        Qt.AlignmentFlag.AlignTop | Qt.AlignmentFlag.AlignHCenter,
                        "Detections by Hour of Day")


class TemporalAnalyticsDashboard(QMainWindow):
    """Main dashboard for temporal analytics"""
    
    def __init__(self, time_analyzer=None):
        super().__init__()
        self.time_analyzer = time_analyzer
        self.init_ui()
        
        # Update timer
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_analytics)
        self.update_timer.start(5000)  # Update every 5 seconds
    
    def init_ui(self):
        """Initialize the dashboard UI"""
        self.setWindowTitle("Temporal Analytics Dashboard")
        self.setMinimumSize(1000, 700)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # Header
        header_layout = QHBoxLayout()
        
        title_label = QLabel("📊 Temporal Analytics Dashboard")
        title_label.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        
        # Time range selector
        header_layout.addWidget(QLabel("Time Range:"))
        self.time_range_combo = QComboBox()
        self.time_range_combo.addItems(["1 Hour", "6 Hours", "12 Hours", "24 Hours"])
        self.time_range_combo.setCurrentText("1 Hour")
        self.time_range_combo.currentTextChanged.connect(self.on_time_range_changed)
        header_layout.addWidget(self.time_range_combo)
        
        # Refresh button
        refresh_btn = QPushButton("🔄 Refresh")
        refresh_btn.clicked.connect(self.update_analytics)
        header_layout.addWidget(refresh_btn)
        
        layout.addLayout(header_layout)
        
        # Main content
        content_splitter = QSplitter(Qt.Orientation.Vertical)
        
        # Top section - Timeline and stats
        top_widget = QWidget()
        top_layout = QVBoxLayout(top_widget)
        
        # Detection timeline
        timeline_group = QGroupBox("🕒 Detection Timeline")
        timeline_layout = QVBoxLayout(timeline_group)
        
        self.timeline_widget = TimelineWidget()
        timeline_layout.addWidget(self.timeline_widget)
        
        top_layout.addWidget(timeline_group)
        
        # Statistics summary
        stats_group = QGroupBox("📈 Quick Statistics")
        stats_layout = QGridLayout(stats_group)
        
        self.total_detections_label = QLabel("0")
        self.total_detections_label.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        stats_layout.addWidget(QLabel("Total Detections:"), 0, 0)
        stats_layout.addWidget(self.total_detections_label, 0, 1)
        
        self.detection_rate_label = QLabel("0.0/hour")
        stats_layout.addWidget(QLabel("Detection Rate:"), 0, 2)
        stats_layout.addWidget(self.detection_rate_label, 0, 3)
        
        self.peak_hour_label = QLabel("--")
        stats_layout.addWidget(QLabel("Peak Hour:"), 1, 0)
        stats_layout.addWidget(self.peak_hour_label, 1, 1)
        
        self.quiet_hour_label = QLabel("--")
        stats_layout.addWidget(QLabel("Quiet Hour:"), 1, 2)
        stats_layout.addWidget(self.quiet_hour_label, 1, 3)
        
        top_layout.addWidget(stats_group)
        content_splitter.addWidget(top_widget)
        
        # Bottom section - Charts
        bottom_widget = QWidget()
        bottom_layout = QHBoxLayout(bottom_widget)
        
        # Hourly distribution chart
        chart_group = QGroupBox("📊 Hourly Distribution")
        chart_layout = QVBoxLayout(chart_group)
        
        self.hourly_chart = HourlyDistributionChart()
        chart_layout.addWidget(self.hourly_chart)
        
        bottom_layout.addWidget(chart_group)
        
        # Pattern analysis
        pattern_group = QGroupBox("🔍 Pattern Analysis")
        pattern_layout = QVBoxLayout(pattern_group)
        
        self.pattern_text = QTextEdit()
        self.pattern_text.setMaximumHeight(200)
        self.pattern_text.setReadOnly(True)
        pattern_layout.addWidget(self.pattern_text)
        
        bottom_layout.addWidget(pattern_group)
        
        content_splitter.addWidget(bottom_widget)
        content_splitter.setSizes([300, 400])
        
        layout.addWidget(content_splitter)
    
    def set_time_analyzer(self, time_analyzer):
        """Set the time analyzer instance"""
        self.time_analyzer = time_analyzer
        self.update_analytics()
    
    def on_time_range_changed(self, range_text: str):
        """Handle time range change"""
        hours_map = {
            "1 Hour": 1,
            "6 Hours": 6,
            "12 Hours": 12,
            "24 Hours": 24
        }
        
        hours = hours_map.get(range_text, 1)
        self.timeline_widget.set_time_range(hours)
    
    def update_analytics(self):
        """Update all analytics displays"""
        if not self.time_analyzer:
            return
        
        try:
            # Get detection patterns
            patterns = self.time_analyzer.get_detection_patterns()
            
            # Update statistics
            self.total_detections_label.setText(str(patterns.get('total_detections', 0)))
            self.detection_rate_label.setText(f"{patterns.get('detection_frequency', 0):.1f}/hour")
            
            # Update peak and quiet hours
            peak_hours = patterns.get('peak_hours', [])
            quiet_periods = patterns.get('quiet_periods', [])
            
            self.peak_hour_label.setText(f"{peak_hours[0]}:00" if peak_hours else "--")
            self.quiet_hour_label.setText(f"{quiet_periods[0]}:00" if quiet_periods else "--")
            
            # Update hourly chart
            hourly_dist = patterns.get('hourly_distribution', {})
            self.hourly_chart.update_data(hourly_dist)
            
            # Update pattern analysis
            self.update_pattern_analysis(patterns)
            
        except Exception as e:
            print(f"Error updating analytics: {e}")
    
    def update_pattern_analysis(self, patterns: Dict):
        """Update pattern analysis text"""
        analysis_text = []
        
        # Detection frequency analysis
        freq = patterns.get('detection_frequency', 0)
        if freq > 10:
            analysis_text.append("🔥 High detection activity detected!")
        elif freq > 5:
            analysis_text.append("📈 Moderate detection activity")
        elif freq > 0:
            analysis_text.append("📊 Low detection activity")
        else:
            analysis_text.append("😴 No recent detection activity")
        
        # Peak hours analysis
        peak_hours = patterns.get('peak_hours', [])
        if peak_hours:
            peak_str = ", ".join([f"{h}:00" for h in peak_hours[:3]])
            analysis_text.append(f"⏰ Peak detection hours: {peak_str}")
        
        # Quiet periods analysis
        quiet_periods = patterns.get('quiet_periods', [])
        if quiet_periods:
            quiet_str = ", ".join([f"{h}:00" for h in quiet_periods[:3]])
            analysis_text.append(f"😴 Quiet periods: {quiet_str}")
        
        # Average interval analysis
        avg_interval = patterns.get('average_interval', 0)
        if avg_interval > 0:
            if avg_interval < 60:
                analysis_text.append(f"⚡ Fast detection rate: {avg_interval:.1f}s between detections")
            elif avg_interval < 300:
                analysis_text.append(f"📊 Normal detection rate: {avg_interval/60:.1f}m between detections")
            else:
                analysis_text.append(f"🐌 Slow detection rate: {avg_interval/60:.1f}m between detections")
        
        # Recommendations
        analysis_text.append("\n💡 Recommendations:")
        if freq < 1:
            analysis_text.append("• Consider adjusting OCR settings for better detection")
        if peak_hours:
            analysis_text.append(f"• Focus monitoring during peak hours: {', '.join([f'{h}:00' for h in peak_hours[:2]])}")
        if quiet_periods:
            analysis_text.append(f"• Use quiet periods for system maintenance: {', '.join([f'{h}:00' for h in quiet_periods[:2]])}")
        
        self.pattern_text.setPlainText("\n".join(analysis_text))
    
    def add_detection(self, timestamp: datetime, code_type: str, valid: bool):
        """Add a detection to the timeline"""
        self.timeline_widget.add_detection(timestamp, code_type, valid)
    
    def closeEvent(self, event):
        """Handle window close"""
        self.update_timer.stop()
        event.accept()


# Test function
if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    dashboard = TemporalAnalyticsDashboard()
    dashboard.show()
    
    sys.exit(app.exec())
