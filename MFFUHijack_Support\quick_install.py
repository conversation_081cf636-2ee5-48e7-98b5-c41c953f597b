#!/usr/bin/env python3
"""
Quick Install for MFFUHijack
Installs only the most reliable and essential packages
"""

import sys
import subprocess
import importlib


def check_package(package_name, import_name=None):
    """Check if a package is installed"""
    if import_name is None:
        import_name = package_name
    
    try:
        importlib.import_module(import_name)
        return True
    except ImportError:
        return False


def install_package(package_name):
    """Install a package using pip"""
    try:
        print(f"Installing {package_name}...")
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", package_name
        ], stdout=subprocess.DEVNULL, stderr=subprocess.PIPE)
        print(f"✅ {package_name} installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install {package_name}")
        if e.stderr:
            error_msg = e.stderr.decode().strip()
            if len(error_msg) > 200:
                error_msg = error_msg[:200] + "..."
            print(f"   Error: {error_msg}")
        return False


def main():
    """Install essential packages for MFFUHijack"""
    print("🚀 MFFUHijack Quick Install")
    print("=" * 40)
    print("Installing only the most reliable packages...")
    print()
    
    # Essential packages that work reliably on most systems
    essential_packages = [
        ('yt-dlp', 'yt_dlp', 'YouTube video processing'),
        ('easyocr', 'easyocr', 'High-quality OCR engine'),
    ]
    
    # Optional packages for advanced features
    optional_packages = [
        ('torch', 'torch', 'PyTorch for custom model training'),
        ('torchvision', 'torchvision', 'Computer vision utilities'),
    ]
    
    installed_count = 0
    total_packages = len(essential_packages) + len(optional_packages)
    
    print("📦 Installing essential packages...")
    for package_name, import_name, description in essential_packages:
        if check_package(package_name, import_name):
            print(f"✅ {package_name} - Already installed")
            installed_count += 1
        else:
            print(f"📥 {package_name} - {description}")
            if install_package(package_name):
                installed_count += 1
    
    print("\n📦 Installing optional packages...")
    print("(You can skip these if you don't need custom model training)")
    
    for package_name, import_name, description in optional_packages:
        if check_package(package_name, import_name):
            print(f"✅ {package_name} - Already installed")
            installed_count += 1
        else:
            print(f"📥 {package_name} - {description}")
            try:
                choice = input(f"Install {package_name}? (y/n, default n): ").lower().strip()
                if choice in ['y', 'yes']:
                    if install_package(package_name):
                        installed_count += 1
                else:
                    print(f"⏭️  Skipped {package_name}")
            except (KeyboardInterrupt, EOFError):
                print(f"\n⏭️  Skipped {package_name}")
    
    print("\n" + "=" * 40)
    print("📊 INSTALLATION SUMMARY")
    print("=" * 40)
    print(f"Packages available: {installed_count}/{total_packages}")
    
    # Check what's working
    print("\n🔍 Checking functionality...")
    
    if check_package('yt-dlp', 'yt_dlp'):
        print("✅ YouTube livestream processing - Available")
    else:
        print("❌ YouTube livestream processing - Not available")
        print("   Install with: pip install yt-dlp")
    
    if check_package('easyocr'):
        print("✅ OCR text recognition - Available")
    else:
        print("❌ OCR text recognition - Not available")
        print("   Install with: pip install easyocr")
    
    if check_package('torch'):
        print("✅ Custom model training - Available")
    else:
        print("⚠️  Custom model training - Not available")
        print("   Install with: pip install torch torchvision")
    
    # PaddlePaddle status
    if check_package('paddleocr'):
        print("✅ PaddleOCR engine - Available")
    else:
        print("ℹ️  PaddleOCR engine - Not installed")
        print("   Note: PaddleOCR has known installation issues on Windows")
        print("   EasyOCR is recommended and provides similar functionality")
    
    print("\n🎯 NEXT STEPS")
    print("=" * 40)
    
    if check_package('yt-dlp', 'yt_dlp') and check_package('easyocr'):
        print("🎉 You're ready to use MFFUHijack!")
        print("Start the application with:")
        print("   python main.py")
        print("   or")
        print("   python launch.py")
    else:
        print("⚠️  Some essential packages are missing.")
        print("MFFUHijack will have limited functionality.")
        print("\nYou can:")
        print("1. Install missing packages manually:")
        if not check_package('yt-dlp', 'yt_dlp'):
            print("   pip install yt-dlp")
        if not check_package('easyocr'):
            print("   pip install easyocr")
        print("2. Run the application anyway (some features won't work)")
        print("3. Try the full installer: python launch.py")
    
    print("\n💡 TIPS")
    print("=" * 40)
    print("• EasyOCR works reliably on all systems")
    print("• yt-dlp is essential for YouTube livestream processing")
    print("• PaddleOCR can be skipped if installation fails")
    print("• Custom model training requires PyTorch")
    print("• You can always install more packages later")
    
    return installed_count > 0


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\nInstallation interrupted by user")
    except Exception as e:
        print(f"\nInstallation failed: {e}")
        import traceback
        traceback.print_exc()
