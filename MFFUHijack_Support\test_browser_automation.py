#!/usr/bin/env python3
"""
Test script for browser automation functionality
Tests the My Funded Futures website automation without requiring full GUI
"""

import sys
import os
import time
from datetime import datetime

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_browser_automation():
    """Test browser automation functionality"""
    print("🧪 Testing Browser Automation for MFFUHijack")
    print("=" * 50)
    
    try:
        from browser_automation import browser_automation, SELENIUM_AVAILABLE
        
        if not SELENIUM_AVAILABLE:
            print("❌ Selenium not available. Please install with:")
            print("   pip install selenium")
            return False
            
        print("✅ Browser automation module imported successfully")
        
        # Test browser initialization
        print("\n🌐 Testing browser initialization...")
        success = browser_automation.initialize_browser()
        if success:
            print("✅ Browser initialized successfully")
        else:
            print("❌ Browser initialization failed")
            return False
            
        # Test login page navigation
        print("\n🔐 Testing login page navigation...")
        try:
            browser_automation.driver.get("https://myfundedfutures.com/login")
            time.sleep(2)
            
            # Check if page loaded
            if "login" in browser_automation.driver.current_url.lower():
                print("✅ Login page loaded successfully")
            else:
                print("❌ Failed to load login page")
                
        except Exception as e:
            print(f"❌ Login page navigation error: {str(e)}")
            
        # Test element detection
        print("\n🔍 Testing element detection...")
        try:
            # Look for username field
            from selenium.webdriver.common.by import By
            username_fields = browser_automation.driver.find_elements(
                By.XPATH,
                "//input[@placeholder='Username']"
            )
            if username_fields:
                print("✅ Username field found")
            else:
                print("⚠️ Username field not found (page may have changed)")

            # Look for password field
            password_fields = browser_automation.driver.find_elements(
                By.XPATH,
                "//input[@placeholder='Password']"
            )
            if password_fields:
                print("✅ Password field found")
            else:
                print("⚠️ Password field not found (page may have changed)")
                
        except Exception as e:
            print(f"❌ Element detection error: {str(e)}")
            
        # Test visibility toggle
        print("\n👁️ Testing browser visibility...")
        browser_automation.set_visibility(False)
        print("✅ Browser set to hidden mode")
        
        browser_automation.set_visibility(True)
        print("✅ Browser set to visible mode")
        
        # Keep browser open for manual inspection
        print("\n⏳ Browser will remain open for 10 seconds for manual inspection...")
        time.sleep(10)
        
        # Cleanup
        print("\n🧹 Cleaning up...")
        browser_automation.close_browser()
        print("✅ Browser closed successfully")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {str(e)}")
        print("Please ensure all dependencies are installed:")
        print("   pip install selenium")
        return False
        
    except Exception as e:
        print(f"❌ Unexpected error: {str(e)}")
        return False

def test_url_mapping():
    """Test URL mapping for different account types"""
    print("\n🔗 Testing URL mapping...")
    
    try:
        from browser_automation import MFF_URLS
        
        expected_urls = {
            "login": "https://myfundedfutures.com/login",
            "stats": "https://myfundedfutures.com/stats",
            "starter_plus": "https://myfundedfutures.com/challenge?id=39&platform=Tradovate",
            "starter": "https://myfundedfutures.com/challenge?id=40&platform=Tradovate",
            "expert": "https://myfundedfutures.com/challenge?id=43&platform=Tradovate"
        }
        
        for account_type, expected_url in expected_urls.items():
            if account_type in MFF_URLS and MFF_URLS[account_type] == expected_url:
                print(f"✅ {account_type}: {expected_url}")
            else:
                print(f"❌ {account_type}: URL mismatch")
                
        return True
        
    except Exception as e:
        print(f"❌ URL mapping test error: {str(e)}")
        return False

def test_mock_code_submission():
    """Test code submission logic without actual browser interaction"""
    print("\n🎯 Testing mock code submission logic...")
    
    try:
        from browser_automation import browser_automation
        
        # Test different account types
        test_cases = [
            ("ABC123", "Starter", "123"),
            ("DEF456", "Starter Plus", "456"),
            ("GHI789", "Expert", "789"),
            ("RST999", "Reset", "999", "ACC12345")
        ]
        
        for test_case in test_cases:
            code = test_case[0]
            account_type = test_case[1]
            cvv = test_case[2]
            reset_account = test_case[3] if len(test_case) > 3 else None
            
            print(f"📝 Test case: {code} for {account_type}")
            
            # This would normally submit, but we're just testing the logic
            if account_type.lower() == "reset" and not reset_account:
                print("❌ Reset account validation failed (expected)")
            else:
                print("✅ Validation passed")
                
        return True
        
    except Exception as e:
        print(f"❌ Mock submission test error: {str(e)}")
        return False

def main():
    """Main test function"""
    print(f"🚀 Starting Browser Automation Tests - {datetime.now()}")
    print()
    
    # Test URL mapping first (doesn't require browser)
    url_test = test_url_mapping()
    
    # Test mock submission logic
    mock_test = test_mock_code_submission()
    
    # Ask user if they want to run browser tests
    print("\n" + "="*50)
    print("⚠️  BROWSER TEST WARNING")
    print("The next test will open a real browser window.")
    print("Make sure you have Chrome installed and are connected to the internet.")
    
    response = input("\nDo you want to run browser tests? (y/N): ").strip().lower()
    
    browser_test = True
    if response in ['y', 'yes']:
        browser_test = test_browser_automation()
    else:
        print("⏭️ Skipping browser tests")
    
    # Summary
    print("\n" + "="*50)
    print("📊 TEST SUMMARY")
    print(f"URL Mapping: {'✅ PASS' if url_test else '❌ FAIL'}")
    print(f"Mock Submission: {'✅ PASS' if mock_test else '❌ FAIL'}")
    print(f"Browser Tests: {'✅ PASS' if browser_test else '⏭️ SKIPPED'}")
    
    if url_test and mock_test and browser_test:
        print("\n🎉 All tests passed!")
        return True
    else:
        print("\n⚠️ Some tests failed or were skipped")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
