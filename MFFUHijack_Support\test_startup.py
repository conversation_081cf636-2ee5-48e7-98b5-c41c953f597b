#!/usr/bin/env python3
"""
Quick startup test for MFFUHijack
Tests basic imports and initialization without GUI
"""

import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test that all modules can be imported"""
    print("Testing imports...")
    
    try:
        print("  Importing ocr_utils...", end=" ")
        from ocr_utils import ocr_manager
        print("✓")
        
        print("  Importing submitter...", end=" ")
        from submitter import code_submitter
        print("✓")
        
        print("  Importing live_bot...", end=" ")
        from live_bot import LiveBot
        print("✓")
        
        print("  Importing dataset_builder...", end=" ")
        from dataset_builder import DatasetBuilder
        print("✓")
        
        print("  Importing model_trainer...", end=" ")
        from model_trainer import ModelTrainer
        print("✓")
        
        print("  Importing gui...", end=" ")
        from gui import MFFUHijackGUI
        print("✓")
        
        return True
        
    except Exception as e:
        print(f"✗ Import failed: {e}")
        return False

def test_ocr_engines():
    """Test OCR engine initialization"""
    print("\nTesting OCR engines...")
    
    try:
        from ocr_utils import ocr_manager
        
        available_engines = ocr_manager.get_available_engines()
        print(f"  Available engines: {available_engines}")
        
        if not available_engines:
            print("  ⚠️  No OCR engines available")
            return False
        
        # Test switching engines
        for engine in available_engines:
            success = ocr_manager.set_engine(engine)
            status = "✓" if success else "✗"
            print(f"  {engine}: {status}")
        
        return True
        
    except Exception as e:
        print(f"  ✗ OCR engine test failed: {e}")
        return False

def test_gui_creation():
    """Test GUI creation without showing"""
    print("\nTesting GUI creation...")
    
    try:
        from PyQt6.QtWidgets import QApplication
        from gui import MFFUHijackGUI
        
        # Create QApplication if it doesn't exist
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # Create GUI window (but don't show it)
        window = MFFUHijackGUI()
        print("  GUI creation: ✓")
        
        # Test that tabs are created
        if hasattr(window, 'livestream_tab') and hasattr(window, 'dataset_tab'):
            print("  Tabs created: ✓")
        else:
            print("  Tabs created: ✗")
            return False
        
        # Clean up
        window.close()
        
        return True
        
    except Exception as e:
        print(f"  ✗ GUI creation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run startup tests"""
    print("=" * 50)
    print("MFFUHijack Startup Test")
    print("=" * 50)
    
    tests = [
        ("Module Imports", test_imports),
        ("OCR Engines", test_ocr_engines),
        ("GUI Creation", test_gui_creation),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        try:
            if test_func():
                passed += 1
                print(f"  Result: ✓ PASSED")
            else:
                print(f"  Result: ✗ FAILED")
        except Exception as e:
            print(f"  Result: ✗ ERROR - {e}")
    
    print("\n" + "=" * 50)
    print(f"Test Results: {passed}/{total} passed")
    
    if passed == total:
        print("🎉 All tests passed! MFFUHijack should start correctly.")
        print("Run 'python main.py' to start the application.")
    else:
        print("⚠️  Some tests failed. Check the errors above.")
        print("You may need to install missing dependencies.")
    
    print("=" * 50)
    
    return passed == total

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\nTest interrupted by user")
    except Exception as e:
        print(f"\nTest failed with error: {e}")
        import traceback
        traceback.print_exc()
