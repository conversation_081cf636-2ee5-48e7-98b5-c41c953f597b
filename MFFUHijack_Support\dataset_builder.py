"""
Dataset Builder for MFFUHijack
Extracts training data from past YouTube livestreams
"""

import os
import cv2
import subprocess
import json
import time
from datetime import datetime
from typing import List, Dict, Any, Optional
import logging

from PyQt6.QtCore import QThread, pyqtSignal

# Import our modules
from ocr_utils import ocr_manager

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class DatasetBuilderWorker(QThread):
    """Worker thread for dataset building"""
    
    # Signals
    progress_update = pyqtSignal(int)      # Progress percentage
    status_update = pyqtSignal(str)        # Status message
    frame_saved = pyqtSignal(str, str)     # filename, code
    error_occurred = pyqtSignal(str)       # Error message
    finished = pyqtSignal(int)             # Total frames collected
    
    def __init__(self):
        super().__init__()
        self.channel_url = ""
        self.target_frames = 50
        self.region = "bottom_third"
        self.is_running = False
        
        # Paths
        self.frames_dir = "ocr_training_data/frames"
        self.log_file = "ocr_training_data/log.txt"
        
        # Ensure directories exist
        os.makedirs(self.frames_dir, exist_ok=True)
    
    def configure(self, channel_url: str, target_frames: int, region: str):
        """Configure the dataset builder"""
        self.channel_url = channel_url
        self.target_frames = target_frames
        self.region = region
    
    def run(self):
        """Main dataset building process"""
        self.is_running = True
        collected_frames = 0
        
        try:
            # Get list of livestream videos from channel
            self.status_update.emit("Fetching channel videos...")
            video_urls = self.get_channel_livestreams()
            
            if not video_urls:
                self.error_occurred.emit("No livestream videos found in channel")
                return
            
            self.status_update.emit(f"Found {len(video_urls)} livestream videos")
            
            # Process each video
            for i, video_url in enumerate(video_urls):
                if not self.is_running or collected_frames >= self.target_frames:
                    break
                
                self.status_update.emit(f"Processing video {i+1}/{len(video_urls)}")
                
                # Extract frames from this video
                frames_from_video = self.extract_frames_from_video(
                    video_url, 
                    self.target_frames - collected_frames
                )
                
                collected_frames += frames_from_video
                
                # Update progress
                progress = min(100, int((collected_frames / self.target_frames) * 100))
                self.progress_update.emit(progress)
                
                if collected_frames >= self.target_frames:
                    break
            
            self.status_update.emit(f"Dataset building complete! Collected {collected_frames} frames")
            self.finished.emit(collected_frames)
        
        except Exception as e:
            logger.error(f"Dataset building error: {e}")
            self.error_occurred.emit(f"Dataset building failed: {str(e)}")
    
    def get_channel_livestreams(self) -> List[str]:
        """Get list of livestream video URLs from YouTube channel"""
        try:
            # Use yt-dlp to get channel videos (use python -m for better compatibility)
            cmd = [
                'python', '-m', 'yt_dlp',
                '--flat-playlist',
                '--print', 'url',
                '--match-filter', 'was_live',  # Only get livestreams
                self.channel_url
            ]

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)
            
            if result.returncode == 0:
                urls = [line.strip() for line in result.stdout.split('\n') if line.strip()]
                logger.info(f"Found {len(urls)} livestream videos")
                return urls[:20]  # Limit to first 20 videos to avoid excessive processing
            else:
                logger.error(f"yt-dlp channel fetch failed: {result.stderr}")
                return []
        
        except subprocess.TimeoutExpired:
            logger.error("yt-dlp channel fetch timeout")
            return []
        except Exception as e:
            logger.error(f"Failed to get channel videos: {e}")
            return []
    
    def extract_frames_from_video(self, video_url: str, max_frames: int) -> int:
        """Extract frames with codes from a single video"""
        frames_collected = 0
        
        try:
            # Get video info
            video_info = self.get_video_info(video_url)
            if not video_info:
                return 0
            
            video_title = video_info.get('title', 'Unknown')
            self.status_update.emit(f"Processing: {video_title[:50]}...")
            
            # Get direct video URL
            direct_url = self.get_direct_video_url(video_url)
            if not direct_url:
                logger.warning(f"Could not get direct URL for {video_url}")
                return 0
            
            # Open video with OpenCV
            cap = cv2.VideoCapture(direct_url)
            if not cap.isOpened():
                logger.warning(f"Could not open video: {video_url}")
                return 0
            
            # Get video properties
            fps = cap.get(cv2.CAP_PROP_FPS) or 30
            
            # Sample frames every 1-2 seconds
            frame_interval = max(1, int(fps * 1.5))  # Every 1.5 seconds
            
            frame_count = 0
            processed_count = 0
            
            while frames_collected < max_frames and self.is_running:
                ret, frame = cap.read()
                if not ret:
                    break
                
                frame_count += 1
                
                # Skip frames based on interval
                if frame_count % frame_interval != 0:
                    continue
                
                processed_count += 1
                
                # Process frame for codes
                codes = ocr_manager.process_frame_for_codes(frame, self.region)
                
                if codes:
                    # Save frame with detected code
                    for code_data in codes:
                        if frames_collected >= max_frames:
                            break
                        
                        # Generate filename
                        timestamp = int(time.time())
                        filename = f"frame_{timestamp}_{frames_collected:03d}.jpg"
                        filepath = os.path.join(self.frames_dir, filename)
                        
                        # Crop to region of interest
                        cropped_frame = ocr_manager.crop_region_of_interest(frame, self.region)
                        
                        # Save cropped frame
                        if cv2.imwrite(filepath, cropped_frame):
                            frames_collected += 1
                            
                            # Log the frame
                            self.log_training_frame(
                                filename, video_title, video_url, 
                                code_data['code'], code_data['confidence']
                            )
                            
                            # Emit signal
                            self.frame_saved.emit(filename, code_data['code'])
                            
                            logger.info(f"Saved frame {filename} with code: {code_data['code']}")
                
                # Limit processing time per video (max 5 minutes of processing)
                if processed_count > 300:  # ~5 minutes at 1.5s intervals
                    break
            
            cap.release()
            
        except Exception as e:
            logger.error(f"Error processing video {video_url}: {e}")
        
        return frames_collected
    
    def get_video_info(self, video_url: str) -> Optional[Dict[str, Any]]:
        """Get video metadata"""
        try:
            cmd = [
                'python', '-m', 'yt_dlp',
                '--dump-json',
                '--no-download',
                video_url
            ]

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                return json.loads(result.stdout)
            else:
                return None
        
        except Exception as e:
            logger.error(f"Failed to get video info: {e}")
            return None
    
    def get_direct_video_url(self, video_url: str) -> Optional[str]:
        """Get direct video stream URL"""
        try:
            cmd = [
                'python', '-m', 'yt_dlp',
                '--get-url',
                '--format', 'best[height<=480]',  # Lower quality for faster processing
                video_url
            ]

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0 and result.stdout.strip():
                return result.stdout.strip()
            else:
                return None
        
        except Exception as e:
            logger.error(f"Failed to get direct video URL: {e}")
            return None
    
    def log_training_frame(self, filename: str, video_title: str, video_url: str, 
                          code: str, confidence: float):
        """Log training frame information"""
        try:
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            log_entry = f"{timestamp},{filename},{video_title},{video_url},{code},{confidence:.3f}\n"
            
            with open(self.log_file, 'a', encoding='utf-8') as f:
                f.write(log_entry)
        
        except Exception as e:
            logger.error(f"Failed to log training frame: {e}")
    
    def stop(self):
        """Stop the dataset building process"""
        self.is_running = False


class DatasetBuilder:
    """Main dataset builder controller"""
    
    def __init__(self):
        self.worker = None
        self.progress_callback = None
        self.status_callback = None
        self.frame_callback = None
        self.error_callback = None
        self.finished_callback = None
    
    def set_callbacks(self, progress_cb=None, status_cb=None, frame_cb=None, 
                     error_cb=None, finished_cb=None):
        """Set callback functions for dataset builder events"""
        self.progress_callback = progress_cb
        self.status_callback = status_cb
        self.frame_callback = frame_cb
        self.error_callback = error_cb
        self.finished_callback = finished_cb
    
    def start(self, channel_url: str, target_frames: int = 50, region: str = "bottom_third"):
        """Start dataset building"""
        if self.worker and self.worker.isRunning():
            logger.warning("Dataset builder is already running")
            return False
        
        # Create new worker
        self.worker = DatasetBuilderWorker()
        self.worker.configure(channel_url, target_frames, region)
        
        # Connect signals
        if self.progress_callback:
            self.worker.progress_update.connect(self.progress_callback)
        if self.status_callback:
            self.worker.status_update.connect(self.status_callback)
        if self.frame_callback:
            self.worker.frame_saved.connect(self.frame_callback)
        if self.error_callback:
            self.worker.error_occurred.connect(self.error_callback)
        if self.finished_callback:
            self.worker.finished.connect(self.finished_callback)
        
        # Start worker
        self.worker.start()
        logger.info(f"Started dataset builder for: {channel_url}")
        return True
    
    def stop(self):
        """Stop dataset building"""
        if self.worker:
            self.worker.stop()
            self.worker.wait()
            self.worker = None
            logger.info("Stopped dataset builder")
    
    def is_running(self) -> bool:
        """Check if dataset builder is currently running"""
        return self.worker is not None and self.worker.isRunning()
    
    def get_dataset_stats(self) -> Dict[str, Any]:
        """Get statistics about the current dataset"""
        frames_dir = "ocr_training_data/frames"
        log_file = "ocr_training_data/log.txt"
        
        stats = {
            'total_frames': 0,
            'log_entries': 0,
            'dataset_size_mb': 0
        }
        
        try:
            # Count frames
            if os.path.exists(frames_dir):
                frame_files = [f for f in os.listdir(frames_dir) if f.endswith('.jpg')]
                stats['total_frames'] = len(frame_files)
                
                # Calculate total size
                total_size = sum(
                    os.path.getsize(os.path.join(frames_dir, f)) 
                    for f in frame_files
                )
                stats['dataset_size_mb'] = total_size / (1024 * 1024)
            
            # Count log entries
            if os.path.exists(log_file):
                with open(log_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    stats['log_entries'] = len([l for l in lines if ',' in l])
        
        except Exception as e:
            logger.error(f"Error getting dataset stats: {e}")
        
        return stats
