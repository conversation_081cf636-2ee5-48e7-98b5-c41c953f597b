# MFFUHijack - Enhanced Startup System

## 🎯 Overview

I've implemented a comprehensive startup system that automatically checks for and installs missing dependencies when <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> first starts up. This ensures users have the best possible experience without manual dependency management.

## 🚀 New Components

### 1. **Startup Checker** (`startup_checker.py`)
- **Automatic Dependency Detection**: Scans for required and optional packages
- **Smart Configuration**: Remembers user preferences and previous installations
- **Multi-Engine Support**: Handles different package installation scenarios
- **External Tool Detection**: Checks for yt-dlp, FFmpeg, etc.

### 2. **GUI Dependency Installer** (`dependency_installer_gui.py`)
- **User-Friendly Interface**: PyQt6-based installer with checkboxes and descriptions
- **Categorized Packages**: Groups dependencies by functionality (Core, Advanced OCR, ML)
- **Real-Time Progress**: Shows installation progress with detailed logging
- **Threaded Installation**: Non-blocking installation process

### 3. **Enhanced Launcher** (`launch.py`)
- **Command-Line Options**: Multiple startup modes and configurations
- **Flexible Startup**: Can skip dependency checks or use console-only mode
- **Error Handling**: Graceful fallbacks and user-friendly error messages

### 4. **Updated Platform Scripts**
- **`run.bat`** (Windows): Enhanced batch file with better error handling
- **`run.sh`** (Linux/macOS): Improved shell script with multiple Python detection

## 🔧 How It Works

### First-Time Startup Flow
1. **Launch Application**: User runs `python launch.py` or double-clicks `run.bat`
2. **Dependency Check**: System scans for missing packages
3. **GUI Installer**: If missing packages found, shows user-friendly installer
4. **Package Selection**: User selects which packages to install
5. **Automatic Installation**: Packages installed in background with progress display
6. **Application Start**: Once dependencies satisfied, main application launches

### Subsequent Startups
- **Quick Check**: Minimal dependency verification
- **Skip Option**: User can disable future dependency checks
- **Direct Launch**: Faster startup for users with complete installations

## 📦 Package Categories

### Core Functionality
- **yt-dlp**: YouTube video processing (essential for livestream capture)
- **easyocr**: High-quality OCR engine (recommended for most users)

### Advanced OCR
- **paddlepaddle**: Backend for PaddleOCR engine
- **paddleocr**: Fast OCR with multi-language support

### Machine Learning
- **torch**: PyTorch framework (required for custom model training)
- **torchvision**: Computer vision utilities (required for custom models)

## 🎮 Usage Options

### Recommended Usage
```bash
python launch.py                    # Full startup with GUI installer
```
or simply double-click `run.bat` (Windows) or `run.sh` (Unix)

### Advanced Options
```bash
python launch.py --help                    # Show all options
python launch.py --no-gui-installer        # Use console-only installer
python launch.py --skip-deps               # Skip dependency check entirely
python launch.py --install-only            # Only install dependencies, don't start app
```

### Direct Usage (Skip Dependency Check)
```bash
python main.py                      # Start app directly
```

## 🔍 Smart Features

### Configuration Management
- **Persistent Settings**: Remembers user choices in `startup_config.json`
- **First-Run Detection**: Special handling for initial setup
- **Skip Options**: Users can disable future dependency checks

### Error Handling
- **Graceful Fallbacks**: Console installer if GUI fails
- **Version Compatibility**: Handles different package versions (e.g., PaddleOCR)
- **Network Issues**: Timeout handling and retry options

### User Experience
- **Progress Feedback**: Real-time installation progress
- **Package Descriptions**: Clear explanations of what each package does
- **Impact Information**: Shows what features will be limited without packages

## 📋 Installation Process

### GUI Installer Features
- **Visual Package Selection**: Checkboxes with descriptions
- **Categorized Display**: Packages grouped by functionality
- **Real-Time Progress**: Live installation logs and progress bars
- **Success/Failure Indicators**: Clear visual feedback for each package
- **Retry Options**: Can retry failed installations

### Console Installer Features
- **Text-Based Interface**: Works in any terminal
- **Batch Installation**: Installs all selected packages sequentially
- **Detailed Logging**: Comprehensive installation logs
- **Error Recovery**: Continues with remaining packages if one fails

## 🛡️ Safety Features

### User Control
- **Optional Installation**: All packages are optional, app works with minimal setup
- **User Confirmation**: Always asks before installing anything
- **Skip Options**: Multiple ways to bypass dependency installation

### Error Recovery
- **Timeout Handling**: Prevents hanging on network issues
- **Partial Success**: Continues even if some packages fail
- **Rollback Safe**: Doesn't break existing installations

## 📊 Current Status

### ✅ Implemented Features
- Complete startup system with GUI and console installers
- Smart dependency detection and categorization
- User-friendly package selection interface
- Real-time installation progress and logging
- Persistent configuration management
- Multiple startup modes and options
- Enhanced platform-specific launchers
- Comprehensive error handling and fallbacks

### 🎯 Benefits for Users
- **Zero Manual Setup**: Automatic dependency installation
- **User Choice**: Can select which packages to install
- **Clear Information**: Understands what each package does
- **Flexible Options**: Multiple ways to start the application
- **Error Resilience**: Works even with partial installations

## 🚀 Getting Started

### For New Users
1. Download MFFUHijack
2. Double-click `run.bat` (Windows) or `run.sh` (Unix)
3. Follow the GUI installer prompts
4. Select desired packages and click "Install Selected"
5. Wait for installation to complete
6. Application starts automatically

### For Advanced Users
- Use `python launch.py --help` to see all options
- Use `python main.py` to skip dependency checks
- Use `python install.py` for manual dependency checking

## 📈 Impact

This enhanced startup system transforms MFFUHijack from a developer-focused tool requiring manual setup into a user-friendly application that handles its own dependencies automatically. Users can now:

- **Start immediately** without reading installation instructions
- **Choose their features** based on clear descriptions
- **Get help automatically** when dependencies are missing
- **Recover gracefully** from installation issues
- **Customize their experience** with flexible startup options

The system maintains full backward compatibility while providing a significantly improved user experience for new installations.
