#!/usr/bin/env python3
"""
Test script for Smart Features in Enhanced Live Scan Monitor
Demonstrates Time-based Analysis, Smart Retry System, and Code History
"""

import sys
import time
import random
from datetime import datetime, timedelta
from PyQt6.QtWidgets import *
from PyQt6.QtCore import *
from PyQt6.QtGui import *

from live_scan_monitor import LiveScanMonitorWindow
from smart_features import SmartFeaturesManager, CharacterConfusionMatrix


class SmartFeaturesTestWindow(QMainWindow):
    """Test window for smart features"""
    
    def __init__(self):
        super().__init__()
        self.monitor_window = None
        self.smart_features = SmartFeaturesManager()
        self.confusion_matrix = CharacterConfusionMatrix()
        self.init_ui()
    
    def init_ui(self):
        """Initialize test UI"""
        self.setWindowTitle("Smart Features Test Controller")
        self.setGeometry(100, 100, 800, 600)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # Header
        header_label = QLabel("🧠 Smart Features Test Controller")
        header_label.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        layout.addWidget(header_label)
        
        # Monitor Control
        monitor_group = QGroupBox("Monitor Control")
        monitor_layout = QHBoxLayout(monitor_group)
        
        self.create_monitor_btn = QPushButton("🔍 Create Enhanced Monitor")
        self.create_monitor_btn.clicked.connect(self.create_monitor)
        monitor_layout.addWidget(self.create_monitor_btn)
        
        self.start_session_btn = QPushButton("🚀 Start Smart Session")
        self.start_session_btn.clicked.connect(self.start_session)
        self.start_session_btn.setEnabled(False)
        monitor_layout.addWidget(self.start_session_btn)
        
        layout.addWidget(monitor_group)
        
        # Smart Features Testing
        smart_group = QGroupBox("🧠 Smart Features Testing")
        smart_layout = QVBoxLayout(smart_group)
        
        # Character confusion testing
        confusion_layout = QHBoxLayout()
        confusion_layout.addWidget(QLabel("Test Character Confusion:"))
        
        self.test_code_input = QLineEdit("ABC123")
        confusion_layout.addWidget(self.test_code_input)
        
        test_confusion_btn = QPushButton("🔄 Test Confusion")
        test_confusion_btn.clicked.connect(self.test_character_confusion)
        confusion_layout.addWidget(test_confusion_btn)
        
        smart_layout.addLayout(confusion_layout)
        
        # Code simulation
        sim_layout = QHBoxLayout()
        
        sim_valid_btn = QPushButton("✅ Simulate Valid Code")
        sim_valid_btn.clicked.connect(lambda: self.simulate_code(True))
        sim_layout.addWidget(sim_valid_btn)
        
        sim_invalid_btn = QPushButton("❌ Simulate Invalid Code")
        sim_invalid_btn.clicked.connect(lambda: self.simulate_code(False))
        sim_layout.addWidget(sim_invalid_btn)
        
        sim_confused_btn = QPushButton("🔀 Simulate Confused Code")
        sim_confused_btn.clicked.connect(self.simulate_confused_code)
        sim_layout.addWidget(sim_confused_btn)
        
        smart_layout.addLayout(sim_layout)
        
        # Batch testing
        batch_layout = QHBoxLayout()
        
        batch_btn = QPushButton("🎯 Run Batch Test")
        batch_btn.clicked.connect(self.run_batch_test)
        batch_layout.addWidget(batch_btn)
        
        time_test_btn = QPushButton("⏰ Test Time Analysis")
        time_test_btn.clicked.connect(self.test_time_analysis)
        batch_layout.addWidget(time_test_btn)
        
        smart_layout.addLayout(batch_layout)
        
        layout.addWidget(smart_group)
        
        # Results Display
        results_group = QGroupBox("📊 Test Results")
        results_layout = QVBoxLayout(results_group)
        
        self.results_text = QTextEdit()
        self.results_text.setMaximumHeight(200)
        results_layout.addWidget(self.results_text)
        
        layout.addWidget(results_group)
        
        # Status
        self.status_label = QLabel("Ready to test smart features")
        layout.addWidget(self.status_label)
    
    def create_monitor(self):
        """Create enhanced monitor window"""
        if not self.monitor_window:
            self.monitor_window = LiveScanMonitorWindow()
            self.monitor_window.show()
            
            # Position next to this window
            geometry = self.geometry()
            self.monitor_window.setGeometry(
                geometry.x() + geometry.width() + 20,
                geometry.y(),
                1400, 900
            )
            
            self.create_monitor_btn.setEnabled(False)
            self.start_session_btn.setEnabled(True)
            self.update_status("✅ Enhanced monitor created with smart features")
    
    def start_session(self):
        """Start a smart scanning session"""
        if self.monitor_window:
            test_url = "https://www.youtube.com/@MyFundedFuturesPropFirm"
            self.monitor_window.start_scanning_session(test_url)
            self.update_status("🚀 Smart scanning session started")
    
    def test_character_confusion(self):
        """Test character confusion matrix"""
        test_code = self.test_code_input.text().strip()
        if not test_code:
            return
        
        variants = self.confusion_matrix.generate_code_variants(test_code, max_substitutions=2)
        
        result_text = f"Character Confusion Test for '{test_code}':\n"
        result_text += "=" * 50 + "\n"
        
        for i, (variant, confidence) in enumerate(variants[:10]):  # Show top 10
            result_text += f"{i+1:2d}. {variant} (confidence: {confidence:.2f})\n"
        
        self.results_text.append(result_text)
        self.update_status(f"🔄 Generated {len(variants)} variants for '{test_code}'")
    
    def simulate_code(self, valid: bool):
        """Simulate code detection"""
        if not self.monitor_window:
            self.update_status("❌ No monitor window available")
            return
        
        if valid:
            codes = [
                {"code": "ABC123", "type": "Starter", "confidence": 0.95},
                {"code": "XYZ789", "type": "Expert", "confidence": 0.87},
                {"code": "RESET5", "type": "Free Reset Code", "confidence": 0.92},
                {"code": "PLUS99", "type": "Starter Plus", "confidence": 0.88},
            ]
        else:
            codes = [
                {"code": "AB", "type": "Starter", "confidence": 0.4},  # Too short
                {"code": "INVALID", "type": "Unknown", "confidence": 0.3},
                {"code": "EXPIRED", "type": "Expert", "confidence": 0.2},
            ]
        
        code_data = random.choice(codes)
        code_data['full_text'] = f"FREE 50 {code_data['type'].upper()}: {code_data['code']}"
        
        self.monitor_window.on_code_detected(code_data)
        
        status = "valid" if valid else "invalid"
        self.update_status(f"🎯 Simulated {status} code: {code_data['code']}")
    
    def simulate_confused_code(self):
        """Simulate code with character confusion"""
        if not self.monitor_window:
            self.update_status("❌ No monitor window available")
            return
        
        # Original valid codes
        original_codes = ["START50", "EXPERT2024", "RESET123", "PLUS88"]
        original = random.choice(original_codes)
        
        # Apply character confusion
        confused_code = self.apply_confusion(original)
        
        code_data = {
            "code": confused_code,
            "type": "Starter",
            "confidence": 0.6,  # Medium confidence due to confusion
            "full_text": f"FREE 50 STARTER: {confused_code}"
        }
        
        self.monitor_window.on_code_detected(code_data)
        
        self.results_text.append(f"Confused Code Test: {original} → {confused_code}\n")
        self.update_status(f"🔀 Simulated confused code: {original} → {confused_code}")
    
    def apply_confusion(self, code: str) -> str:
        """Apply character confusion to a code"""
        confused = list(code)
        
        # Apply 1-2 character confusions
        num_changes = random.randint(1, 2)
        positions = random.sample(range(len(code)), min(num_changes, len(code)))
        
        for pos in positions:
            char = confused[pos]
            substitutions = self.confusion_matrix.get_possible_substitutions(char)
            if substitutions:
                confused[pos] = random.choice(substitutions)
        
        return ''.join(confused)
    
    def run_batch_test(self):
        """Run a batch test of multiple codes"""
        if not self.monitor_window:
            self.update_status("❌ No monitor window available")
            return
        
        self.results_text.append("🎯 Running Batch Test...\n")
        
        # Test various scenarios
        test_cases = [
            # Valid codes
            {"code": "START50", "type": "Starter", "confidence": 0.95, "scenario": "Valid"},
            {"code": "EXPERT99", "type": "Expert", "confidence": 0.88, "scenario": "Valid"},
            
            # Confused codes (S/5, O/0, I/1)
            {"code": "5TART50", "type": "Starter", "confidence": 0.7, "scenario": "S→5 Confusion"},
            {"code": "EXPERT0", "type": "Expert", "confidence": 0.75, "scenario": "O→0 Confusion"},
            {"code": "1NVALID", "type": "Expert", "confidence": 0.6, "scenario": "I→1 Confusion"},
            
            # Invalid codes
            {"code": "AB", "type": "Starter", "confidence": 0.4, "scenario": "Too Short"},
            {"code": "TOOLONG123456", "type": "Expert", "confidence": 0.3, "scenario": "Too Long"},
            
            # Duplicates
            {"code": "START50", "type": "Starter", "confidence": 0.95, "scenario": "Duplicate"},
        ]
        
        for i, test_case in enumerate(test_cases):
            test_case['full_text'] = f"TEST {test_case['scenario']}: {test_case['code']}"
            self.monitor_window.on_code_detected(test_case)
            
            self.results_text.append(f"{i+1:2d}. {test_case['scenario']}: {test_case['code']}")
            
            # Small delay for visual effect
            QApplication.processEvents()
            time.sleep(0.2)
        
        self.results_text.append("✅ Batch test completed!\n")
        self.update_status("🎯 Batch test completed - check smart features analytics")
    
    def test_time_analysis(self):
        """Test time-based analysis"""
        if not self.monitor_window:
            self.update_status("❌ No monitor window available")
            return
        
        self.results_text.append("⏰ Testing Time-based Analysis...\n")
        
        # Simulate codes at different times
        base_time = datetime.now()
        
        for i in range(20):
            # Vary detection times to create patterns
            time_offset = timedelta(minutes=i*3)  # Every 3 minutes
            detection_time = base_time - time_offset
            
            code_data = {
                "code": f"TIME{i:02d}",
                "type": random.choice(["Starter", "Expert", "Reset"]),
                "confidence": random.uniform(0.7, 0.95),
                "full_text": f"TIME TEST: TIME{i:02d}"
            }
            
            # Manually add to time analyzer
            if self.monitor_window.smart_features:
                self.monitor_window.smart_features.time_analyzer.add_detection(detection_time, code_data)
        
        self.results_text.append("✅ Time analysis test data generated!\n")
        self.update_status("⏰ Time analysis test completed - open Temporal Dashboard to view")
    
    def update_status(self, message: str):
        """Update status label"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.status_label.setText(f"[{timestamp}] {message}")


def main():
    """Main test function"""
    print("🧠 Starting Smart Features Test")
    print("=" * 50)
    
    app = QApplication(sys.argv)
    
    test_window = SmartFeaturesTestWindow()
    test_window.show()
    
    print("📋 Smart Features Test Instructions:")
    print("1. Click 'Create Enhanced Monitor' to open the monitor")
    print("2. Click 'Start Smart Session' to begin smart monitoring")
    print("3. Test individual features:")
    print("   • Character Confusion: Test OCR error corrections")
    print("   • Valid/Invalid Codes: Test detection and validation")
    print("   • Confused Codes: Test smart retry suggestions")
    print("4. Run batch tests to see comprehensive smart features")
    print("5. Use monitor's Smart Features menu for:")
    print("   • Temporal Analytics Dashboard")
    print("   • Code History Browser")
    print("   • Retry Statistics")
    print("   • Comprehensive Analytics")
    print()
    print("🧠 Smart Features Include:")
    print("• Time-based Analysis: Peak hours, detection patterns")
    print("• Smart Retry System: Character confusion correction")
    print("• Code History: Duplicate detection, pattern learning")
    print("• Temporal Dashboard: Real-time analytics visualization")
    
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
