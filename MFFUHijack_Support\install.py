#!/usr/bin/env python3
"""
Installation script for MFFUHijack
Checks dependencies and provides installation guidance
"""

import sys
import subprocess
import importlib
import os
from pathlib import Path


def check_python_version():
    """Check if Python version is compatible"""
    print("Checking Python version...")
    version = sys.version_info
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print(f"❌ Python {version.major}.{version.minor} detected")
        print("❌ Python 3.8 or higher is required")
        return False
    else:
        print(f"✅ Python {version.major}.{version.minor}.{version.micro} - OK")
        return True


def check_package(package_name, import_name=None):
    """Check if a package is installed"""
    if import_name is None:
        import_name = package_name
    
    try:
        importlib.import_module(import_name)
        print(f"✅ {package_name} - Installed")
        return True
    except ImportError:
        print(f"❌ {package_name} - Not installed")
        return False


def install_package(package_name):
    """Install a package using pip"""
    try:
        print(f"Installing {package_name}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
        print(f"✅ {package_name} installed successfully")
        return True
    except subprocess.CalledProcessError:
        print(f"❌ Failed to install {package_name}")
        return False


def check_external_tools():
    """Check for external tools like yt-dlp and ffmpeg"""
    print("\nChecking external tools...")
    
    tools = {
        'yt-dlp': 'yt-dlp --version',
        'ffmpeg': 'ffmpeg -version'
    }
    
    results = {}
    
    for tool, command in tools.items():
        try:
            result = subprocess.run(command.split(), 
                                  capture_output=True, 
                                  text=True, 
                                  timeout=10)
            if result.returncode == 0:
                print(f"✅ {tool} - Available")
                results[tool] = True
            else:
                print(f"❌ {tool} - Not working properly")
                results[tool] = False
        except (subprocess.TimeoutExpired, FileNotFoundError):
            print(f"❌ {tool} - Not found")
            results[tool] = False
    
    return results


def main():
    """Main installation check and setup"""
    print("=" * 60)
    print("MFFUHijack Installation Check")
    print("=" * 60)
    
    # Check Python version
    if not check_python_version():
        print("\n❌ Installation cannot continue with incompatible Python version")
        return False
    
    print("\nChecking Python packages...")
    
    # Define required packages
    required_packages = [
        ('PyQt6', 'PyQt6'),
        ('opencv-python', 'cv2'),
        ('numpy', 'numpy'),
        ('requests', 'requests'),
        ('Pillow', 'PIL'),
        ('yt-dlp', 'yt_dlp'),
    ]
    
    optional_packages = [
        ('easyocr', 'easyocr'),
        ('paddlepaddle', 'paddle'),  # PaddleOCR requires paddlepaddle
        ('paddleocr', 'paddleocr'),
        ('torch', 'torch'),
        ('torchvision', 'torchvision'),
    ]
    
    # Check required packages
    missing_required = []
    for package_name, import_name in required_packages:
        if not check_package(package_name, import_name):
            missing_required.append(package_name)
    
    # Check optional packages
    missing_optional = []
    for package_name, import_name in optional_packages:
        if not check_package(package_name, import_name):
            missing_optional.append(package_name)
    
    # Check external tools
    external_tools = check_external_tools()
    
    # Summary
    print("\n" + "=" * 60)
    print("INSTALLATION SUMMARY")
    print("=" * 60)
    
    if missing_required:
        print("❌ Missing required packages:")
        for package in missing_required:
            print(f"   - {package}")
        print("\nTo install required packages, run:")
        print(f"   pip install {' '.join(missing_required)}")
    else:
        print("✅ All required packages are installed")
    
    if missing_optional:
        print("\n⚠️  Missing optional packages (OCR/ML functionality limited):")
        for package in missing_optional:
            print(f"   - {package}")
        print("\nTo install optional packages, run:")
        print(f"   pip install {' '.join(missing_optional)}")
    else:
        print("✅ All optional packages are installed")
    
    # External tools summary
    print("\nExternal tools status:")
    for tool, available in external_tools.items():
        status = "✅ Available" if available else "❌ Missing"
        print(f"   {tool}: {status}")
    
    if not external_tools.get('yt-dlp', False):
        print("\n📥 To install yt-dlp:")
        print("   pip install yt-dlp")
    
    if not external_tools.get('ffmpeg', False):
        print("\n📥 To install FFmpeg:")
        print("   Windows: Download from https://ffmpeg.org/download.html")
        print("   macOS: brew install ffmpeg")
        print("   Linux: sudo apt install ffmpeg")
    
    # Check if we can run the application
    can_run = len(missing_required) == 0
    
    if can_run:
        print("\n🎉 MFFUHijack is ready to run!")
        print("   Start with: python main.py")
        
        if missing_optional:
            print("\n⚠️  Note: Some features may be limited without optional packages")
    else:
        print("\n❌ Please install missing required packages before running MFFUHijack")
    
    # Offer to install missing packages
    if missing_required and input("\nWould you like to install missing required packages? (y/n): ").lower() == 'y':
        print("\nInstalling required packages...")
        for package in missing_required:
            install_package(package)
    
    if missing_optional and input("\nWould you like to install missing optional packages? (y/n): ").lower() == 'y':
        print("\nInstalling optional packages...")
        for package in missing_optional:
            install_package(package)
    
    print("\n" + "=" * 60)
    print("Installation check complete!")
    print("=" * 60)
    
    return can_run


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\nInstallation check interrupted by user")
    except Exception as e:
        print(f"\nInstallation check failed: {e}")
        import traceback
        traceback.print_exc()
