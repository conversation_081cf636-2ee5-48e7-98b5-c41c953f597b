#!/usr/bin/env python3
"""
MFFUHijack - Real-Time OCR Livestream Code Sniping Bot
Main application entry point
"""

import sys
import os
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import Qt

# Add current directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import startup checker
from startup_checker import check_startup_dependencies
from gui import MFFU<PERSON><PERSON>ckGUI


def main():
    """Main application entry point"""

    # Run startup dependency check with automatic installation
    print("🚀 Starting MFFUHijack...")
    print("🔍 Checking and installing dependencies...")

    if not check_startup_dependencies(auto_install=True, use_gui=True):
        print("❌ Cannot start application due to missing critical dependencies.")
        print("Please install required packages and try again.")
        print("\nYou can also try:")
        print("  python launch.py --no-gui-installer  # Use console installer")
        print("  python install.py                    # Manual dependency check")
        sys.exit(1)

    # Create QApplication instance
    app = QApplication(sys.argv)
    
    # Set application properties
    app.setApplicationName("MFFUHijack")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("MFFUHijack")
    
    # Enable high DPI scaling (PyQt6 compatible)
    try:
        app.setAttribute(Qt.ApplicationAttribute.AA_EnableHighDpiScaling, True)
        app.setAttribute(Qt.ApplicationAttribute.AA_UseHighDpiPixmaps, True)
    except AttributeError:
        # PyQt6 handles high DPI automatically, these attributes may not exist
        pass
    
    # Create and show main window
    window = MFFUHijackGUI()
    window.show()
    
    # Start event loop
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
