#!/usr/bin/env python3
"""
MFFUHijack - Comprehensive Main GUI
Single file with all functionality including livestream testing mode
"""

import sys
import os
import time
import cv2
import json
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from PyQt6.QtWidgets import *
from PyQt6.QtCore import *
from PyQt6.QtGui import *

# Add current directory and support folder to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'MFFUHijack_Support'))

# Import core modules with fallbacks
try:
    from ocr_utils import ocr_manager
    OCR_AVAILABLE = True
except ImportError:
    print("OCR utilities not available")
    OCR_AVAILABLE = False

try:
    from submitter import code_submitter
    SUBMITTER_AVAILABLE = True
except ImportError:
    print("Code submitter not available")
    SUBMITTER_AVAILABLE = False

try:
    from livestream_testing_mode import (
        CodeValidationTester, StreamProcessor, YouTubeStreamDownloader, TestingStatistics
    )
    TESTING_MODE_AVAILABLE = True
except ImportError:
    print("Testing mode components not available")
    TESTING_MODE_AVAILABLE = False

try:
    from manual_ocr_region_selector import OCRRegionSelectorDialog
    OCR_REGION_SELECTOR_AVAILABLE = True
except ImportError:
    print("OCR region selector not available")
    OCR_REGION_SELECTOR_AVAILABLE = False

try:
    from smart_features import SmartFeaturesManager
    SMART_FEATURES_AVAILABLE = True
except ImportError:
    print("Smart features not available")
    SMART_FEATURES_AVAILABLE = False

# Try to import psutil, use replacement if not available
try:
    import psutil
except ImportError:
    try:
        import psutil_replacement as psutil
        print("Using psutil replacement")
    except ImportError:
        print("No system monitoring available")

# Try to import OCR engines
try:
    import easyocr
    EASYOCR_AVAILABLE = True
except ImportError:
    EASYOCR_AVAILABLE = False

try:
    import paddleocr
    PADDLEOCR_AVAILABLE = True
except ImportError:
    PADDLEOCR_AVAILABLE = False


class PreloadThread(QThread):
    """Thread for browser preloading to keep GUI responsive"""
    status_update = pyqtSignal(str)

    def __init__(self, selected_types, username, password, cvv=""):
        super().__init__()
        self.selected_types = selected_types
        self.username = username
        self.password = password
        self.cvv = cvv
        self.browser_automation = None

    def run(self):
        """Run browser preloading in separate thread"""
        try:
            self.status_update.emit(f"🚀 Starting browser preloading for: {', '.join(self.selected_types)}")

            # Import and initialize browser automation in thread
            import sys
            import os
            sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'MFFUHijack_Support'))

            from MFFUHijack_Support.browser_automation import BrowserAutomation
            self.browser_automation = BrowserAutomation()
            self.browser_automation.is_visible = True  # Ensure browser is visible

            if self.browser_automation.initialize_browser():
                self.status_update.emit("Browser initialized")

                # Login first to establish session
                if self.browser_automation.login_to_mff(self.username, self.password):
                    self.status_update.emit("Login page loaded - please complete CAPTCHA if present")

                    # Wait for login completion
                    if self.browser_automation.wait_for_login_completion(timeout=60):
                        self.status_update.emit("Login completed successfully")

                        # Open tabs for manual setup with CVV
                        success = self.browser_automation.open_manual_tabs(self.selected_types, self.cvv)
                        if success:
                            if self.cvv:
                                self.status_update.emit("All tabs opened successfully - CVV pre-filled")
                            else:
                                self.status_update.emit("All tabs opened successfully")
                        else:
                            self.status_update.emit("Failed to open some tabs")
                    else:
                        self.status_update.emit("Login timeout - please complete login manually")
                else:
                    self.status_update.emit("Failed to load login page or fill credentials")
            else:
                self.status_update.emit("Failed to initialize browser")
        except Exception as e:
            self.status_update.emit(f"❌ Error during preloading: {str(e)}")
            import traceback
            print(f"Preloading error: {str(e)}")
            print(traceback.format_exc())


class LivestreamTab(QWidget):
    """Main livestream bot tab"""

    def __init__(self):
        super().__init__()
        self.live_bot = None
        self.monitor_window = None
        self.session_start_time = None
        self.session_timer = QTimer()
        self.session_timer.timeout.connect(self.update_session_time)
        self.init_ui()

    def init_ui(self):
        """Initialize compact and responsive livestream tab UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)  # Reduced margins
        layout.setSpacing(8)  # Reduced spacing

        # Configuration section with compact styling
        config_group = QGroupBox("🔧 Configuration")
        config_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 1px solid #ccc;
                border-radius: 5px;
                margin-top: 8px;
                padding-top: 5px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 8px;
                padding: 0 3px 0 3px;
            }
        """)
        config_layout = QGridLayout(config_group)
        config_layout.setSpacing(8)  # Reduced spacing
        config_layout.setContentsMargins(10, 15, 10, 10)

        # Channel input
        config_layout.addWidget(QLabel("Channel:"), 0, 0)
        self.channel_input = QLineEdit()
        self.channel_input.setPlaceholderText("https://www.youtube.com/@MyFundedFuturesPropFirm")
        self.channel_input.setText("https://www.youtube.com/@MyFundedFuturesPropFirm")
        config_layout.addWidget(self.channel_input, 0, 1, 1, 2)

        # Auto-detect button
        self.auto_detect_btn = QPushButton("🔍 Auto-Detect Latest Stream")
        self.auto_detect_btn.clicked.connect(self.auto_detect_stream)
        config_layout.addWidget(self.auto_detect_btn, 0, 3)

        # URL input
        config_layout.addWidget(QLabel("Stream URL:"), 1, 0)
        self.url_input = QLineEdit()
        self.url_input.setPlaceholderText("https://www.youtube.com/watch?v=...")
        config_layout.addWidget(self.url_input, 1, 1, 1, 3)

        # OCR Engine
        config_layout.addWidget(QLabel("OCR Engine:"), 2, 0)
        self.ocr_combo = QComboBox()
        if EASYOCR_AVAILABLE:
            self.ocr_combo.addItem("EasyOCR", "easyocr")
        if PADDLEOCR_AVAILABLE:
            self.ocr_combo.addItem("PaddleOCR", "paddleocr")
        if self.ocr_combo.count() == 0:
            self.ocr_combo.addItem("No OCR Available", "none")
        config_layout.addWidget(self.ocr_combo, 2, 1)

        # Polling interval
        config_layout.addWidget(QLabel("Interval (sec):"), 2, 2)
        self.interval_input = QLineEdit("1.0")
        self.interval_input.setMaximumWidth(80)
        config_layout.addWidget(self.interval_input, 2, 3)

        layout.addWidget(config_group)

        # Compact control section
        control_group = QGroupBox("🎮 Controls")
        control_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 1px solid #ccc;
                border-radius: 5px;
                margin-top: 8px;
                padding-top: 5px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 8px;
                padding: 0 3px 0 3px;
            }
        """)
        control_layout = QHBoxLayout(control_group)
        control_layout.setSpacing(8)
        control_layout.setContentsMargins(10, 15, 10, 10)

        # Compact buttons
        self.start_btn = QPushButton("🚀 Start")
        self.start_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 4px;
                font-weight: bold;
                padding: 8px 16px;
                font-size: 12px;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        self.start_btn.clicked.connect(self.start_bot)
        self.start_btn.setSizePolicy(QSizePolicy.Policy.Preferred, QSizePolicy.Policy.Fixed)
        control_layout.addWidget(self.start_btn)

        self.stop_btn = QPushButton("⏹️ Stop")
        self.stop_btn.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                border-radius: 4px;
                font-weight: bold;
                padding: 8px 16px;
                font-size: 12px;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #da190b;
            }
        """)
        self.stop_btn.clicked.connect(self.stop_bot)
        self.stop_btn.setEnabled(False)
        self.stop_btn.setSizePolicy(QSizePolicy.Policy.Preferred, QSizePolicy.Policy.Fixed)
        control_layout.addWidget(self.stop_btn)

        # OCR Region button
        if OCR_REGION_SELECTOR_AVAILABLE:
            self.ocr_region_btn = QPushButton("🎯 OCR Region")
            self.ocr_region_btn.clicked.connect(self.open_ocr_region_selector)
            control_layout.addWidget(self.ocr_region_btn)

        # Preload Pages button
        self.preload_btn = QPushButton("🚀 Preload Pages")
        self.preload_btn.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                border-radius: 4px;
                font-weight: bold;
                padding: 8px 16px;
                font-size: 12px;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """)
        self.preload_btn.clicked.connect(self.preload_pages)
        self.preload_btn.setToolTip("Preload browser windows and links for selected account types")
        self.preload_btn.setSizePolicy(QSizePolicy.Policy.Preferred, QSizePolicy.Policy.Fixed)
        control_layout.addWidget(self.preload_btn)

        # Live Scan Monitor button
        self.monitor_btn = QPushButton("📊 Monitor")
        self.monitor_btn.setStyleSheet("""
            QPushButton {
                background-color: #FF9800;
                color: white;
                border: none;
                border-radius: 4px;
                font-weight: bold;
                padding: 8px 16px;
                font-size: 12px;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #F57C00;
            }
        """)
        self.monitor_btn.clicked.connect(self.open_monitor_window)
        self.monitor_btn.setSizePolicy(QSizePolicy.Policy.Preferred, QSizePolicy.Policy.Fixed)
        control_layout.addWidget(self.monitor_btn)

        control_layout.addStretch()

        layout.addWidget(control_group)
        # Account Type Selection
        account_group = QGroupBox("🎯 Account Types to Scan")
        account_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 1px solid #ccc;
                border-radius: 5px;
                margin-top: 8px;
                padding-top: 5px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 8px;
                padding: 0 3px 0 3px;
            }
        """)
        account_layout = QVBoxLayout(account_group)
        account_layout.setSpacing(8)
        account_layout.setContentsMargins(10, 15, 10, 10)

        # Account type checkboxes
        checkbox_layout = QGridLayout()
        checkbox_layout.setSpacing(8)

        # Create checkboxes for each account type
        self.account_checkboxes = {}
        account_types = [
            ("Free Reset Code", "🔄 Free Reset Code"),
            ("Starter", "🚀 Starter"),
            ("Starter Plus", "⭐ Starter Plus"),
            ("Expert", "👑 Expert")
        ]

        for i, (account_type, display_name) in enumerate(account_types):
            checkbox = QCheckBox(display_name)
            checkbox.setChecked(True)  # All enabled by default
            checkbox.setStyleSheet("""
                QCheckBox {
                    font-size: 11px;
                    font-weight: bold;
                    spacing: 6px;
                }
                QCheckBox::indicator {
                    width: 16px;
                    height: 16px;
                }
                QCheckBox::indicator:unchecked {
                    border: 2px solid #ccc;
                    border-radius: 3px;
                    background-color: white;
                }
                QCheckBox::indicator:checked {
                    border: 2px solid #4CAF50;
                    border-radius: 3px;
                    background-color: #4CAF50;
                }
            """)
            self.account_checkboxes[account_type] = checkbox
            checkbox_layout.addWidget(checkbox, i // 2, i % 2)

        account_layout.addLayout(checkbox_layout)

        # Account selection info
        account_info = QLabel("💡 Select which account types to scan for during live streaming. Only selected types will be detected and processed.")
        account_info.setStyleSheet("color: #666; font-size: 10px; font-style: italic;")
        account_info.setWordWrap(True)
        account_layout.addWidget(account_info)

        layout.addWidget(account_group)

        # Status section (simplified)
        status_group = QGroupBox("� Status")
        status_layout = QVBoxLayout(status_group)

        self.status_label = QLabel("⚫ Ready - Click 'Start Bot' to begin scanning")
        self.status_label.setStyleSheet("font-size: 14px; font-weight: bold; padding: 10px;")
        self.status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        status_layout.addWidget(self.status_label)

        # Quick stats
        stats_layout = QHBoxLayout()

        self.codes_found_label = QLabel("Codes Found: 0")
        self.codes_found_label.setStyleSheet("padding: 5px;")
        stats_layout.addWidget(self.codes_found_label)

        self.session_time_label = QLabel("Session Time: 00:00:00")
        self.session_time_label.setStyleSheet("padding: 5px;")
        stats_layout.addWidget(self.session_time_label)

        self.last_detection_label = QLabel("Last Detection: None")
        self.last_detection_label.setStyleSheet("padding: 5px;")
        stats_layout.addWidget(self.last_detection_label)

        status_layout.addLayout(stats_layout)
        layout.addWidget(status_group)

        # Initialize OCR region from saved settings
        self.current_ocr_region = self.load_ocr_region_settings()

    def load_ocr_region_settings(self):
        """Load OCR region settings from QSettings"""
        try:
            settings = QSettings()
            x = settings.value("ocr_region_x", 0.0, type=float)
            y = settings.value("ocr_region_y", 67.0, type=float)
            w = settings.value("ocr_region_width", 100.0, type=float)
            h = settings.value("ocr_region_height", 33.0, type=float)

            # Validate region values
            x = max(0.0, min(100.0, x))
            y = max(0.0, min(100.0, y))
            w = max(5.0, min(100.0 - x, w))
            h = max(5.0, min(100.0 - y, h))

            return (x, y, w, h)
        except Exception as e:
            print(f"Failed to load OCR region settings: {e}")
            return (0.0, 67.0, 100.0, 33.0)  # Default region

    def save_ocr_region_settings(self, region):
        """Save OCR region settings to QSettings"""
        try:
            settings = QSettings()
            x, y, w, h = region
            settings.setValue("ocr_region_x", x)
            settings.setValue("ocr_region_y", y)
            settings.setValue("ocr_region_width", w)
            settings.setValue("ocr_region_height", h)
            settings.sync()
            self.update_status(f"💾 OCR region saved: {x:.1f}%, {y:.1f}%, {w:.1f}%, {h:.1f}%")
        except Exception as e:
            print(f"Failed to save OCR region settings: {e}")

    def auto_detect_stream(self):
        """Auto-detect latest livestream"""
        self.log_message("🔍 Auto-detecting latest stream...")
        # Implementation would go here

    def start_bot(self):
        """Start the livestream bot"""
        try:
            # Get configuration
            url = self.url_input.text().strip()
            if not url:
                self.update_status("❌ Error: Please enter a stream URL")
                return

            ocr_engine = self.ocr_combo.currentData() or "easyocr"
            interval = float(self.interval_input.text() or "1.0")

            # Get selected account types
            selected_account_types = self.get_selected_account_types()
            if not selected_account_types:
                self.update_status("❌ Error: Please select at least one account type to scan")
                return

            # Initialize live bot if not exists
            if not self.live_bot:
                from MFFUHijack_Support.live_bot import LiveBot
                self.live_bot = LiveBot()

                # Connect signals for status updates
                self.live_bot.status_changed.connect(self.update_status)
                self.live_bot.code_detected.connect(self.on_code_detected)
                self.live_bot.error_occurred.connect(self.on_error)

            # Set monitor window if available
            if self.monitor_window:
                self.live_bot.set_monitor_window(self.monitor_window)

            # Set selected account types for filtering
            if hasattr(self.live_bot, 'set_account_types'):
                self.live_bot.set_account_types(selected_account_types)

            # Start the bot
            success = self.live_bot.start(url, ocr_engine, interval, region=self.current_ocr_region, account_types=selected_account_types)

            if success:
                self.start_btn.setEnabled(False)
                self.stop_btn.setEnabled(True)

                # Show which account types are being scanned
                account_types_str = ", ".join(selected_account_types)
                self.update_status(f"🟢 Bot Started - Scanning for: {account_types_str}")

                # Start session timer
                self.session_start_time = datetime.now()
                self.session_timer.start(1000)  # Update every second

                # Auto-open monitor window
                if not self.monitor_window or not self.monitor_window.isVisible():
                    self.open_monitor_window()
            else:
                self.update_status("❌ Failed to start bot")

        except Exception as e:
            self.update_status(f"❌ Error starting bot: {e}")

    def get_selected_account_types(self):
        """Get list of selected account types"""
        selected_types = []
        for account_type, checkbox in self.account_checkboxes.items():
            if checkbox.isChecked():
                selected_types.append(account_type)
        return selected_types

    def preload_pages(self):
        """Preload account type pages to checkout stage for selected account types"""
        try:
            # Get selected account types from checkboxes
            selected_types = self.get_selected_account_types()
            if not selected_types:
                self.update_status("❌ No account types selected. Please select at least one account type to preload.")
                return

            # Get credentials and CVV from Settings tab
            username = ""
            password = ""
            cvv = ""
            if self.main_window and hasattr(self.main_window, 'settings_tab'):
                settings = self.main_window.settings_tab.get_settings()
                browser_settings = settings.get('browser', {})
                username = browser_settings.get('username', '').strip()
                password = browser_settings.get('password', '').strip()
                cvv = browser_settings.get('cvv', '').strip()

            if not username or not password:
                self.update_status("❌ Please enter username and password in Settings tab before preloading.")
                QMessageBox.information(self, "Credentials Required",
                                      "Please go to Settings tab → Browser Automation Settings → MFFU Credentials\n"
                                      "and enter your username and password before preloading pages.")
                return

            # Show CVV status
            if cvv:
                self.update_status(f"💳 CVV provided - preloading pages for: {', '.join(selected_types)}")
            else:
                self.update_status(f"⚠️ CVV not provided - preloading pages for: {', '.join(selected_types)}")

            self.preload_btn.setText("⏳ Preloading...")
            self.preload_btn.setEnabled(False)

            # Start preloading in a separate thread to keep GUI responsive
            self.preload_thread = PreloadThread(selected_types, username, password, cvv)
            self.preload_thread.status_update.connect(self.update_status)
            self.preload_thread.finished.connect(self.on_preload_finished)

            # Use QTimer to start thread after GUI update
            QTimer.singleShot(100, self.preload_thread.start)

        except Exception as e:
            self.update_status(f"Error during preloading: {str(e)}")
            self.preload_btn.setText("🚀 Preload Pages")
            self.preload_btn.setEnabled(True)

    def on_preload_finished(self):
        """Called when preloading thread finishes"""
        self.preload_btn.setText("🚀 Preload Pages")
        self.preload_btn.setEnabled(True)

    def stop_bot(self):
        """Stop the livestream bot"""
        try:
            if self.live_bot:
                self.live_bot.stop()

            # Stop session timer
            self.session_timer.stop()

            self.start_btn.setEnabled(True)
            self.stop_btn.setEnabled(False)
            self.update_status("⚫ Bot Stopped")

        except Exception as e:
            self.update_status(f"❌ Error stopping bot: {e}")

    def update_session_time(self):
        """Update session time display"""
        if self.session_start_time:
            elapsed = datetime.now() - self.session_start_time
            hours, remainder = divmod(int(elapsed.total_seconds()), 3600)
            minutes, seconds = divmod(remainder, 60)
            self.session_time_label.setText(f"Session Time: {hours:02d}:{minutes:02d}:{seconds:02d}")

    def open_monitor_window(self):
        """Open the Live Scan Monitor window"""
        try:
            if not self.monitor_window:
                from MFFUHijack_Support.live_scan_monitor import LiveScanMonitorWindow
                self.monitor_window = LiveScanMonitorWindow()

                # Connect monitor window signals
                self.monitor_window.window_closed.connect(self.on_monitor_closed)
                self.monitor_window.region_changed.connect(self.on_region_changed)

                # Set monitor window in live bot if available
                if self.live_bot:
                    self.live_bot.set_monitor_window(self.monitor_window)

            # Show and position the monitor window
            self.monitor_window.show()
            self.monitor_window.raise_()
            self.monitor_window.activateWindow()

            # Position next to main window
            main_geometry = self.window().geometry()
            monitor_x = main_geometry.x() + main_geometry.width() + 20
            monitor_y = main_geometry.y()
            self.monitor_window.setGeometry(monitor_x, monitor_y, 1400, 900)

            self.update_status("📊 Live Scan Monitor opened")

        except Exception as e:
            self.update_status(f"❌ Error opening monitor: {e}")

    def on_monitor_closed(self):
        """Handle monitor window being closed"""
        self.monitor_window = None

    def on_region_changed(self, region):
        """Handle OCR region change from monitor window"""
        self.current_ocr_region = region
        x, y, w, h = region
        self.update_status(f"🎯 OCR region updated: {x:.1f}%, {y:.1f}%, {w:.1f}%, {h:.1f}%")

    def on_code_detected(self, code_data):
        """Handle code detection from live bot"""
        code = code_data.get('code', 'Unknown')
        account_type = code_data.get('type', 'Unknown')

        # Update quick stats
        current_count = int(self.codes_found_label.text().split(': ')[1])
        self.codes_found_label.setText(f"Codes Found: {current_count + 1}")
        self.last_detection_label.setText(f"Last Detection: {code} ({account_type})")

        self.update_status(f"Code detected: {code} ({account_type})")

        # Log to console with green [+]
        try:
            from MFFUHijack_Support.browser_automation import console
            console.code_found(f"Code detected: {code} ({account_type})")
        except Exception:
            print(f"[+] Code detected: {code} ({account_type})")

        # Test CVV retrieval before attempting submission
        test_cvv = self.test_cvv_retrieval()

        # Attempt automatic submission if browser automation is available
        self.submit_code_automatically(code, account_type)

    def submit_code_automatically(self, code: str, account_type: str):
        """Automatically submit code using browser automation"""
        try:
            # Get CVV from Settings tab
            cvv = ""
            reset_account = ""
            if hasattr(self, 'main_window') and hasattr(self.main_window, 'settings_tab'):
                settings = self.main_window.settings_tab.get_settings()
                browser_settings = settings.get('browser', {})
                cvv = browser_settings.get('cvv', '').strip()

                # Debug: Show CVV retrieval
                if cvv:
                    print(f"💳 CVV retrieved from settings: {'*' * len(cvv)}")
                    self.update_status(f"💳 CVV retrieved from settings: {'*' * len(cvv)}")
                else:
                    print("💳 No CVV found in settings")
                    self.update_status("💳 No CVV found in settings")
            else:
                print("❌ Cannot access settings tab for CVV")
                self.update_status("❌ Cannot access settings tab for CVV")

            # Get reset account from livestream tab if available
            if hasattr(self, 'reset_account_input'):
                reset_account = self.reset_account_input.text().strip()

            if not cvv:
                self.update_status("❌ CVV not provided - cannot submit code automatically")
                self.update_status("💡 Please enter CVV in Settings tab → Browser Automation Settings")
                return

            if account_type.lower() == "reset" and not reset_account:
                self.update_status("❌ Reset account number not provided - cannot submit reset code")
                return

            self.update_status(f"🚀 Attempting automatic submission of code: {code}")
            self.update_status(f"💳 Passing CVV to browser automation: {'*' * len(cvv)}")

            # Submit code in a separate thread to avoid blocking GUI
            import threading
            submission_thread = threading.Thread(
                target=self._submit_code_thread,
                args=(code, account_type, cvv, reset_account),
                daemon=True
            )
            submission_thread.start()

        except Exception as e:
            self.update_status(f"❌ Automatic submission error: {str(e)}")

    def _submit_code_thread(self, code: str, account_type: str, cvv: str, reset_account: str):
        """Submit code in separate thread"""
        try:
            from MFFUHijack_Support.browser_automation import browser_automation

            success = browser_automation.submit_code(code, account_type, cvv, reset_account)

            if success:
                self.update_status(f"✅ Code {code} submitted successfully!")
            else:
                self.update_status(f"❌ Failed to submit code {code}")

        except Exception as e:
            self.update_status(f"❌ Code submission thread error: {str(e)}")

    def test_cvv_retrieval(self):
        """Test method to verify CVV can be retrieved from settings"""
        try:
            if hasattr(self, 'main_window') and hasattr(self.main_window, 'settings_tab'):
                # Direct field access
                direct_cvv = self.main_window.settings_tab.cvv_input.text().strip()
                print(f"🧪 Direct CVV field access: {'*' * len(direct_cvv) if direct_cvv else 'EMPTY'}")

                # Settings method access
                settings = self.main_window.settings_tab.get_settings()
                browser_settings = settings.get('browser', {})
                settings_cvv = browser_settings.get('cvv', '').strip()
                print(f"🧪 Settings method CVV access: {'*' * len(settings_cvv) if settings_cvv else 'EMPTY'}")

                # Show both for comparison
                self.update_status(f"🧪 CVV Test - Direct: {'*' * len(direct_cvv) if direct_cvv else 'EMPTY'}, Settings: {'*' * len(settings_cvv) if settings_cvv else 'EMPTY'}")

                return direct_cvv or settings_cvv
            else:
                print("🧪 Cannot access settings tab for CVV test")
                return ""
        except Exception as e:
            print(f"🧪 CVV test error: {e}")
            return ""

    def on_error(self, error_message):
        """Handle error from live bot"""
        self.update_status(f"❌ Error: {error_message}")

    def update_status(self, message):
        """Update status label"""
        self.status_label.setText(message)

    def open_ocr_region_selector(self):
        """Open OCR region selector"""
        if not OCR_REGION_SELECTOR_AVAILABLE:
            QMessageBox.warning(self, "Not Available", "OCR region selector is not available")
            return

        try:
            dialog = OCRRegionSelectorDialog(self, initial_region=self.current_ocr_region)
            if dialog.exec() == QDialog.DialogCode.Accepted:
                self.current_ocr_region = dialog.get_selected_region()
                x, y, w, h = self.current_ocr_region
                self.log_message(f"🎯 OCR region updated: {x:.1f}%, {y:.1f}%, {w:.1f}%, {h:.1f}%")

                # Save the new region settings
                self.save_ocr_region_settings(self.current_ocr_region)
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to open OCR region selector: {e}")

    def log_message(self, message: str):
        """Log message to monitor window if available, otherwise update status"""
        if self.monitor_window and hasattr(self.monitor_window, 'log_activity'):
            self.monitor_window.log_activity(message)
        else:
            self.update_status(message)



class SettingsTab(QWidget):
    """Settings and configuration tab"""

    def __init__(self, main_window=None):
        super().__init__()
        self.main_window = main_window
        self.init_ui()

    def init_ui(self):
        """Initialize compact and responsive settings tab UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)  # Reduced margins
        layout.setSpacing(8)  # Reduced spacing

        # Create responsive scroll area for settings
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: none;
                background-color: transparent;
            }
            QScrollBar:vertical {
                background-color: #f0f0f0;
                width: 12px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical {
                background-color: #c0c0c0;
                border-radius: 6px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #a0a0a0;
            }
        """)

        # Main settings widget with responsive layout
        settings_widget = QWidget()
        settings_widget.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Preferred)
        settings_layout = QVBoxLayout(settings_widget)
        settings_layout.setSpacing(12)  # Reduced spacing
        settings_layout.setContentsMargins(5, 5, 5, 5)

        # Add all settings sections
        settings_layout.addWidget(self.create_general_settings())
        settings_layout.addWidget(self.create_ocr_settings())
        settings_layout.addWidget(self.create_browser_settings())
        settings_layout.addWidget(self.create_account_settings())
        settings_layout.addWidget(self.create_advanced_settings())

        # Load settings after UI is created
        self.load_settings()

        # Connect auto-save functionality
        self.connect_auto_save()

        # Add stretch to push everything to top
        settings_layout.addStretch()

        scroll_area.setWidget(settings_widget)
        layout.addWidget(scroll_area)

    def create_general_settings(self):
        """Create compact general settings section"""
        group = QGroupBox("⚙️ General Settings")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 12px;
                border: 1px solid #cccccc;
                border-radius: 5px;
                margin-top: 8px;
                padding-top: 8px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 8px;
                padding: 0 3px 0 3px;
            }
        """)
        group.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Preferred)
        layout = QGridLayout(group)
        layout.setSpacing(8)  # Reduced spacing
        layout.setContentsMargins(10, 15, 10, 10)

        # Startup behavior
        layout.addWidget(QLabel("🚀 Startup Behavior:"), 0, 0)
        self.auto_start_checkbox = QCheckBox("Auto-start live scanning on launch")
        layout.addWidget(self.auto_start_checkbox, 0, 1, 1, 2)

        # Logging preferences
        layout.addWidget(QLabel("📝 Logging Level:"), 1, 0)
        self.logging_combo = QComboBox()
        self.logging_combo.addItems(["Debug", "Info", "Warning", "Error"])
        self.logging_combo.setCurrentText("Info")
        layout.addWidget(self.logging_combo, 1, 1)

        # UI preferences
        layout.addWidget(QLabel("🎨 UI Theme:"), 2, 0)
        self.theme_combo = QComboBox()
        self.theme_combo.addItems(["Default", "Dark", "Light"])
        layout.addWidget(self.theme_combo, 2, 1)

        # Auto-save settings
        self.auto_save_checkbox = QCheckBox("Auto-save settings on change")
        self.auto_save_checkbox.setChecked(True)
        layout.addWidget(self.auto_save_checkbox, 3, 0, 1, 3)

        # Layout auto-save
        self.auto_save_layout_checkbox = QCheckBox("Auto-save window layout and sizing")
        self.auto_save_layout_checkbox.setChecked(True)
        self.auto_save_layout_checkbox.setToolTip("Automatically save and restore window size, position, and section layouts")
        layout.addWidget(self.auto_save_layout_checkbox, 4, 0, 1, 3)

        return group

    def create_ocr_settings(self):
        """Create OCR settings section"""
        group = QGroupBox("👁️ OCR Settings")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                border: 2px solid #ddd;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        layout = QGridLayout(group)
        layout.setSpacing(15)

        # OCR Engine selection
        layout.addWidget(QLabel("🔧 OCR Engine:"), 0, 0)
        self.ocr_engine_combo = QComboBox()
        self.ocr_engine_combo.addItems(["EasyOCR", "PaddleOCR", "Auto-Select"])
        self.ocr_engine_combo.setCurrentText("EasyOCR")
        layout.addWidget(self.ocr_engine_combo, 0, 1)

        # GPU acceleration
        self.gpu_checkbox = QCheckBox("🎮 Enable GPU Acceleration")
        self.gpu_checkbox.setChecked(True)
        layout.addWidget(self.gpu_checkbox, 1, 0, 1, 2)

        # OCR confidence threshold
        layout.addWidget(QLabel("🎯 Confidence Threshold:"), 2, 0)
        self.confidence_slider = QSlider(Qt.Orientation.Horizontal)
        self.confidence_slider.setRange(50, 100)
        self.confidence_slider.setValue(80)
        self.confidence_slider.setTickPosition(QSlider.TickPosition.TicksBelow)
        self.confidence_slider.setTickInterval(10)
        layout.addWidget(self.confidence_slider, 2, 1)

        self.confidence_label = QLabel("80%")
        self.confidence_slider.valueChanged.connect(lambda v: self.confidence_label.setText(f"{v}%"))
        layout.addWidget(self.confidence_label, 2, 2)

        # OCR region settings
        layout.addWidget(QLabel("📐 Default OCR Region:"), 3, 0)
        self.ocr_region_button = QPushButton("🎯 Configure Region")
        self.ocr_region_button.clicked.connect(self.configure_ocr_region)
        layout.addWidget(self.ocr_region_button, 3, 1)

        return group

    def create_browser_settings(self):
        """Create browser settings section"""
        group = QGroupBox("🌐 Browser Automation Settings")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                border: 2px solid #ddd;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        layout = QGridLayout(group)
        layout.setSpacing(15)

        # MFFU Credentials
        layout.addWidget(QLabel("👤 MFFU Username:"), 0, 0)
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("Enter your MFFU username")
        layout.addWidget(self.username_input, 0, 1, 1, 2)

        layout.addWidget(QLabel("🔒 MFFU Password:"), 1, 0)
        self.password_input = QLineEdit()
        self.password_input.setEchoMode(QLineEdit.EchoMode.Password)
        self.password_input.setPlaceholderText("Enter your MFFU password")
        layout.addWidget(self.password_input, 1, 1, 1, 2)

        # CVV field
        layout.addWidget(QLabel("💳 Card CVV:"), 2, 0)
        self.cvv_input = QLineEdit()
        self.cvv_input.setPlaceholderText("Enter your card CVV (3-4 digits)")
        self.cvv_input.setMaxLength(4)  # CVV is typically 3-4 digits
        layout.addWidget(self.cvv_input, 2, 1, 1, 2)

        # Browser preferences
        layout.addWidget(QLabel("🌐 Browser:"), 3, 0)
        self.browser_combo = QComboBox()
        self.browser_combo.addItems(["Chrome", "Firefox", "Edge"])
        layout.addWidget(self.browser_combo, 3, 1)

        # Headless mode
        self.headless_checkbox = QCheckBox("🔇 Headless Mode (faster, no GUI)")
        layout.addWidget(self.headless_checkbox, 4, 0, 1, 3)

        # Auto-login
        self.auto_login_checkbox = QCheckBox("🔐 Auto-login on startup")
        layout.addWidget(self.auto_login_checkbox, 5, 0, 1, 3)

        # Timeout settings
        layout.addWidget(QLabel("⏱️ Login Timeout (seconds):"), 6, 0)
        self.login_timeout_spin = QSpinBox()
        self.login_timeout_spin.setRange(10, 120)
        self.login_timeout_spin.setValue(30)
        layout.addWidget(self.login_timeout_spin, 6, 1)

        return group

    def create_account_settings(self):
        """Create account type settings section"""
        group = QGroupBox("🎯 Account Type Configuration")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                border: 2px solid #ddd;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        layout = QVBoxLayout(group)
        layout.setSpacing(15)

        # Account type patterns
        patterns_label = QLabel("📝 Configure search patterns for each account type:")
        patterns_label.setStyleSheet("font-weight: bold; margin-bottom: 10px;")
        layout.addWidget(patterns_label)

        # Create pattern inputs for each account type
        self.pattern_inputs = {}
        account_types = [
            ("Free Reset Code", "🔄", "FREE RESET, RESET CODE, FREE RESETS"),
            ("Starter", "🚀", "STARTER, STARTER ACCOUNT"),
            ("Starter Plus", "⭐", "STARTER PLUS, STARTER+"),
            ("Expert", "👑", "EXPERT, EXPERT ACCOUNT")
        ]

        for account_type, icon, default_patterns in account_types:
            type_layout = QHBoxLayout()

            type_label = QLabel(f"{icon} {account_type}:")
            type_label.setMinimumWidth(150)
            type_layout.addWidget(type_label)

            pattern_input = QLineEdit()
            pattern_input.setPlaceholderText("Comma-separated search terms")
            pattern_input.setText(default_patterns)
            self.pattern_inputs[account_type] = pattern_input
            type_layout.addWidget(pattern_input)

            layout.addLayout(type_layout)

        # Pattern info
        info_label = QLabel("💡 Use comma-separated terms. Case-insensitive matching.")
        info_label.setStyleSheet("color: #666; font-size: 10px; font-style: italic;")
        layout.addWidget(info_label)

        return group

    def create_advanced_settings(self):
        """Create advanced settings section"""
        group = QGroupBox("🔧 Advanced Settings")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                border: 2px solid #ddd;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        layout = QGridLayout(group)
        layout.setSpacing(15)

        # Performance settings
        layout.addWidget(QLabel("⚡ Max Processing Threads:"), 0, 0)
        self.threads_spin = QSpinBox()
        self.threads_spin.setRange(1, 16)
        self.threads_spin.setValue(4)
        layout.addWidget(self.threads_spin, 0, 1)

        # Memory management
        layout.addWidget(QLabel("💾 Memory Limit (MB):"), 1, 0)
        self.memory_spin = QSpinBox()
        self.memory_spin.setRange(512, 8192)
        self.memory_spin.setValue(2048)
        layout.addWidget(self.memory_spin, 1, 1)

        # Debug mode
        self.debug_checkbox = QCheckBox("🐛 Enable Debug Mode")
        layout.addWidget(self.debug_checkbox, 2, 0, 1, 2)

        # Export/Import settings
        export_layout = QHBoxLayout()
        export_btn = QPushButton("📤 Export Settings")
        export_btn.clicked.connect(self.export_settings)
        export_layout.addWidget(export_btn)

        import_btn = QPushButton("📥 Import Settings")
        import_btn.clicked.connect(self.import_settings)
        export_layout.addWidget(import_btn)

        reset_btn = QPushButton("🔄 Reset to Defaults")
        reset_btn.clicked.connect(self.reset_settings)
        export_layout.addWidget(reset_btn)

        layout.addLayout(export_layout, 3, 0, 1, 2)

        return group

    def configure_ocr_region(self):
        """Open OCR region configuration"""
        if not OCR_REGION_SELECTOR_AVAILABLE:
            QMessageBox.warning(self, "Not Available", "OCR region selector is not available")
            return

        try:
            # Get current OCR region from main window or use default
            current_region = (0.0, 67.0, 100.0, 33.0)  # Default region
            if self.main_window:
                # Try to get current region from active tab
                current_tab = self.main_window.tab_widget.currentWidget()
                if hasattr(current_tab, 'current_ocr_region'):
                    current_region = current_tab.current_ocr_region
                elif hasattr(self.main_window.livestream_tab, 'current_ocr_region'):
                    current_region = self.main_window.livestream_tab.current_ocr_region

            # Open the OCR region selector dialog
            dialog = OCRRegionSelectorDialog(self, initial_region=current_region)
            if dialog.exec() == QDialog.DialogCode.Accepted:
                selected_region = dialog.get_selected_region()

                # Update all tabs with the new region
                if self.main_window:
                    if hasattr(self.main_window.livestream_tab, 'current_ocr_region'):
                        self.main_window.livestream_tab.current_ocr_region = selected_region
                    if hasattr(self.main_window.testing_tab, 'current_ocr_region'):
                        self.main_window.testing_tab.current_ocr_region = selected_region
                        if hasattr(self.main_window.testing_tab, 'update_region_display'):
                            self.main_window.testing_tab.update_region_display()

                # Show confirmation
                x, y, w, h = selected_region
                QMessageBox.information(self, "OCR Region Updated",
                                      f"OCR region updated successfully!\n\n"
                                      f"Position: {x:.1f}%, {y:.1f}%\n"
                                      f"Size: {w:.1f}% × {h:.1f}%\n\n"
                                      f"This region will be used for code detection in all tabs.")

                # Save the region setting
                self.save_ocr_region_setting(selected_region)

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to open OCR region selector: {e}")

    def save_ocr_region_setting(self, region):
        """Save OCR region setting"""
        try:
            settings = QSettings()
            x, y, w, h = region
            settings.setValue("ocr_region_x", x)
            settings.setValue("ocr_region_y", y)
            settings.setValue("ocr_region_width", w)
            settings.setValue("ocr_region_height", h)
            settings.sync()
        except Exception as e:
            print(f"Failed to save OCR region setting: {e}")

    def save_settings(self):
        """Save all settings to QSettings"""
        try:
            settings = QSettings()
            current_settings = self.get_settings()

            # Save general settings
            general = current_settings.get('general', {})
            settings.setValue("general/auto_start", general.get('auto_start', False))
            settings.setValue("general/logging_level", general.get('logging_level', 'Info'))
            settings.setValue("general/theme", general.get('theme', 'Default'))
            settings.setValue("general/auto_save", general.get('auto_save', True))
            settings.setValue("general/auto_save_layout", general.get('auto_save_layout', True))

            # Save layout settings if auto-save layout is enabled
            if general.get('auto_save_layout', True):
                self.save_layout_settings(settings)

            # Save OCR settings
            ocr = current_settings.get('ocr', {})
            settings.setValue("ocr/engine", ocr.get('engine', 'EasyOCR'))
            settings.setValue("ocr/gpu_enabled", ocr.get('gpu_enabled', True))
            settings.setValue("ocr/confidence_threshold", ocr.get('confidence_threshold', 80))

            # Save browser settings
            browser = current_settings.get('browser', {})
            cvv_value = browser.get('cvv', '')
            settings.setValue("browser/username", browser.get('username', ''))
            settings.setValue("browser/password", browser.get('password', ''))
            settings.setValue("browser/cvv", cvv_value)
            settings.setValue("browser/browser_type", browser.get('browser_type', 'Chrome'))
            settings.setValue("browser/headless", browser.get('headless', False))
            settings.setValue("browser/auto_login", browser.get('auto_login', False))
            settings.setValue("browser/login_timeout", browser.get('login_timeout', 30))

            # Debug: Verify CVV is being saved
            if cvv_value:
                print(f"💳 CVV saved to settings: {'*' * len(cvv_value)}")
            else:
                print("💳 CVV is empty - not saving")

            # Save account patterns
            patterns = current_settings.get('account_patterns', {})
            for account_type, pattern in patterns.items():
                settings.setValue(f"patterns/{account_type}", pattern)

            # Save advanced settings
            advanced = current_settings.get('advanced', {})
            settings.setValue("advanced/max_threads", advanced.get('max_threads', 4))
            settings.setValue("advanced/memory_limit", advanced.get('memory_limit', 2048))
            settings.setValue("advanced/debug_mode", advanced.get('debug_mode', False))

            settings.sync()
            return True

        except Exception as e:
            print(f"Failed to save settings: {e}")
            return False

    def save_layout_settings(self, settings):
        """Save window layout and sizing settings"""
        try:
            # Get reference to main window
            main_window = None
            if hasattr(self, 'main_window') and self.main_window:
                main_window = self.main_window
            elif hasattr(self, 'parent') and self.parent():
                # Try to find main window through parent hierarchy
                parent = self.parent()
                while parent and not isinstance(parent, QMainWindow):
                    parent = parent.parent()
                if parent:
                    main_window = parent

            # Save main window geometry if found
            if main_window:
                settings.setValue("layout/main_window_geometry", main_window.saveGeometry())
                settings.setValue("layout/main_window_state", main_window.saveState())
                print(f"💾 Main window geometry saved: {main_window.size().width()}x{main_window.size().height()}")

            # Save splitter states if they exist
            if hasattr(self, 'main_splitter'):
                settings.setValue("layout/main_splitter_sizes", self.main_splitter.sizes())

            # Save tab widget current index
            if hasattr(self, 'tab_widget'):
                settings.setValue("layout/current_tab", self.tab_widget.currentIndex())

            print("💾 Layout settings saved")

        except Exception as e:
            print(f"Failed to save layout settings: {e}")

    def load_layout_settings(self, settings):
        """Load window layout and sizing settings"""
        try:
            # Get reference to main window
            main_window = None
            if hasattr(self, 'main_window') and self.main_window:
                main_window = self.main_window
            elif hasattr(self, 'parent') and self.parent():
                # Try to find main window through parent hierarchy
                parent = self.parent()
                while parent and not isinstance(parent, QMainWindow):
                    parent = parent.parent()
                if parent:
                    main_window = parent

            # Load main window geometry if found
            if main_window:
                geometry = settings.value("layout/main_window_geometry")
                if geometry:
                    main_window.restoreGeometry(geometry)
                    print(f"📐 Main window geometry restored: {main_window.size().width()}x{main_window.size().height()}")

                state = settings.value("layout/main_window_state")
                if state:
                    main_window.restoreState(state)

            # Load splitter states
            if hasattr(self, 'main_splitter'):
                sizes = settings.value("layout/main_splitter_sizes")
                if sizes:
                    # Convert to integers if they're strings
                    try:
                        sizes = [int(size) for size in sizes]
                        self.main_splitter.setSizes(sizes)
                    except (ValueError, TypeError):
                        pass  # Use default sizes

            # Load tab widget current index
            if hasattr(self, 'tab_widget'):
                current_tab = settings.value("layout/current_tab", 0, type=int)
                if 0 <= current_tab < self.tab_widget.count():
                    self.tab_widget.setCurrentIndex(current_tab)

            print("📐 Layout settings loaded")

        except Exception as e:
            print(f"Failed to load layout settings: {e}")

    def load_settings(self):
        """Load all settings from QSettings"""
        try:
            settings = QSettings()

            # Load general settings
            self.auto_start_checkbox.setChecked(settings.value("general/auto_start", False, type=bool))
            self.logging_combo.setCurrentText(settings.value("general/logging_level", "Info", type=str))
            self.theme_combo.setCurrentText(settings.value("general/theme", "Default", type=str))
            self.auto_save_checkbox.setChecked(settings.value("general/auto_save", True, type=bool))
            self.auto_save_layout_checkbox.setChecked(settings.value("general/auto_save_layout", True, type=bool))

            # Load layout settings if auto-save layout is enabled
            if settings.value("general/auto_save_layout", True, type=bool):
                self.load_layout_settings(settings)

            # Load OCR settings
            self.ocr_engine_combo.setCurrentText(settings.value("ocr/engine", "EasyOCR", type=str))
            self.gpu_checkbox.setChecked(settings.value("ocr/gpu_enabled", True, type=bool))
            self.confidence_slider.setValue(settings.value("ocr/confidence_threshold", 80, type=int))

            # Load browser settings
            self.username_input.setText(settings.value("browser/username", "", type=str))
            self.password_input.setText(settings.value("browser/password", "", type=str))

            # Load CVV with debug output
            cvv_value = settings.value("browser/cvv", "", type=str)
            self.cvv_input.setText(cvv_value)
            if cvv_value:
                print(f"💳 CVV loaded from settings: {'*' * len(cvv_value)}")
            else:
                print("💳 No CVV found in settings")

            self.browser_combo.setCurrentText(settings.value("browser/browser_type", "Chrome", type=str))
            self.headless_checkbox.setChecked(settings.value("browser/headless", False, type=bool))
            self.auto_login_checkbox.setChecked(settings.value("browser/auto_login", False, type=bool))
            self.login_timeout_spin.setValue(settings.value("browser/login_timeout", 30, type=int))

            # Load account patterns
            default_patterns = {
                "Free Reset Code": "FREE RESET, RESET CODE, FREE RESETS",
                "Starter": "STARTER, STARTER ACCOUNT",
                "Starter Plus": "STARTER PLUS, STARTER+",
                "Expert": "EXPERT, EXPERT ACCOUNT"
            }
            for account_type, default_pattern in default_patterns.items():
                pattern = settings.value(f"patterns/{account_type}", default_pattern, type=str)
                if account_type in self.pattern_inputs:
                    self.pattern_inputs[account_type].setText(pattern)

            # Load advanced settings
            self.threads_spin.setValue(settings.value("advanced/max_threads", 4, type=int))
            self.memory_spin.setValue(settings.value("advanced/memory_limit", 2048, type=int))
            self.debug_checkbox.setChecked(settings.value("advanced/debug_mode", False, type=bool))

            return True

        except Exception as e:
            print(f"Failed to load settings: {e}")
            return False

    def export_settings(self):
        """Export settings to file"""
        try:
            from PyQt6.QtWidgets import QFileDialog
            import json

            filename, _ = QFileDialog.getSaveFileName(
                self, "Export Settings", "mffuhijack_settings.json",
                "JSON Files (*.json);;All Files (*)"
            )

            if filename:
                settings_data = self.get_settings()
                with open(filename, 'w') as f:
                    json.dump(settings_data, f, indent=2)

                QMessageBox.information(self, "Export Successful",
                                      f"Settings exported successfully to:\n{filename}")

        except Exception as e:
            QMessageBox.critical(self, "Export Failed", f"Failed to export settings:\n{str(e)}")

    def import_settings(self):
        """Import settings from file"""
        try:
            from PyQt6.QtWidgets import QFileDialog
            import json

            filename, _ = QFileDialog.getOpenFileName(
                self, "Import Settings", "",
                "JSON Files (*.json);;All Files (*)"
            )

            if filename:
                with open(filename, 'r') as f:
                    settings_data = json.load(f)

                self.apply_settings(settings_data)
                self.save_settings()  # Save to QSettings

                QMessageBox.information(self, "Import Successful",
                                      f"Settings imported successfully from:\n{filename}")

        except Exception as e:
            QMessageBox.critical(self, "Import Failed", f"Failed to import settings:\n{str(e)}")

    def connect_auto_save(self):
        """Connect all settings controls to auto-save functionality"""
        # General settings
        self.auto_start_checkbox.toggled.connect(self.on_setting_changed)
        self.logging_combo.currentTextChanged.connect(self.on_setting_changed)
        self.theme_combo.currentTextChanged.connect(self.on_setting_changed)
        self.auto_save_checkbox.toggled.connect(self.on_setting_changed)
        self.auto_save_layout_checkbox.toggled.connect(self.on_setting_changed)

        # OCR settings
        self.ocr_engine_combo.currentTextChanged.connect(self.on_setting_changed)
        self.gpu_checkbox.toggled.connect(self.on_setting_changed)
        self.confidence_slider.valueChanged.connect(self.on_setting_changed)

        # Browser settings
        self.username_input.textChanged.connect(self.on_setting_changed)
        self.password_input.textChanged.connect(self.on_setting_changed)
        self.cvv_input.textChanged.connect(self.on_setting_changed)
        self.browser_combo.currentTextChanged.connect(self.on_setting_changed)
        self.headless_checkbox.toggled.connect(self.on_setting_changed)
        self.auto_login_checkbox.toggled.connect(self.on_setting_changed)
        self.login_timeout_spin.valueChanged.connect(self.on_setting_changed)

        # Account patterns
        for pattern_input in self.pattern_inputs.values():
            pattern_input.textChanged.connect(self.on_setting_changed)

        # Advanced settings
        self.threads_spin.valueChanged.connect(self.on_setting_changed)
        self.memory_spin.valueChanged.connect(self.on_setting_changed)
        self.debug_checkbox.toggled.connect(self.on_setting_changed)

    def on_setting_changed(self):
        """Handle setting change - validate and auto-save if enabled"""
        # Validate the changed setting
        if self.validate_current_settings():
            # Save if auto-save is enabled
            if hasattr(self, 'auto_save_checkbox') and self.auto_save_checkbox.isChecked():
                # Debug: Check if CVV changed
                cvv = self.cvv_input.text().strip()
                if cvv:
                    print(f"💳 CVV changed, auto-saving: {'*' * len(cvv)}")
                self.save_settings()

    def validate_current_settings(self):
        """Validate all current settings and show warnings if needed"""
        try:
            # Validate username/password/CVV
            username = self.username_input.text().strip()
            password = self.password_input.text().strip()
            cvv = self.cvv_input.text().strip()

            if username and not password:
                self.show_validation_warning("Password is required when username is provided")
                return False
            elif password and not username:
                self.show_validation_warning("Username is required when password is provided")
                return False

            # Validate CVV format if provided
            if cvv and not cvv.isdigit():
                self.show_validation_warning("CVV must contain only digits")
                return False
            elif cvv and (len(cvv) < 3 or len(cvv) > 4):
                self.show_validation_warning("CVV must be 3 or 4 digits")
                return False

            # Validate confidence threshold
            confidence = self.confidence_slider.value()
            if confidence < 50:
                self.show_validation_warning("Confidence threshold should be at least 50% for reliable detection")

            # Validate thread count
            threads = self.threads_spin.value()
            if threads > 8:
                self.show_validation_warning("High thread count may impact system performance")

            # Validate memory limit
            memory = self.memory_spin.value()
            if memory < 1024:
                self.show_validation_warning("Memory limit below 1GB may cause performance issues")
            elif memory > 4096:
                self.show_validation_warning("High memory limit may impact other applications")

            # Validate account patterns
            for account_type, pattern_input in self.pattern_inputs.items():
                pattern = pattern_input.text().strip()
                if not pattern:
                    self.show_validation_warning(f"Pattern for {account_type} is empty - this account type will not be detected")
                elif len(pattern.split(',')) > 10:
                    self.show_validation_warning(f"Too many patterns for {account_type} - consider consolidating")

            # Validate login timeout
            timeout = self.login_timeout_spin.value()
            if timeout < 15:
                self.show_validation_warning("Login timeout below 15 seconds may be too short for CAPTCHA completion")

            return True

        except Exception as e:
            print(f"Validation error: {e}")
            return True  # Don't block saving on validation errors

    def show_validation_warning(self, message):
        """Show validation warning to user"""
        try:
            # For now, just print the warning
            # In a full implementation, you might show a status bar message or tooltip
            print(f"Settings validation warning: {message}")
        except Exception as e:
            print(f"Error showing validation warning: {e}")

    def validate_and_save_settings(self):
        """Validate settings before saving"""
        if self.validate_current_settings():
            if self.save_settings():
                QMessageBox.information(self, "Settings Saved", "Settings have been saved successfully!")
                return True
            else:
                QMessageBox.warning(self, "Save Failed", "Failed to save settings. Please try again.")
                return False
        return False

    def reset_settings(self):
        """Reset all settings to defaults"""
        # Reset all controls to default values
        self.auto_start_checkbox.setChecked(False)
        self.logging_combo.setCurrentText("Info")
        self.theme_combo.setCurrentText("Default")
        self.auto_save_checkbox.setChecked(True)

        self.ocr_engine_combo.setCurrentText("EasyOCR")
        self.gpu_checkbox.setChecked(True)
        self.confidence_slider.setValue(80)

        self.username_input.clear()
        self.password_input.clear()
        self.cvv_input.clear()
        self.browser_combo.setCurrentText("Chrome")
        self.headless_checkbox.setChecked(False)
        self.auto_login_checkbox.setChecked(False)
        self.login_timeout_spin.setValue(30)

        # Reset pattern inputs to defaults
        default_patterns = {
            "Free Reset Code": "FREE RESET, RESET CODE, FREE RESETS",
            "Starter": "STARTER, STARTER ACCOUNT",
            "Starter Plus": "STARTER PLUS, STARTER+",
            "Expert": "EXPERT, EXPERT ACCOUNT"
        }
        for account_type, pattern in default_patterns.items():
            if account_type in self.pattern_inputs:
                self.pattern_inputs[account_type].setText(pattern)

        self.threads_spin.setValue(4)
        self.memory_spin.setValue(2048)
        self.debug_checkbox.setChecked(False)

    def get_settings(self):
        """Get all current settings as a dictionary"""
        return {
            'general': {
                'auto_start': self.auto_start_checkbox.isChecked(),
                'logging_level': self.logging_combo.currentText(),
                'theme': self.theme_combo.currentText(),
                'auto_save': self.auto_save_checkbox.isChecked(),
                'auto_save_layout': self.auto_save_layout_checkbox.isChecked()
            },
            'ocr': {
                'engine': self.ocr_engine_combo.currentText(),
                'gpu_enabled': self.gpu_checkbox.isChecked(),
                'confidence_threshold': self.confidence_slider.value()
            },
            'browser': {
                'username': self.username_input.text(),
                'password': self.password_input.text(),
                'cvv': self.cvv_input.text(),
                'browser_type': self.browser_combo.currentText(),
                'headless': self.headless_checkbox.isChecked(),
                'auto_login': self.auto_login_checkbox.isChecked(),
                'login_timeout': self.login_timeout_spin.value()
            },
            'account_patterns': {
                account_type: input_widget.text()
                for account_type, input_widget in self.pattern_inputs.items()
            },
            'advanced': {
                'max_threads': self.threads_spin.value(),
                'memory_limit': self.memory_spin.value(),
                'debug_mode': self.debug_checkbox.isChecked()
            }
        }

    def apply_settings(self, settings):
        """Apply settings from a dictionary"""
        try:
            # General settings
            general = settings.get('general', {})
            self.auto_start_checkbox.setChecked(general.get('auto_start', False))
            self.logging_combo.setCurrentText(general.get('logging_level', 'Info'))
            self.theme_combo.setCurrentText(general.get('theme', 'Default'))
            self.auto_save_checkbox.setChecked(general.get('auto_save', True))

            # OCR settings
            ocr = settings.get('ocr', {})
            self.ocr_engine_combo.setCurrentText(ocr.get('engine', 'EasyOCR'))
            self.gpu_checkbox.setChecked(ocr.get('gpu_enabled', True))
            self.confidence_slider.setValue(ocr.get('confidence_threshold', 80))

            # Browser settings
            browser = settings.get('browser', {})
            self.username_input.setText(browser.get('username', ''))
            self.password_input.setText(browser.get('password', ''))
            self.cvv_input.setText(browser.get('cvv', ''))
            self.browser_combo.setCurrentText(browser.get('browser_type', 'Chrome'))
            self.headless_checkbox.setChecked(browser.get('headless', False))
            self.auto_login_checkbox.setChecked(browser.get('auto_login', False))
            self.login_timeout_spin.setValue(browser.get('login_timeout', 30))

            # Account patterns
            patterns = settings.get('account_patterns', {})
            for account_type, pattern in patterns.items():
                if account_type in self.pattern_inputs:
                    self.pattern_inputs[account_type].setText(pattern)

            # Advanced settings
            advanced = settings.get('advanced', {})
            self.threads_spin.setValue(advanced.get('max_threads', 4))
            self.memory_spin.setValue(advanced.get('memory_limit', 2048))
            self.debug_checkbox.setChecked(advanced.get('debug_mode', False))

        except Exception as e:
            print(f"Error applying settings: {e}")


class StreamTestingWorker(QThread):
    """Worker thread for stream testing"""

    # Signals for communication with main thread
    progress_update = pyqtSignal(int)  # Progress percentage
    detection_log = pyqtSignal(str)    # Log messages
    validation_result = pyqtSignal(str, str, str, str, str)  # code, type, status, message, time
    status_update = pyqtSignal(str, int, int, int)  # current_time, frames_processed, codes_detected, codes_validated
    frame_update = pyqtSignal(int, str, str)  # frame_number, timestamp, ocr_result
    finished = pyqtSignal()

    def __init__(self, stream_path: str, frame_interval: float, ocr_engine: str, ocr_region: tuple, validate_codes: bool, start_time_seconds: float = 0.0, preview_window=None, username: str = "", password: str = ""):
        super().__init__()
        self.stream_path = stream_path
        self.frame_interval = frame_interval
        self.ocr_engine = ocr_engine
        self.ocr_region = ocr_region
        self.validate_codes = validate_codes
        self.start_time_seconds = start_time_seconds
        self.preview_window = preview_window
        self.username = username
        self.password = password
        self.running = False

        # Initialize components
        self.stream_processor = None
        self.code_validator = None
        self.statistics = None
        self.browser_automation = None

        if TESTING_MODE_AVAILABLE:
            from livestream_testing_mode import StreamProcessor, CodeValidationTester, TestingStatistics
            self.stream_processor = StreamProcessor()
            if validate_codes:
                self.code_validator = CodeValidationTester()
            self.statistics = TestingStatistics()

        # Initialize browser automation for code validation
        if validate_codes and SUBMITTER_AVAILABLE:
            try:
                from browser_automation import BrowserAutomation
                self.browser_automation = BrowserAutomation()

                # Set browser to headless mode for stream testing to hide the window
                self.browser_automation.set_visibility(False)
                self.detection_log.emit("🌐 Browser automation initialized for code validation (hidden mode)")

                # Initialize browser for testing
                if self.browser_automation.initialize_browser():
                    self.detection_log.emit("✅ Browser ready for code validation testing (running in background)")

                    # Login if credentials are provided
                    if self.username and self.password:
                        self.detection_log.emit(f"🔐 Logging in as {self.username}...")
                        login_success = self.browser_automation.login_to_mff(self.username, self.password)
                        if login_success:
                            self.detection_log.emit("✅ Login initiated - please complete CAPTCHA if present")
                            # Wait for login completion
                            if self.browser_automation.wait_for_login_completion(timeout=30):
                                self.detection_log.emit("✅ Login completed successfully")

                                # Preload account pages for faster validation
                                account_types = ["Starter", "Starter Plus", "Expert"]
                                if self.browser_automation.preload_account_pages(account_types):
                                    self.detection_log.emit("✅ Account pages preloaded for instant validation")
                                else:
                                    self.detection_log.emit("⚠️ Failed to preload account pages")
                            else:
                                self.detection_log.emit("⚠️ Login timeout - continuing without login")
                        else:
                            self.detection_log.emit("❌ Login failed - continuing without login")
                    else:
                        self.detection_log.emit("ℹ️ No credentials provided - browser validation will work without login")

                else:
                    self.detection_log.emit("❌ Failed to initialize browser")
                    self.browser_automation = None

            except Exception as e:
                self.detection_log.emit(f"⚠️ Browser automation not available: {e}")
                self.browser_automation = None

    def run(self):
        """Main testing loop"""
        if not TESTING_MODE_AVAILABLE or not self.stream_processor:
            self.detection_log.emit("❌ Testing mode components not available")
            self.finished.emit()
            return

        self.running = True
        self.detection_log.emit(f"🎬 Loading stream: {os.path.basename(self.stream_path)}")

        try:
            # Load video stream
            if not self.stream_processor.load_stream(self.stream_path):
                self.detection_log.emit("❌ Failed to load video stream")
                self.finished.emit()
                return

            total_frames = self.stream_processor.total_frames
            frame_rate = self.stream_processor.cap.get(cv2.CAP_PROP_FPS) if self.stream_processor.cap else 30
            # Calculate how many frames to skip based on the interval setting from textbox
            # For example: 5 second interval * 30 FPS = skip 150 frames between scans
            interval_frames = int(self.frame_interval * frame_rate)

            # Calculate start frame from start time
            start_frame = int(self.start_time_seconds * frame_rate)
            if start_frame >= total_frames:
                self.detection_log.emit(f"❌ Start time ({self.start_time_seconds}s) exceeds video duration")
                self.finished.emit()
                return

            # Convert start time to HH:MM:SS for display
            start_hours = int(self.start_time_seconds // 3600)
            start_minutes = int((self.start_time_seconds % 3600) // 60)
            start_secs = int(self.start_time_seconds % 60)
            start_time_str = f"{start_hours:02d}:{start_minutes:02d}:{start_secs:02d}"

            self.detection_log.emit(f"📊 Stream info: {total_frames} frames, {frame_rate:.1f} FPS")
            self.detection_log.emit(f"⏰ Starting from: {start_time_str} (frame {start_frame})")
            self.detection_log.emit(f"⏱️ Testing every {self.frame_interval}s ({interval_frames} frames)")

            frames_processed = 0
            codes_detected = 0
            codes_validated = 0
            current_frame = start_frame

            while self.running and current_frame < total_frames:
                # Check if preview window is in manual navigation mode (not automated testing)
                # The automated testing should continue regardless of the "Use Interval" setting
                # since that only affects the preview window playback behavior

                # Set frame position
                self.stream_processor.cap.set(cv2.CAP_PROP_POS_FRAMES, current_frame)

                # Read frame
                ret, frame = self.stream_processor.cap.read()
                if not ret:
                    break

                # Calculate current time in video
                current_time_seconds = current_frame / frame_rate
                current_time = str(timedelta(seconds=int(current_time_seconds)))

                # Process frame with OCR
                if OCR_AVAILABLE:
                    try:
                        # Crop frame to OCR region
                        h, w = frame.shape[:2]
                        x_percent, y_percent, w_percent, h_percent = self.ocr_region
                        x1 = int((x_percent / 100) * w)
                        y1 = int((y_percent / 100) * h)
                        x2 = int(((x_percent + w_percent) / 100) * w)
                        y2 = int(((y_percent + h_percent) / 100) * h)

                        cropped_frame = frame[y1:y2, x1:x2]

                        # Perform OCR with raw image processing for best accuracy
                        from ocr_utils import ocr_manager
                        ocr_results = ocr_manager.extract_text_from_image(cropped_frame, "custom", use_raw_image=True)

                        # Extract text from OCR results
                        detected_text = ""
                        for result in ocr_results:
                            if 'text' in result:
                                detected_text += result['text'] + " "
                        detected_text = detected_text.strip()

                        # Debug logging
                        if detected_text:
                            self.detection_log.emit(f"🔍 [{current_time}] OCR detected: '{detected_text}'")

                        # Emit frame update for preview window
                        self.frame_update.emit(current_frame, current_time, detected_text if detected_text else "No text detected")

                        if detected_text and len(detected_text.strip()) > 5:
                            # Check for codes
                            codes = self.extract_codes_from_text(detected_text)

                            for code_info in codes:
                                codes_detected += 1
                                code = code_info['code']
                                account_type = code_info['type']

                                # Update statistics object if available
                                if self.statistics:
                                    code_data = {
                                        'code': code,
                                        'type': account_type,
                                        'confidence': 0.9,  # Default confidence
                                        'timestamp': current_time
                                    }
                                    self.statistics.add_code_detected(code_data)

                                self.detection_log.emit(f"🔍 [{current_time}] Detected {account_type}: {code}")

                                # Validate code with browser automation if enabled
                                if self.validate_codes:
                                    try:
                                        validation_result = self.validate_code_with_browser(code, account_type, current_time)
                                        status = validation_result['status']
                                        message = validation_result['message']
                                        codes_validated += 1

                                        # Update statistics object if available
                                        if self.statistics:
                                            validation_data = {
                                                'code': code,
                                                'valid': '✅' in status,
                                                'message': message,
                                                'timestamp': current_time
                                            }
                                            self.statistics.add_validation_result(validation_data)

                                        self.validation_result.emit(code, account_type, status, message, current_time)
                                        self.detection_log.emit(f"✅ [{current_time}] Browser validation {code}: {status}")

                                        # Stop scanning after first code validation (as requested)
                                        self.detection_log.emit(f"🛑 Stopping scan after code validation as requested")
                                        self.running = False
                                        break

                                    except Exception as e:
                                        self.validation_result.emit(code, account_type, "❌ Error", str(e), current_time)
                                        self.detection_log.emit(f"❌ [{current_time}] Validation error for {code}: {e}")
                                else:
                                    # Just log detection without validation
                                    self.validation_result.emit(code, account_type, "📝 Detected", "Code detected (validation disabled)", current_time)

                    except Exception as e:
                        self.detection_log.emit(f"❌ OCR error at {current_time}: {e}")

                frames_processed += 1

                # Update statistics object if available
                if self.statistics:
                    self.statistics.add_frame_processed(0.01)  # Add minimal processing time

                # Advance to next frame based on interval setting (e.g., every 5 seconds = skip 150 frames)
                current_frame += interval_frames

                # Update progress and status
                progress = int((current_frame / total_frames) * 100)
                self.progress_update.emit(progress)
                self.status_update.emit(current_time, frames_processed, codes_detected, codes_validated)

                # Small delay to prevent overwhelming the UI
                self.msleep(10)

            # Get final statistics from statistics object if available
            if self.statistics:
                final_frames = self.statistics.frames_processed
                final_codes = self.statistics.codes_detected
                final_validated = self.statistics.codes_validated
                self.detection_log.emit(f"✅ Testing completed! Processed {final_frames} frames, detected {final_codes} codes, validated {final_validated} codes")
            else:
                self.detection_log.emit(f"✅ Testing completed! Processed {frames_processed} frames, detected {codes_detected} codes, validated {codes_validated} codes")

        except Exception as e:
            self.detection_log.emit(f"❌ Testing error: {e}")

        finally:
            if self.stream_processor:
                self.stream_processor.close()
            if self.code_validator:
                self.code_validator.close()
            self.finished.emit()

    def extract_codes_from_text(self, text: str) -> list:
        """Extract codes from OCR text with improved pattern matching"""
        codes = []
        text_upper = text.upper()

        # Debug logging
        self.detection_log.emit(f"🔍 Analyzing text for codes: '{text}'")

        # Multiple patterns to look for codes
        import re
        patterns = [
            # Standard "CODE:" pattern
            r'CODE:\s*([A-Z0-9]{4,8})',
            # "USE CODE" pattern
            r'USE\s+CODE\s*:?\s*([A-Z0-9]{4,8})',
            # Direct code patterns (common formats)
            r'\b([A-Z0-9]{6,8})\b',  # 6-8 character codes
            r'([A-Z]+\d+[A-Z]*)',    # Letter+number combinations
            r'(RESET\d+[A-Z]*)',     # RESET codes
            r'(START\d+[A-Z]*)',     # START codes
            r'(PLUS\d+[A-Z]*)',      # PLUS codes
        ]

        for pattern in patterns:
            matches = re.findall(pattern, text_upper)
            for match in matches:
                if len(match) >= 4:  # Minimum code length
                    # Determine account type based on context
                    account_type = "Unknown"
                    if any(word in text_upper for word in ['RESET', 'FREE RESET']):
                        account_type = "Free Reset Code"
                    elif 'STARTER PLUS' in text_upper or 'PLUS' in match:
                        account_type = "Starter Plus"
                    elif 'STARTER' in text_upper or 'START' in match:
                        account_type = "Starter"
                    elif 'EXPERT' in text_upper:
                        account_type = "Expert"

                    code_info = {
                        'code': match,
                        'type': account_type,
                        'context': text,
                        'pattern': pattern
                    }

                    # Avoid duplicates
                    if not any(c['code'] == match for c in codes):
                        codes.append(code_info)
                        self.detection_log.emit(f"✅ Found code: {match} ({account_type}) using pattern: {pattern}")

        if not codes:
            self.detection_log.emit(f"❌ No codes found in text: '{text}'")

        return codes

    def validate_code_with_browser(self, code: str, account_type: str, timestamp: str) -> dict:
        """Validate code using browser automation (same as live scanning but without checkout)"""
        self.detection_log.emit(f"🌐 [{timestamp}] Starting browser validation for {code} ({account_type})")

        if not self.browser_automation:
            return {
                'status': '⚠️ No Browser',
                'message': 'Browser automation not available'
            }

        try:
            # Preload browser and navigate to checkout page (same as live scanning)
            self.detection_log.emit(f"🌐 [{timestamp}] Preloading browser for {account_type}...")

            # Map account type to URL (same logic as live scanning)
            account_urls = {
                "Free Reset Code": "https://myfondedfutures.com/reset-account",
                "Starter": "https://myfondedfutures.com/starter-account",
                "Starter Plus": "https://myfondedfutures.com/starter-plus-account",
                "Expert": "https://myfondedfutures.com/expert-account"
            }

            target_url = account_urls.get(account_type, account_urls["Starter"])

            # Navigate to the target URL first
            self.browser_automation.driver.get(target_url)

            # For standard account types, navigate through checkout process
            if account_type in ["Starter", "Starter Plus", "Expert"]:
                success = self.browser_automation._navigate_to_checkout()
                if not success:
                    return {
                        'status': '❌ Navigation Failed',
                        'message': 'Could not navigate to checkout page'
                    }
            else:
                # For Reset accounts, just wait for page to load
                from selenium.webdriver.support.ui import WebDriverWait
                from selenium.webdriver.support import expected_conditions as EC
                from selenium.webdriver.common.by import By
                try:
                    WebDriverWait(self.browser_automation.driver, 5).until(
                        EC.presence_of_element_located((By.TAG_NAME, "body"))
                    )
                except:
                    return {
                        'status': '❌ Page Load Failed',
                        'message': 'Could not load account page'
                    }

            self.detection_log.emit(f"🌐 [{timestamp}] Browser loaded, applying code {code}...")

            # Apply the code (same as live scanning)
            code_result = self.browser_automation.apply_discount_code(code)

            if code_result['success']:
                # Code was applied successfully
                discount_amount = code_result.get('discount_amount', 'Unknown')
                self.detection_log.emit(f"✅ [{timestamp}] Code {code} applied! Discount: {discount_amount}")

                # DO NOT CLICK CHECKOUT - Just validate and stop
                self.detection_log.emit(f"🛑 [{timestamp}] Code validation complete - NOT purchasing (test mode)")

                return {
                    'status': '✅ Valid & Applied',
                    'message': f'Code applied successfully. Discount: {discount_amount}'
                }
            else:
                # Code failed to apply
                error_message = code_result.get('error', 'Unknown error')
                self.detection_log.emit(f"❌ [{timestamp}] Code {code} failed: {error_message}")

                return {
                    'status': '❌ Invalid',
                    'message': f'Code rejected: {error_message}'
                }

        except Exception as e:
            self.detection_log.emit(f"❌ [{timestamp}] Browser validation error: {e}")
            return {
                'status': '❌ Error',
                'message': f'Validation error: {str(e)}'
            }

        finally:
            # Clean up browser (close tabs but keep browser open for next test)
            try:
                if self.browser_automation:
                    self.browser_automation.cleanup_after_test()
                    self.detection_log.emit(f"🧹 [{timestamp}] Browser cleaned up")
            except Exception as e:
                self.detection_log.emit(f"⚠️ [{timestamp}] Browser cleanup warning: {e}")

    def stop(self):
        """Stop the testing process"""
        self.running = False

        # Clean up browser automation
        if self.browser_automation:
            try:
                self.browser_automation.close()
                self.detection_log.emit("🌐 Browser automation closed")
            except Exception as e:
                self.detection_log.emit(f"⚠️ Browser cleanup error: {e}")


class StreamPreviewWindow(QDialog):
    """Popup window for stream preview during testing"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("🎬 Stream Testing Preview")
        self.setWindowFlags(Qt.WindowType.Window | Qt.WindowType.WindowStaysOnTopHint)

        # Responsive sizing
        self.setMinimumSize(600, 400)
        self.resize(800, 600)
        self.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)

        # Video preview variables
        self.video_cap = None
        self.current_frame_number = 0
        self.total_frames = 0
        self.fps = 30.0
        self.is_playing = False
        self.preview_timer = QTimer()
        self.preview_timer.timeout.connect(self.advance_frame_during_playback)
        self.current_ocr_region = self.load_ocr_region_settings()

        self.init_ui()

    def load_ocr_region_settings(self):
        """Load OCR region settings from QSettings"""
        try:
            settings = QSettings()
            x = settings.value("ocr_region_x", 0.0, type=float)
            y = settings.value("ocr_region_y", 67.0, type=float)
            w = settings.value("ocr_region_width", 100.0, type=float)
            h = settings.value("ocr_region_height", 33.0, type=float)

            # Validate region values
            x = max(0.0, min(100.0, x))
            y = max(0.0, min(100.0, y))
            w = max(5.0, min(100.0 - x, w))
            h = max(5.0, min(100.0 - y, h))

            return (x, y, w, h)
        except Exception as e:
            print(f"Failed to load OCR region settings: {e}")
            return (0.0, 67.0, 100.0, 33.0)  # Default region

    def init_ui(self):
        """Initialize the preview window UI"""
        layout = QVBoxLayout(self)

        # Title
        title_label = QLabel("🎬 Stream Testing Preview")
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; padding: 10px;")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)

        # Video preview area
        self.video_preview = QLabel()
        self.video_preview.setMinimumSize(720, 405)
        self.video_preview.setMaximumSize(720, 405)
        self.video_preview.setStyleSheet("border: 2px solid #ccc; background-color: #000;")
        self.video_preview.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.video_preview.setText("📹 Loading stream preview...")
        layout.addWidget(self.video_preview, alignment=Qt.AlignmentFlag.AlignCenter)

        # Manual controls
        controls_group = QGroupBox("🎮 Manual Controls")
        controls_layout = QVBoxLayout(controls_group)

        # Top controls row
        top_controls = QHBoxLayout()

        # Playback speed
        speed_label = QLabel("Speed:")
        top_controls.addWidget(speed_label)

        self.speed_combo = QComboBox()
        # Generate speed options from 2x to 50x in intervals of 2
        speed_options = ["0.25x", "0.5x", "1x"]
        speed_options.extend([f"{i}x" for i in range(2, 51, 2)])  # 2x, 4x, 6x, ... 50x
        self.speed_combo.addItems(speed_options)
        self.speed_combo.setCurrentText("1x")
        top_controls.addWidget(self.speed_combo)

        top_controls.addWidget(QLabel("  |  "))

        # Frame interval control for automated scanning
        interval_label = QLabel("Scan Interval:")
        top_controls.addWidget(interval_label)

        self.interval_spin = QDoubleSpinBox()
        self.interval_spin.setRange(1.0, 60.0)
        self.interval_spin.setValue(5.0)
        self.interval_spin.setSuffix(" sec")
        self.interval_spin.setToolTip("Frame scanning interval when using Play button for automated scanning")
        top_controls.addWidget(self.interval_spin)

        top_controls.addWidget(QLabel("  |  "))

        # Use interval toggle
        self.manual_mode_checkbox = QCheckBox("Use Interval")
        self.manual_mode_checkbox.setToolTip("Use frame interval for scanning instead of continuous playback speed")
        self.manual_mode_checkbox.setChecked(True)  # Enable interval mode by default
        self.manual_mode_checkbox.stateChanged.connect(self.on_manual_mode_changed)
        top_controls.addWidget(self.manual_mode_checkbox)

        top_controls.addStretch()
        controls_layout.addLayout(top_controls)

        # Navigation controls row
        nav_controls = QHBoxLayout()

        # Frame navigation buttons
        self.prev_frame_btn = QPushButton("⏮️ Previous")
        self.prev_frame_btn.setToolTip("Previous frame (Left Arrow)")
        self.prev_frame_btn.clicked.connect(self.prev_frame)
        nav_controls.addWidget(self.prev_frame_btn)

        self.play_pause_btn = QPushButton("▶️ Play")
        self.play_pause_btn.setToolTip("Play/Pause (Space)")
        self.play_pause_btn.clicked.connect(self.toggle_playback)
        nav_controls.addWidget(self.play_pause_btn)

        self.next_frame_btn = QPushButton("⏭️ Next")
        self.next_frame_btn.setToolTip("Next frame (Right Arrow)")
        self.next_frame_btn.clicked.connect(self.next_frame)
        nav_controls.addWidget(self.next_frame_btn)

        nav_controls.addWidget(QLabel("  |  "))

        # Scan current frame button
        self.scan_frame_btn = QPushButton("🔍 Scan Current Frame")
        self.scan_frame_btn.setToolTip("Perform OCR on current frame")
        self.scan_frame_btn.clicked.connect(self.scan_current_frame)
        nav_controls.addWidget(self.scan_frame_btn)

        nav_controls.addStretch()
        controls_layout.addLayout(nav_controls)

        layout.addWidget(controls_group)

        # Current frame info
        info_group = QGroupBox("📊 Frame Information")
        info_layout = QHBoxLayout(info_group)

        self.current_time_display = QLabel("Time: 00:00:00")
        info_layout.addWidget(self.current_time_display)

        self.current_frame_display = QLabel("Frame: 0")
        info_layout.addWidget(self.current_frame_display)

        self.total_duration_display = QLabel("Duration: 00:00:00")
        info_layout.addWidget(self.total_duration_display)

        info_layout.addStretch()

        layout.addWidget(info_group)

        # OCR result display
        ocr_group = QGroupBox("🔍 OCR Results")
        ocr_layout = QVBoxLayout(ocr_group)

        self.current_ocr_result = QLabel("OCR: No scan performed")
        self.current_ocr_result.setStyleSheet("background-color: #f0f0f0; padding: 10px; border: 1px solid #ccc; font-family: monospace;")
        self.current_ocr_result.setWordWrap(True)
        ocr_layout.addWidget(self.current_ocr_result)

        layout.addWidget(ocr_group)

        # Close button
        close_btn = QPushButton("❌ Close Preview")
        close_btn.clicked.connect(self.close)
        layout.addWidget(close_btn)

    def load_video(self, video_path, ocr_region, start_frame=0):
        """Load video for preview"""
        try:
            import cv2

            if self.video_cap:
                self.video_cap.release()

            self.video_cap = cv2.VideoCapture(video_path)
            self.current_ocr_region = ocr_region

            if not self.video_cap.isOpened():
                self.video_preview.setText("❌ Could not open video")
                return False

            # Get video properties
            self.total_frames = int(self.video_cap.get(cv2.CAP_PROP_FRAME_COUNT))
            self.fps = self.video_cap.get(cv2.CAP_PROP_FPS)
            duration_seconds = self.total_frames / self.fps

            # Update duration display
            hours = int(duration_seconds // 3600)
            minutes = int((duration_seconds % 3600) // 60)
            seconds = int(duration_seconds % 60)
            self.total_duration_display.setText(f"Duration: {hours:02d}:{minutes:02d}:{seconds:02d}")

            # Set start frame
            self.current_frame_number = start_frame
            self.update_preview_frame()

            return True

        except Exception as e:
            self.video_preview.setText(f"❌ Error loading video: {e}")
            return False

    def update_preview_frame(self):
        """Update the preview frame"""
        if not self.video_cap:
            return

        try:
            import cv2

            # Set frame position
            self.video_cap.set(cv2.CAP_PROP_POS_FRAMES, self.current_frame_number)
            ret, frame = self.video_cap.read()

            if not ret:
                return

            # Resize frame to fit preview
            frame_resized = cv2.resize(frame, (720, 405))

            # Draw OCR region overlay
            h, w = frame_resized.shape[:2]
            x_percent, y_percent, w_percent, h_percent = self.current_ocr_region
            x1 = int((x_percent / 100) * w)
            y1 = int((y_percent / 100) * h)
            x2 = int(((x_percent + w_percent) / 100) * w)
            y2 = int(((y_percent + h_percent) / 100) * h)

            # Draw red rectangle for OCR region
            cv2.rectangle(frame_resized, (x1, y1), (x2, y2), (0, 0, 255), 3)
            cv2.putText(frame_resized, "OCR SCAN REGION", (x1, y1-10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)

            # Convert to Qt format and display
            rgb_image = cv2.cvtColor(frame_resized, cv2.COLOR_BGR2RGB)
            h, w, ch = rgb_image.shape
            bytes_per_line = ch * w
            qt_image = QImage(rgb_image.data, w, h, bytes_per_line, QImage.Format.Format_RGB888)
            pixmap = QPixmap.fromImage(qt_image)

            self.video_preview.setPixmap(pixmap)

            # Update frame info
            current_time_seconds = self.current_frame_number / self.fps
            hours = int(current_time_seconds // 3600)
            minutes = int((current_time_seconds % 3600) // 60)
            seconds = int(current_time_seconds % 60)

            self.current_time_display.setText(f"Time: {hours:02d}:{minutes:02d}:{seconds:02d}")
            self.current_frame_display.setText(f"Frame: {self.current_frame_number}")

        except Exception as e:
            print(f"Preview update error: {e}")

    def prev_frame(self):
        """Go to previous frame"""
        if self.current_frame_number > 0:
            self.current_frame_number -= 1
            self.update_preview_frame()
            # Always scan when manually navigating frames
            self.scan_current_frame()

    def next_frame(self):
        """Go to next frame"""
        if self.current_frame_number < self.total_frames - 1:
            self.current_frame_number += 1
            self.update_preview_frame()
            # Always scan when manually navigating frames
            self.scan_current_frame()

    def advance_frame_during_playback(self):
        """Advance frame during playback and update display"""
        print(f"🔄 advance_frame_during_playback called - is_playing: {self.is_playing}, manual_mode: {self.manual_mode_checkbox.isChecked()}")

        if not self.is_playing:
            print("⚠️ advance_frame_during_playback called but not playing")
            return

        if self.current_frame_number >= self.total_frames - 1:
            # Stop playback when reaching end of video
            print("🏁 Reached end of video, stopping playback")
            self.toggle_playback()
            return

        if self.manual_mode_checkbox.isChecked():
            # Use interval mode - skip frames based on interval setting
            interval_seconds = self.interval_spin.value()
            frames_to_skip = int(interval_seconds * self.fps)
            old_frame = self.current_frame_number
            self.current_frame_number = min(self.current_frame_number + frames_to_skip, self.total_frames - 1)

            print(f"🎬 Interval mode: jumped from frame {old_frame} to {self.current_frame_number} (skipped {frames_to_skip} frames, interval: {interval_seconds}s)")

            # Auto-scan when using interval mode
            print("🔍 Updating preview frame and scanning...")
            self.update_preview_frame()
            self.scan_current_frame()
            print("✅ Interval scan completed")
        else:
            # Use speed mode - advance frame by frame at chosen speed
            old_frame = self.current_frame_number
            self.current_frame_number += 1

            print(f"🎬 Speed mode: advanced from frame {old_frame} to {self.current_frame_number}")

            self.update_preview_frame()

    def toggle_playback(self):
        """Toggle play/pause"""
        print(f"🎬 toggle_playback called - current state: is_playing={self.is_playing}")

        if self.is_playing:
            print("⏸️ Stopping playback...")
            self.preview_timer.stop()
            self.play_pause_btn.setText("▶️ Play")
            self.is_playing = False
            print("✅ Playback stopped")
        else:
            print("▶️ Starting playback...")
            if self.manual_mode_checkbox.isChecked():
                # Use interval mode - timer fires every interval to jump frames and scan
                interval_seconds = self.interval_spin.value()
                timer_interval = int(interval_seconds * 1000)
                print(f"🎬 Starting interval playback: {interval_seconds}s intervals (timer: {timer_interval}ms)")
            else:
                # Use speed mode - timer based on playback speed for smooth playback
                speed_text = self.speed_combo.currentText()
                speed_multiplier = float(speed_text.replace('x', ''))
                timer_interval = int(1000 / (self.fps * speed_multiplier))
                print(f"🎬 Starting speed playback: {speed_multiplier}x speed (timer: {timer_interval}ms)")

            print(f"🔧 Starting timer with {timer_interval}ms interval...")
            self.preview_timer.start(timer_interval)
            self.play_pause_btn.setText("⏸️ Pause")
            self.is_playing = True

            # Verify timer is running
            if self.preview_timer.isActive():
                print(f"✅ Timer started successfully with {timer_interval}ms interval")
            else:
                print(f"❌ Timer failed to start!")
                self.is_playing = False
                self.play_pause_btn.setText("▶️ Play")

    def scan_current_frame(self):
        """Scan current frame with OCR"""
        print(f"🔍 scan_current_frame called - frame: {self.current_frame_number}")

        if not self.video_cap:
            print("❌ No video loaded for scanning")
            self.current_ocr_result.setText("OCR: No video loaded")
            return

        try:
            import cv2
            from ocr_utils import ocr_manager

            print(f"🎬 Getting frame {self.current_frame_number} for OCR scanning...")

            # Get current frame
            self.video_cap.set(cv2.CAP_PROP_POS_FRAMES, self.current_frame_number)
            ret, frame = self.video_cap.read()

            if not ret:
                print(f"❌ Could not read frame {self.current_frame_number}")
                self.current_ocr_result.setText("OCR: Could not read frame")
                return

            print(f"✅ Frame {self.current_frame_number} read successfully, applying OCR region...")

            # Apply OCR region cropping
            h, w = frame.shape[:2]
            x_percent, y_percent, w_percent, h_percent = self.current_ocr_region
            x1 = int((x_percent / 100) * w)
            y1 = int((y_percent / 100) * h)
            x2 = int(((x_percent + w_percent) / 100) * w)
            y2 = int(((y_percent + h_percent) / 100) * h)

            cropped_frame = frame[y1:y2, x1:x2]

            # Perform OCR with raw image processing for best accuracy
            ocr_results = ocr_manager.extract_text_from_image(cropped_frame, "custom", use_raw_image=True)

            # Extract text
            detected_text = ""
            for result in ocr_results:
                if 'text' in result:
                    detected_text += result['text'] + " "
            detected_text = detected_text.strip()

            if detected_text:
                self.current_ocr_result.setText(f"OCR: {detected_text}")
                print(f"Manual scan: '{detected_text}'")
            else:
                self.current_ocr_result.setText("OCR: No text detected")
                print("No text detected in current frame")

        except Exception as e:
            self.current_ocr_result.setText(f"OCR: Error - {e}")
            print(f"OCR scan error: {e}")

        # Update frame information after scan
        self.update_frame_info()

    def update_frame_info(self):
        """Update frame information display"""
        if self.video_cap:
            # Update frame info
            current_time_seconds = self.current_frame_number / self.fps
            hours = int(current_time_seconds // 3600)
            minutes = int((current_time_seconds % 3600) // 60)
            seconds = int(current_time_seconds % 60)

            self.current_time_display.setText(f"Time: {hours:02d}:{minutes:02d}:{seconds:02d}")
            self.current_frame_display.setText(f"Frame: {self.current_frame_number}")

    def update_from_automated_scan(self, frame_number, timestamp, ocr_result):
        """Update preview from automated scanning results"""
        # Always update from automated scan results regardless of interval mode
        # The "Use Interval" setting only affects manual playback in the preview window
        self.current_frame_number = frame_number
        self.update_preview_frame()

        # Update OCR result
        self.current_ocr_result.setText(f"OCR: {ocr_result}")

        # Update timestamp display
        self.current_time_display.setText(f"Time: {timestamp}")

    def on_manual_mode_changed(self, state):
        """Handle interval mode toggle"""
        if state == 2:  # Checked
            print("✅ Use Interval mode enabled - Playback will use frame intervals for scanning")
            print(f"🔧 Current interval setting: {self.interval_spin.value()}s")
        else:
            print("❌ Use Interval mode disabled - Playback will use continuous speed")
            print(f"🔧 Current speed setting: {self.speed_combo.currentText()}")

    def keyPressEvent(self, event):
        """Handle keyboard shortcuts"""
        if event.key() == Qt.Key.Key_Left:
            self.prev_frame()
        elif event.key() == Qt.Key.Key_Right:
            self.next_frame()
        elif event.key() == Qt.Key.Key_Space:
            self.toggle_playback()
            event.accept()
        else:
            super().keyPressEvent(event)

    def closeEvent(self, event):
        """Handle window close"""
        if self.preview_timer.isActive():
            self.preview_timer.stop()
        if self.video_cap:
            self.video_cap.release()
        event.accept()


class LivestreamTestingTab(QWidget):
    """Livestream testing mode tab for testing previous streams"""

    def __init__(self, main_window=None):
        super().__init__()
        self.main_window = main_window
        self.testing_worker = None
        self.downloader = YouTubeStreamDownloader() if TESTING_MODE_AVAILABLE else None
        self.current_ocr_region = self.load_ocr_region_settings()
        self.preview_window = None

        self.init_ui()

        if self.downloader:
            self.refresh_stream_list()

    def load_ocr_region_settings(self):
        """Load OCR region settings from QSettings"""
        try:
            settings = QSettings()
            x = settings.value("ocr_region_x", 0.0, type=float)
            y = settings.value("ocr_region_y", 67.0, type=float)
            w = settings.value("ocr_region_width", 100.0, type=float)
            h = settings.value("ocr_region_height", 33.0, type=float)

            # Validate region values
            x = max(0.0, min(100.0, x))
            y = max(0.0, min(100.0, y))
            w = max(5.0, min(100.0 - x, w))
            h = max(5.0, min(100.0 - y, h))

            return (x, y, w, h)
        except Exception as e:
            print(f"Failed to load OCR region settings: {e}")
            return (0.0, 67.0, 100.0, 33.0)  # Default region

    def save_ocr_region_settings(self, region):
        """Save OCR region settings to QSettings"""
        try:
            settings = QSettings()
            x, y, w, h = region
            settings.setValue("ocr_region_x", x)
            settings.setValue("ocr_region_y", y)
            settings.setValue("ocr_region_width", w)
            settings.setValue("ocr_region_height", h)
            settings.sync()
            self.log_detection(f"💾 OCR region saved: {x:.1f}%, {y:.1f}%, {w:.1f}%, {h:.1f}%")
        except Exception as e:
            print(f"Failed to save OCR region settings: {e}")

    def init_ui(self):
        """Initialize compact and responsive testing tab UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)  # Reduced margins
        layout.setSpacing(8)  # Reduced spacing

        # Header
        header_layout = QHBoxLayout()
        title_label = QLabel("🧪 Previous Stream Testing")
        title_label.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        header_layout.addWidget(title_label)

        header_layout.addStretch()

        self.status_label = QLabel("⚫ Ready")
        self.status_label.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        header_layout.addWidget(self.status_label)

        layout.addLayout(header_layout)

        # Main content with responsive splitter
        content_splitter = QSplitter(Qt.Orientation.Horizontal)
        content_splitter.setChildrenCollapsible(False)  # Prevent panels from collapsing
        content_splitter.setStyleSheet("""
            QSplitter::handle {
                background-color: #ccc;
                width: 3px;
            }
            QSplitter::handle:hover {
                background-color: #999;
            }
        """)

        # Left panel - Configuration
        left_panel = self.create_configuration_panel()
        left_panel.setMinimumWidth(300)  # Minimum width for usability
        left_panel.setSizePolicy(QSizePolicy.Policy.Preferred, QSizePolicy.Policy.Expanding)
        content_splitter.addWidget(left_panel)

        # Right panel - Results
        right_panel = self.create_results_panel()
        right_panel.setMinimumWidth(400)  # Minimum width for results
        right_panel.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        content_splitter.addWidget(right_panel)

        # Set responsive sizes (30% left, 70% right)
        content_splitter.setSizes([300, 700])
        content_splitter.setStretchFactor(0, 0)  # Left panel doesn't stretch
        content_splitter.setStretchFactor(1, 1)  # Right panel stretches
        layout.addWidget(content_splitter)

        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)

    def create_configuration_panel(self):
        """Create configuration panel"""
        panel = QWidget()
        layout = QVBoxLayout(panel)

        # Stream Selection
        stream_group = QGroupBox("📺 Stream Selection")
        stream_layout = QVBoxLayout(stream_group)

        # URL input
        url_layout = QHBoxLayout()
        url_layout.addWidget(QLabel("YouTube URL:"))
        self.url_input = QLineEdit()
        self.url_input.setPlaceholderText("https://www.youtube.com/watch?v=...")
        url_layout.addWidget(self.url_input)

        download_btn = QPushButton("📥 Download")
        download_btn.clicked.connect(self.download_stream)
        url_layout.addWidget(download_btn)

        stream_layout.addLayout(url_layout)

        # Available streams
        stream_layout.addWidget(QLabel("Available Streams:"))
        self.stream_list = QListWidget()
        self.stream_list.setMaximumHeight(120)
        self.stream_list.itemSelectionChanged.connect(self.on_stream_selected)
        stream_layout.addWidget(self.stream_list)

        refresh_btn = QPushButton("🔄 Refresh List")
        refresh_btn.clicked.connect(self.refresh_stream_list)
        stream_layout.addWidget(refresh_btn)

        layout.addWidget(stream_group)

        # Testing Configuration
        config_group = QGroupBox("⚙️ Testing Configuration")
        config_layout = QGridLayout(config_group)

        # Frame interval
        config_layout.addWidget(QLabel("Frame Interval:"), 0, 0)
        self.interval_spin = QDoubleSpinBox()
        self.interval_spin.setRange(1.0, 60.0)
        self.interval_spin.setValue(5.0)
        self.interval_spin.setSuffix(" seconds")
        config_layout.addWidget(self.interval_spin, 0, 1)

        # OCR Engine
        config_layout.addWidget(QLabel("OCR Engine:"), 1, 0)
        self.ocr_combo = QComboBox()
        if EASYOCR_AVAILABLE:
            self.ocr_combo.addItem("EasyOCR", "easyocr")
        if PADDLEOCR_AVAILABLE:
            self.ocr_combo.addItem("PaddleOCR", "paddleocr")
        if self.ocr_combo.count() == 0:
            self.ocr_combo.addItem("No OCR Available", "none")
        config_layout.addWidget(self.ocr_combo, 1, 1)

        # Start time selection
        config_layout.addWidget(QLabel("Start Time:"), 2, 0)
        start_time_layout = QHBoxLayout()

        # Time input fields
        self.start_hour_spin = QSpinBox()
        self.start_hour_spin.setRange(0, 23)
        self.start_hour_spin.setValue(0)
        self.start_hour_spin.setPrefix("H:")
        self.start_hour_spin.setMinimumWidth(60)
        start_time_layout.addWidget(self.start_hour_spin)

        self.start_minute_spin = QSpinBox()
        self.start_minute_spin.setRange(0, 59)
        self.start_minute_spin.setValue(0)
        self.start_minute_spin.setPrefix("M:")
        self.start_minute_spin.setMinimumWidth(60)
        start_time_layout.addWidget(self.start_minute_spin)

        self.start_second_spin = QSpinBox()
        self.start_second_spin.setRange(0, 59)
        self.start_second_spin.setValue(0)
        self.start_second_spin.setPrefix("S:")
        self.start_second_spin.setMinimumWidth(60)
        start_time_layout.addWidget(self.start_second_spin)

        # Reset button
        reset_time_btn = QPushButton("⏮️")
        reset_time_btn.setToolTip("Reset to start (00:00:00)")
        reset_time_btn.setMaximumWidth(30)
        reset_time_btn.clicked.connect(self.reset_start_time)
        start_time_layout.addWidget(reset_time_btn)

        # Get video info button
        info_btn = QPushButton("📊")
        info_btn.setToolTip("Get video duration info")
        info_btn.setMaximumWidth(30)
        info_btn.clicked.connect(self.show_video_info)
        start_time_layout.addWidget(info_btn)

        start_time_layout.addStretch()

        start_time_widget = QWidget()
        start_time_widget.setLayout(start_time_layout)
        config_layout.addWidget(start_time_widget, 2, 1)

        # Code validation
        self.validate_checkbox = QCheckBox("Browser validation (preload checkout, test codes, no purchase)")
        self.validate_checkbox.setChecked(True)
        self.validate_checkbox.setToolTip("Preload browsers and test codes exactly like live scanning, but stop before checkout")
        config_layout.addWidget(self.validate_checkbox, 3, 0, 1, 2)

        layout.addWidget(config_group)

        # Account Type Selection
        account_group = QGroupBox("🎯 Account Types to Scan")
        account_layout = QVBoxLayout(account_group)

        # Account type checkboxes
        checkbox_layout = QGridLayout()
        checkbox_layout.setSpacing(10)

        # Create checkboxes for each account type
        self.account_checkboxes = {}
        account_types = [
            ("Free Reset Code", "🔄 Free Reset Code"),
            ("Starter", "🚀 Starter"),
            ("Starter Plus", "⭐ Starter Plus"),
            ("Expert", "👑 Expert")
        ]

        for i, (account_type, display_name) in enumerate(account_types):
            checkbox = QCheckBox(display_name)
            checkbox.setChecked(True)  # All enabled by default
            checkbox.setStyleSheet("""
                QCheckBox {
                    font-size: 12px;
                    font-weight: bold;
                    spacing: 8px;
                }
                QCheckBox::indicator {
                    width: 18px;
                    height: 18px;
                }
                QCheckBox::indicator:unchecked {
                    border: 2px solid #ccc;
                    border-radius: 3px;
                    background-color: white;
                }
                QCheckBox::indicator:checked {
                    border: 2px solid #4CAF50;
                    border-radius: 3px;
                    background-color: #4CAF50;
                }
            """)
            self.account_checkboxes[account_type] = checkbox
            checkbox_layout.addWidget(checkbox, i // 2, i % 2)

        account_layout.addLayout(checkbox_layout)

        # Account selection info
        account_info = QLabel("💡 Select which account types to scan for. Only selected types will be detected and processed.")
        account_info.setStyleSheet("color: #666; font-size: 10px; font-style: italic;")
        account_info.setWordWrap(True)
        account_layout.addWidget(account_info)

        layout.addWidget(account_group)

        # OCR Region
        region_group = QGroupBox("🎯 OCR Region")
        region_layout = QVBoxLayout(region_group)

        self.region_display_label = QLabel("Current Region: 0%, 67%, 100%, 33%")
        self.region_display_label.setStyleSheet("font-family: monospace; background-color: #f0f0f0; padding: 5px; border-radius: 3px;")
        region_layout.addWidget(self.region_display_label)

        if OCR_REGION_SELECTOR_AVAILABLE:
            select_region_btn = QPushButton("🎯 Manual OCR Region Selection")
            select_region_btn.clicked.connect(self.open_ocr_region_selector)
            select_region_btn.setStyleSheet("""
                QPushButton {
                    font-weight: bold;
                    background-color: #2196F3;
                    color: white;
                    border: none;
                    border-radius: 5px;
                    padding: 8px;
                }
                QPushButton:hover {
                    background-color: #1976D2;
                }
            """)
            region_layout.addWidget(select_region_btn)

        layout.addWidget(region_group)

        # Control buttons
        control_layout = QHBoxLayout()

        self.start_btn = QPushButton("🚀 Start Testing")
        self.start_btn.clicked.connect(self.start_testing)
        self.start_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 5px;
                font-weight: bold;
                padding: 10px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        control_layout.addWidget(self.start_btn)

        self.preload_btn = QPushButton("🚀 Preload Pages")
        self.preload_btn.clicked.connect(self.preload_pages)
        self.preload_btn.setToolTip("Automate standard accounts and open tabs for reset accounts")
        self.preload_btn.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                border-radius: 5px;
                font-weight: bold;
                padding: 10px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """)
        control_layout.addWidget(self.preload_btn)

        self.stop_btn = QPushButton("⏹️ Stop Testing")
        self.stop_btn.clicked.connect(self.stop_testing)
        self.stop_btn.setEnabled(False)
        self.stop_btn.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                border-radius: 5px;
                font-weight: bold;
                padding: 10px;
            }
            QPushButton:hover {
                background-color: #da190b;
            }
        """)
        control_layout.addWidget(self.stop_btn)

        layout.addLayout(control_layout)

        layout.addStretch()

        return panel

    def create_results_panel(self):
        """Create results panel"""
        panel = QWidget()
        layout = QVBoxLayout(panel)

        # Current status
        status_group = QGroupBox("📊 Current Status")
        status_layout = QGridLayout(status_group)

        status_layout.addWidget(QLabel("Current Time:"), 0, 0)
        self.current_time_label = QLabel("00:00:00")
        status_layout.addWidget(self.current_time_label, 0, 1)

        status_layout.addWidget(QLabel("Frames Processed:"), 1, 0)
        self.frames_processed_label = QLabel("0")
        status_layout.addWidget(self.frames_processed_label, 1, 1)

        status_layout.addWidget(QLabel("Codes Detected:"), 2, 0)
        self.codes_detected_label = QLabel("0")
        status_layout.addWidget(self.codes_detected_label, 2, 1)

        status_layout.addWidget(QLabel("Codes Validated:"), 3, 0)
        self.codes_validated_label = QLabel("0")
        status_layout.addWidget(self.codes_validated_label, 3, 1)

        layout.addWidget(status_group)

        # Detection log
        log_group = QGroupBox("🔍 Detection Log")
        log_layout = QVBoxLayout(log_group)

        self.detection_log = QTextEdit()
        self.detection_log.setMaximumHeight(150)
        self.detection_log.setReadOnly(True)
        log_layout.addWidget(self.detection_log)

        layout.addWidget(log_group)

        # Validation results
        validation_group = QGroupBox("✅ Validation Results")
        validation_layout = QVBoxLayout(validation_group)

        self.validation_tree = QTreeWidget()
        self.validation_tree.setHeaderLabels(["Code", "Type", "Status", "Message", "Time"])
        self.validation_tree.setMaximumHeight(200)
        validation_layout.addWidget(self.validation_tree)

        layout.addWidget(validation_group)

        return panel

    def refresh_stream_list(self):
        """Refresh the list of available streams"""
        if not self.downloader:
            return

        self.stream_list.clear()

        try:
            streams = self.downloader.list_downloaded_streams()
            for stream in streams:
                item_text = f"{stream['filename']} ({stream['size_mb']:.1f} MB)"
                item = QListWidgetItem(item_text)
                item.setData(Qt.ItemDataRole.UserRole, stream['path'])
                self.stream_list.addItem(item)
        except Exception as e:
            self.log_detection(f"❌ Error refreshing stream list: {e}")

    def download_stream(self):
        """Download a stream from YouTube"""
        if not self.downloader:
            QMessageBox.warning(self, "Not Available", "Stream downloader is not available")
            return

        url = self.url_input.text().strip()
        if not url:
            QMessageBox.warning(self, "Warning", "Please enter a YouTube URL")
            return

        # Show progress dialog
        progress_dialog = QProgressDialog("Downloading stream...", "Cancel", 0, 0, self)
        progress_dialog.setWindowModality(Qt.WindowModality.WindowModal)
        progress_dialog.show()

        def download_thread():
            try:
                file_path = self.downloader.download_stream(url)
                QTimer.singleShot(0, lambda: self.on_download_complete(file_path, progress_dialog))
            except Exception as e:
                QTimer.singleShot(0, lambda: self.on_download_error(str(e), progress_dialog))

        threading.Thread(target=download_thread, daemon=True).start()

    def on_download_complete(self, file_path: str, dialog: QProgressDialog):
        """Handle download completion"""
        dialog.close()
        if file_path:
            QMessageBox.information(self, "Success", f"Stream downloaded successfully!\n{file_path}")
            self.refresh_stream_list()
        else:
            QMessageBox.critical(self, "Error", "Download failed")

    def on_download_error(self, error: str, dialog: QProgressDialog):
        """Handle download error"""
        dialog.close()
        QMessageBox.critical(self, "Error", f"Download failed: {error}")

    def open_ocr_region_selector(self):
        """Open OCR region selector"""
        if not OCR_REGION_SELECTOR_AVAILABLE:
            QMessageBox.warning(self, "Not Available", "OCR region selector is not available")
            return

        try:
            dialog = OCRRegionSelectorDialog(self, initial_region=self.current_ocr_region)
            if dialog.exec() == QDialog.DialogCode.Accepted:
                self.current_ocr_region = dialog.get_selected_region()
                self.update_region_display()

                # Save the new region settings
                self.save_ocr_region_settings(self.current_ocr_region)

                x, y, w, h = self.current_ocr_region
                QMessageBox.information(self, "OCR Region Updated",
                                      f"OCR region updated!\n\n"
                                      f"Position: {x:.1f}%, {y:.1f}%\n"
                                      f"Size: {w:.1f}% × {h:.1f}%")
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to open OCR region selector: {e}")

    def update_region_display(self):
        """Update region display"""
        x, y, w, h = self.current_ocr_region
        self.region_display_label.setText(f"Current Region: {x:.1f}%, {y:.1f}%, {w:.1f}%, {h:.1f}%")

    def reset_start_time(self):
        """Reset start time to 00:00:00"""
        self.start_hour_spin.setValue(0)
        self.start_minute_spin.setValue(0)
        self.start_second_spin.setValue(0)
        self.log_detection("⏮️ Start time reset to 00:00:00")

    def show_video_info(self):
        """Show video duration and information"""
        current_item = self.stream_list.currentItem()
        if not current_item:
            QMessageBox.information(self, "Info", "Please select a stream first")
            return

        stream_path = current_item.data(Qt.ItemDataRole.UserRole)
        if not stream_path or not os.path.exists(stream_path):
            QMessageBox.warning(self, "Warning", "Selected stream file not found")
            return

        try:
            import cv2
            cap = cv2.VideoCapture(stream_path)

            if not cap.isOpened():
                QMessageBox.warning(self, "Error", "Could not open video file")
                return

            # Get video properties
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            fps = cap.get(cv2.CAP_PROP_FPS)
            duration_seconds = total_frames / fps if fps > 0 else 0

            # Convert to HH:MM:SS
            hours = int(duration_seconds // 3600)
            minutes = int((duration_seconds % 3600) // 60)
            seconds = int(duration_seconds % 60)

            # Get file size
            file_size = os.path.getsize(stream_path) / (1024 * 1024)  # MB

            cap.release()

            info_text = f"""📊 Video Information:

📁 File: {os.path.basename(stream_path)}
⏱️ Duration: {hours:02d}:{minutes:02d}:{seconds:02d}
🎬 Total Frames: {total_frames:,}
📺 Frame Rate: {fps:.1f} FPS
💾 File Size: {file_size:.1f} MB

💡 Tip: You can start scanning from any time point!
Set the start time above to skip to a specific moment."""

            QMessageBox.information(self, "Video Information", info_text)

        except Exception as e:
            QMessageBox.warning(self, "Error", f"Could not get video info: {e}")

    def get_start_time_seconds(self):
        """Get start time in seconds from HH:MM:SS inputs"""
        hours = self.start_hour_spin.value()
        minutes = self.start_minute_spin.value()
        seconds = self.start_second_spin.value()
        return hours * 3600 + minutes * 60 + seconds

    def open_preview_window(self, stream_path, start_time_seconds):
        """Open the stream preview popup window"""
        try:
            # Close existing preview window if open
            if self.preview_window:
                self.preview_window.close()

            # Create new preview window
            self.preview_window = StreamPreviewWindow(self)

            # Calculate start frame
            import cv2
            temp_cap = cv2.VideoCapture(stream_path)
            if temp_cap.isOpened():
                fps = temp_cap.get(cv2.CAP_PROP_FPS)
                start_frame = int(start_time_seconds * fps)
                temp_cap.release()
            else:
                start_frame = 0

            # Load video in preview window
            success = self.preview_window.load_video(stream_path, self.current_ocr_region, start_frame)

            if success:
                # Show the preview window
                self.preview_window.show()
                self.preview_window.raise_()
                self.preview_window.activateWindow()

                self.log_detection("🎬 Stream preview window opened")
            else:
                self.log_detection("❌ Failed to open stream preview")

        except Exception as e:
            self.log_detection(f"❌ Preview window error: {e}")

    def on_stream_selected(self):
        """Handle stream selection (placeholder for future functionality)"""
        current_item = self.stream_list.currentItem()
        if current_item:
            stream_name = current_item.text()
            self.log_detection(f"📹 Selected stream: {stream_name}")
            # Preview will be shown when testing starts

    def get_selected_account_types(self):
        """Get list of selected account types"""
        selected_types = []
        for account_type, checkbox in self.account_checkboxes.items():
            if checkbox.isChecked():
                selected_types.append(account_type)
        return selected_types

    def preload_pages(self):
        """Preload account type pages to checkout stage for selected account types"""
        try:
            # Get selected account types from checkboxes
            selected_types = self.get_selected_account_types()
            if not selected_types:
                self.log_detection("❌ No account types selected. Please select at least one account type to preload.")
                return

            # Get credentials and CVV from Settings tab
            username = ""
            password = ""
            cvv = ""
            if self.main_window and hasattr(self.main_window, 'settings_tab'):
                settings = self.main_window.settings_tab.get_settings()
                browser_settings = settings.get('browser', {})
                username = browser_settings.get('username', '').strip()
                password = browser_settings.get('password', '').strip()
                cvv = browser_settings.get('cvv', '').strip()

            if not username or not password:
                self.log_detection("❌ Please enter username and password in Settings tab before preloading.")
                self.log_detection("💡 Go to Settings tab → Browser Automation Settings → MFFU Credentials")
                return

            # Show CVV status
            if cvv:
                self.log_detection(f"💳 CVV provided - preloading pages for: {', '.join(selected_types)}")
            else:
                self.log_detection(f"⚠️ CVV not provided - preloading pages for: {', '.join(selected_types)}")

            # Disable preload button to prevent multiple simultaneous preloads
            self.preload_btn.setText("⏳ Preloading...")
            self.preload_btn.setEnabled(False)

            # Start preloading in a separate thread to keep GUI responsive
            self.preload_thread = PreloadThread(selected_types, username, password, cvv)
            self.preload_thread.status_update.connect(self.log_detection)
            self.preload_thread.finished.connect(self.on_preload_finished)

            # Use QTimer to start thread after GUI update
            QTimer.singleShot(100, self.preload_thread.start)

        except ImportError:
            self.log_detection("❌ Browser automation not available. Please install selenium: pip install selenium")
            self.preload_btn.setText("🚀 Preload Pages")
            self.preload_btn.setEnabled(True)
        except Exception as e:
            self.log_detection(f"❌ Preload error: {str(e)}")
            self.preload_btn.setText("🚀 Preload Pages")
            self.preload_btn.setEnabled(True)

    def on_preload_finished(self):
        """Called when preloading thread finishes"""
        self.preload_btn.setText("🚀 Preload Pages")
        self.preload_btn.setEnabled(True)

    def start_testing(self):
        """Start testing process"""
        # Get selected stream
        current_item = self.stream_list.currentItem()
        if not current_item:
            QMessageBox.warning(self, "Warning", "Please select a stream to test")
            return

        # Get stream path
        stream_path = current_item.data(Qt.ItemDataRole.UserRole)
        if not stream_path or not os.path.exists(stream_path):
            QMessageBox.warning(self, "Warning", "Selected stream file not found")
            return

        # Get configuration
        frame_interval = self.interval_spin.value()
        ocr_engine = self.ocr_combo.currentData() or "easyocr"
        validate_codes = self.validate_checkbox.isChecked()
        start_time_seconds = self.get_start_time_seconds()

        # Check if OCR is available
        if not OCR_AVAILABLE:
            QMessageBox.warning(self, "Warning", "OCR engines are not available")
            return

        # Convert start time to HH:MM:SS for display
        start_hours = int(start_time_seconds // 3600)
        start_minutes = int((start_time_seconds % 3600) // 60)
        start_secs = int(start_time_seconds % 60)
        start_time_str = f"{start_hours:02d}:{start_minutes:02d}:{start_secs:02d}"

        self.log_detection("🚀 Starting stream testing...")
        self.log_detection(f"📁 Stream: {os.path.basename(stream_path)}")
        self.log_detection(f"⏰ Start Time: {start_time_str}")
        self.log_detection(f"⏱️ Interval: {frame_interval}s")
        self.log_detection(f"🔍 OCR Engine: {ocr_engine}")
        if validate_codes:
            self.log_detection(f"🌐 Browser Validation: ENABLED (preload checkout, test codes, no purchase)")
        else:
            self.log_detection(f"📝 Validation: DISABLED (detection only)")

        # Open stream preview window
        self.open_preview_window(stream_path, start_time_seconds)

        # Reset UI
        self.validation_tree.clear()
        self.frames_processed_label.setText("0")
        self.codes_detected_label.setText("0")
        self.codes_validated_label.setText("0")
        self.current_time_label.setText("00:00:00")

        # Get credentials from main window if available
        username = ""
        password = ""
        if self.main_window and hasattr(self.main_window, 'livestream_tab'):
            if hasattr(self.main_window.livestream_tab, 'username_input'):
                username = self.main_window.livestream_tab.username_input.text().strip()
            if hasattr(self.main_window.livestream_tab, 'password_input'):
                password = self.main_window.livestream_tab.password_input.text().strip()

        # Create and start worker thread
        self.testing_worker = StreamTestingWorker(
            stream_path=stream_path,
            frame_interval=frame_interval,
            ocr_engine=ocr_engine,
            ocr_region=self.current_ocr_region,
            validate_codes=validate_codes,
            start_time_seconds=start_time_seconds,
            preview_window=self.preview_window,
            username=username,
            password=password
        )

        # Connect worker signals
        self.testing_worker.progress_update.connect(self.progress_bar.setValue)
        self.testing_worker.detection_log.connect(self.log_detection)
        self.testing_worker.validation_result.connect(self.add_validation_result)
        self.testing_worker.status_update.connect(self.update_status)
        self.testing_worker.finished.connect(self.on_testing_finished)

        # Connect frame updates to preview window if available
        if self.preview_window:
            self.testing_worker.frame_update.connect(self.preview_window.update_from_automated_scan)

        # Start worker
        self.testing_worker.start()

        # Update UI
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.status_label.setText("🟢 Testing")

    def stop_testing(self):
        """Stop testing process"""
        if self.testing_worker and self.testing_worker.isRunning():
            self.log_detection("⏹️ Stopping stream testing...")
            self.testing_worker.stop()
            self.testing_worker.wait(3000)  # Wait up to 3 seconds

        self.on_testing_finished()

    def add_validation_result(self, code: str, account_type: str, status: str, message: str, time_str: str):
        """Add validation result to the tree"""
        item = QTreeWidgetItem([code, account_type, status, message, time_str])

        # Color code the status
        if "Valid" in status:
            item.setBackground(2, QColor(200, 255, 200))  # Light green
        elif "Invalid" in status:
            item.setBackground(2, QColor(255, 200, 200))  # Light red
        elif "Error" in status:
            item.setBackground(2, QColor(255, 255, 200))  # Light yellow
        else:
            item.setBackground(2, QColor(240, 240, 240))  # Light gray

        self.validation_tree.addTopLevelItem(item)
        self.validation_tree.scrollToBottom()

    def update_status(self, current_time: str, frames_processed: int, codes_detected: int, codes_validated: int):
        """Update status labels"""
        self.current_time_label.setText(current_time)
        self.frames_processed_label.setText(str(frames_processed))
        self.codes_detected_label.setText(str(codes_detected))
        self.codes_validated_label.setText(str(codes_validated))

    def on_testing_finished(self):
        """Handle testing completion"""
        self.log_detection("✅ Testing session completed")

        # Reset UI
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.progress_bar.setVisible(False)
        self.status_label.setText("⚫ Ready")

        # Clean up worker
        if self.testing_worker:
            self.testing_worker.deleteLater()
            self.testing_worker = None

    def log_detection(self, message: str):
        """Log detection message"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.detection_log.append(f"[{timestamp}] {message}")

        # Auto-scroll to bottom
        scrollbar = self.detection_log.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())


class MFFUHijackMainWindow(QMainWindow):
    """Main application window with all functionality"""

    def __init__(self):
        super().__init__()
        self.smart_features = SmartFeaturesManager() if SMART_FEATURES_AVAILABLE else None
        self.init_ui()
        self.setup_menu()

        # Log startup
        self.log_startup_info()

        # Apply settings from settings tab to application
        if hasattr(self, 'settings_tab'):
            settings = self.settings_tab.get_settings()
            self.apply_settings_to_application(settings)

        # Load saved window size and position
        self.load_window_geometry()

    def closeEvent(self, event):
        """Handle application close - save window size and settings"""
        try:
            # Save window geometry (size and position)
            self.save_window_geometry()

            # Save settings if auto-save is enabled
            if hasattr(self, 'settings_tab'):
                settings = self.settings_tab.get_settings()
                if settings.get('general', {}).get('auto_save', True):
                    print("💾 Auto-saving settings on close...")
                    self.settings_tab.save_settings()

                # Always save layout settings if auto-save layout is enabled
                if settings.get('general', {}).get('auto_save_layout', True):
                    print("💾 Auto-saving layout on close...")
                    qsettings = QSettings()
                    self.settings_tab.save_layout_settings(qsettings)

            print("👋 Application closing - settings saved")

        except Exception as e:
            print(f"⚠️ Error saving settings on close: {e}")

        # Accept the close event
        event.accept()

    def save_window_geometry(self):
        """Save current window size and position"""
        try:
            settings = QSettings()
            settings.setValue("window/geometry", self.saveGeometry())
            settings.setValue("window/state", self.saveState())
            settings.setValue("window/size", self.size())
            settings.setValue("window/position", self.pos())
            settings.sync()
            print(f"💾 Window geometry saved: {self.size().width()}x{self.size().height()}")
        except Exception as e:
            print(f"⚠️ Failed to save window geometry: {e}")

    def load_window_geometry(self):
        """Load saved window size and position"""
        try:
            settings = QSettings()

            # Try to restore full geometry first (includes size, position, and state)
            geometry = settings.value("window/geometry")
            if geometry:
                self.restoreGeometry(geometry)
                print(f"✅ Window geometry restored from settings")
                return

            # Fallback: restore size and position separately
            size = settings.value("window/size")
            position = settings.value("window/position")

            if size:
                self.resize(size)
                print(f"✅ Window size restored: {size.width()}x{size.height()}")

            if position:
                self.move(position)
                print(f"✅ Window position restored: {position.x()}, {position.y()}")

        except Exception as e:
            print(f"⚠️ Failed to load window geometry: {e}")
            print("🔧 Using default window size: 1200x800")

    def resizeEvent(self, event):
        """Handle window resize - save new size automatically"""
        super().resizeEvent(event)

        # Save window size in real-time (with a small delay to avoid excessive saves)
        if hasattr(self, '_resize_timer'):
            self._resize_timer.stop()
        else:
            from PyQt6.QtCore import QTimer
            self._resize_timer = QTimer()
            self._resize_timer.setSingleShot(True)
            self._resize_timer.timeout.connect(self.save_window_geometry)

        # Save after 500ms of no resize activity
        self._resize_timer.start(500)

    def moveEvent(self, event):
        """Handle window move - save new position automatically"""
        super().moveEvent(event)

        # Save window position in real-time (with a small delay to avoid excessive saves)
        if hasattr(self, '_move_timer'):
            self._move_timer.stop()
        else:
            from PyQt6.QtCore import QTimer
            self._move_timer = QTimer()
            self._move_timer.setSingleShot(True)
            self._move_timer.timeout.connect(self.save_window_geometry)

        # Save after 500ms of no move activity
        self._move_timer.start(500)

    def init_ui(self):
        """Initialize main UI with compact and responsive design"""
        self.setWindowTitle("MFFUHijack - Real-Time OCR Livestream Code Detection & Testing")

        # Set responsive sizing
        self.setMinimumSize(1000, 600)  # Reduced minimum size for better compatibility
        self.resize(1200, 800)  # More reasonable default size

        # Enable proper resizing behavior
        self.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)

        # Central widget with proper size policy
        central_widget = QWidget()
        central_widget.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        self.setCentralWidget(central_widget)

        # Main layout with compact margins
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(8, 8, 8, 8)  # Reduced margins for compactness
        layout.setSpacing(6)  # Reduced spacing

        # Compact header
        header_layout = QHBoxLayout()
        header_layout.setContentsMargins(0, 0, 0, 0)
        header_layout.setSpacing(10)

        # Compact title
        title_label = QLabel("🎯 MFFUHijack")
        title_label.setFont(QFont("Arial", 14, QFont.Weight.Bold))  # Smaller font
        title_label.setSizePolicy(QSizePolicy.Policy.Preferred, QSizePolicy.Policy.Fixed)
        header_layout.addWidget(title_label)

        header_layout.addStretch()

        # Compact status indicator
        self.status_indicator = QLabel("🟢 Ready")
        self.status_indicator.setFont(QFont("Arial", 10, QFont.Weight.Bold))  # Smaller font
        self.status_indicator.setSizePolicy(QSizePolicy.Policy.Preferred, QSizePolicy.Policy.Fixed)
        header_layout.addWidget(self.status_indicator)

        layout.addLayout(header_layout)

        # Compact tab widget
        self.tab_widget = QTabWidget()
        self.tab_widget.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        self.tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #c0c0c0;
                background-color: white;
                margin: 0px;
            }
            QTabBar::tab {
                background-color: #f0f0f0;
                border: 1px solid #c0c0c0;
                padding: 6px 12px;  /* Reduced padding for compactness */
                margin-right: 1px;
                font-weight: bold;
                font-size: 11px;  /* Smaller font for compactness */
                min-width: 80px;  /* Minimum width for readability */
                max-width: 150px; /* Maximum width to prevent excessive stretching */
            }
            QTabBar::tab:selected {
                background-color: white;
                border-bottom-color: white;
                color: #2196F3;
            }
            QTabBar::tab:hover {
                background-color: #e0e0e0;
            }
            QTabWidget::tab-bar {
                alignment: left;
            }
        """)

        # Create tabs
        self.livestream_tab = LivestreamTab()
        self.testing_tab = LivestreamTestingTab(main_window=self)
        self.settings_tab = SettingsTab(main_window=self)

        # Add tabs
        self.tab_widget.addTab(self.livestream_tab, "🎯 Live Stream Bot")
        self.tab_widget.addTab(self.testing_tab, "🧪 Stream Testing")
        self.tab_widget.addTab(self.settings_tab, "⚙️ Settings")

        layout.addWidget(self.tab_widget)

        # Status bar
        self.status_bar = self.statusBar()
        self.status_bar.showMessage("Ready - All components loaded")

    def setup_menu(self):
        """Setup compact and responsive menu bar"""
        menubar = self.menuBar()

        # Make menubar more compact
        menubar.setStyleSheet("""
            QMenuBar {
                background-color: #f0f0f0;
                border: none;
                padding: 2px;
                font-size: 11px;
                spacing: 2px;
            }
            QMenuBar::item {
                background-color: transparent;
                padding: 4px 8px;
                margin: 1px;
                border-radius: 3px;
            }
            QMenuBar::item:selected {
                background-color: #e0e0e0;
            }
            QMenuBar::item:pressed {
                background-color: #d0d0d0;
            }
            QMenu {
                background-color: white;
                border: 1px solid #ccc;
                padding: 2px;
                font-size: 11px;
            }
            QMenu::item {
                padding: 4px 20px 4px 8px;
                margin: 1px;
                border-radius: 2px;
            }
            QMenu::item:selected {
                background-color: #e0e0e0;
            }
            QMenu::separator {
                height: 1px;
                background-color: #ccc;
                margin: 2px 0px;
            }
        """)

        # File menu (compact)
        file_menu = menubar.addMenu("File")

        # Settings
        settings_action = QAction("⚙️ Settings", self)
        settings_action.setShortcut("Ctrl+,")
        settings_action.triggered.connect(self.show_settings)
        file_menu.addAction(settings_action)

        file_menu.addSeparator()

        # Exit
        exit_action = QAction("Exit", self)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # Tools menu (compact)
        tools_menu = menubar.addMenu("Tools")

        # OCR Region Selector
        if OCR_REGION_SELECTOR_AVAILABLE:
            ocr_region_action = QAction("🎯 OCR Region", self)
            ocr_region_action.setShortcut("Ctrl+R")
            ocr_region_action.triggered.connect(self.open_global_ocr_region_selector)
            tools_menu.addAction(ocr_region_action)

        # Smart Features (condensed)
        if SMART_FEATURES_AVAILABLE:
            analytics_action = QAction("📊 Analytics", self)
            analytics_action.triggered.connect(self.show_analytics_dashboard)
            tools_menu.addAction(analytics_action)

            code_history_action = QAction("📚 Code History", self)
            code_history_action.triggered.connect(self.show_code_history)
            tools_menu.addAction(code_history_action)

            tools_menu.addSeparator()

        # System Info
        system_info_action = QAction("💻 System Info", self)
        system_info_action.triggered.connect(self.show_system_info)
        tools_menu.addAction(system_info_action)

        # Help menu (compact)
        help_menu = menubar.addMenu("Help")

        # About
        about_action = QAction("ℹ️ About", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

        # Documentation
        docs_action = QAction("📖 Docs", self)
        docs_action.setShortcut("F1")
        docs_action.triggered.connect(self.show_documentation)
        help_menu.addAction(docs_action)

    def log_startup_info(self):
        """Log startup information"""
        startup_info = []
        startup_info.append("🚀 MFFUHijack Application Started")
        startup_info.append(f"📅 Startup Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        startup_info.append("📋 Component Status:")
        startup_info.append(f"  • OCR Available: {'✅' if OCR_AVAILABLE else '❌'}")
        startup_info.append(f"  • EasyOCR: {'✅' if EASYOCR_AVAILABLE else '❌'}")
        startup_info.append(f"  • PaddleOCR: {'✅' if PADDLEOCR_AVAILABLE else '❌'}")
        startup_info.append(f"  • Code Submitter: {'✅' if SUBMITTER_AVAILABLE else '❌'}")
        startup_info.append(f"  • Testing Mode: {'✅' if TESTING_MODE_AVAILABLE else '❌'}")
        startup_info.append(f"  • OCR Region Selector: {'✅' if OCR_REGION_SELECTOR_AVAILABLE else '❌'}")
        startup_info.append(f"  • Smart Features: {'✅' if SMART_FEATURES_AVAILABLE else '❌'}")

        # Add GPU status information
        if OCR_AVAILABLE:
            try:
                from ocr_utils import ocr_manager
                gpu_status = ocr_manager.get_gpu_status()

                startup_info.append("🎮 GPU Status:")
                if gpu_status['overall_gpu_available']:
                    startup_info.append(f"  • GPU Acceleration: ✅ ENABLED")
                    startup_info.append(f"  • Recommended Engine: {gpu_status['recommended_engine']}")

                    # Show GPU details for EasyOCR
                    easyocr_info = gpu_status['engines'].get('EasyOCR', {})
                    if easyocr_info.get('using_gpu', False):
                        gpu_name = easyocr_info.get('gpu_name', 'Unknown GPU')
                        gpu_memory = easyocr_info.get('gpu_memory_total', 'Unknown')
                        startup_info.append(f"  • GPU Device: {gpu_name} ({gpu_memory})")
                else:
                    startup_info.append(f"  • GPU Acceleration: ❌ DISABLED (CPU only)")
                    startup_info.append(f"  • Install CUDA for faster processing")

            except Exception as e:
                startup_info.append(f"  • GPU Status: ❌ Error checking GPU: {e}")

        # Log to livestream tab
        for info in startup_info:
            self.livestream_tab.log_message(info)

        print("\n".join(startup_info))

    def open_global_ocr_region_selector(self):
        """Open global OCR region selector"""
        if not OCR_REGION_SELECTOR_AVAILABLE:
            QMessageBox.warning(self, "Not Available", "OCR region selector is not available")
            return

        try:
            # Get current region from active tab
            current_tab = self.tab_widget.currentWidget()
            if hasattr(current_tab, 'current_ocr_region'):
                initial_region = current_tab.current_ocr_region
            else:
                initial_region = (0.0, 67.0, 100.0, 33.0)

            dialog = OCRRegionSelectorDialog(self, initial_region=initial_region)
            if dialog.exec() == QDialog.DialogCode.Accepted:
                selected_region = dialog.get_selected_region()

                # Update all tabs with the new region
                if hasattr(self.livestream_tab, 'current_ocr_region'):
                    self.livestream_tab.current_ocr_region = selected_region
                    # Save settings from livestream tab
                    if hasattr(self.livestream_tab, 'save_ocr_region_settings'):
                        self.livestream_tab.save_ocr_region_settings(selected_region)
                if hasattr(self.testing_tab, 'current_ocr_region'):
                    self.testing_tab.current_ocr_region = selected_region
                    self.testing_tab.update_region_display()

                x, y, w, h = selected_region
                self.status_bar.showMessage(f"OCR region updated: {x:.1f}%, {y:.1f}%, {w:.1f}%, {h:.1f}%")

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to open OCR region selector: {e}")

    def show_analytics_dashboard(self):
        """Show analytics dashboard"""
        if not SMART_FEATURES_AVAILABLE:
            QMessageBox.information(self, "Analytics Dashboard",
                                  "Smart features are not available.\n\n"
                                  "The analytics dashboard requires the smart_features module "
                                  "and temporal_analytics_dashboard module to be installed.")
            return

        try:
            # Check if we already have a dashboard instance
            if not hasattr(self, 'analytics_dashboard') or self.analytics_dashboard is None:
                from temporal_analytics_dashboard import TemporalAnalyticsDashboard
                self.analytics_dashboard = TemporalAnalyticsDashboard()

                # Connect to smart features if available
                if self.smart_features and hasattr(self.smart_features, 'time_analyzer'):
                    self.analytics_dashboard.set_time_analyzer(self.smart_features.time_analyzer)

            # Show and bring to front
            self.analytics_dashboard.show()
            self.analytics_dashboard.raise_()
            self.analytics_dashboard.activateWindow()

        except ImportError as e:
            QMessageBox.warning(self, "Module Not Found",
                              f"Analytics dashboard module not found:\n{e}\n\n"
                              f"Please ensure temporal_analytics_dashboard.py is available.")
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to open analytics dashboard:\n{e}")

    def show_code_history(self):
        """Show code history dialog"""
        if not SMART_FEATURES_AVAILABLE:
            QMessageBox.information(self, "Code History",
                                  "Smart features are not available.\n\n"
                                  "The code history feature requires the smart_features module "
                                  "to be installed for persistent code storage and analysis.")
            return

        try:
            # Create code history dialog
            dialog = QDialog(self)
            dialog.setWindowTitle("📚 Code History")
            dialog.setModal(True)
            dialog.resize(900, 600)
            dialog.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)

            layout = QVBoxLayout(dialog)
            layout.setSpacing(10)
            layout.setContentsMargins(15, 15, 15, 15)

            # Header
            header_label = QLabel("📚 Code Detection History")
            header_label.setFont(QFont("Arial", 14, QFont.Weight.Bold))
            header_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            layout.addWidget(header_label)

            # Search and filter section
            search_group = QGroupBox("🔍 Search & Filter")
            search_layout = QHBoxLayout(search_group)

            search_layout.addWidget(QLabel("Search:"))
            search_input = QLineEdit()
            search_input.setPlaceholderText("Enter code, account type, or date...")
            search_layout.addWidget(search_input)

            search_btn = QPushButton("🔍 Search")
            search_layout.addWidget(search_btn)

            # Account type filter
            search_layout.addWidget(QLabel("Type:"))
            type_filter = QComboBox()
            type_filter.addItems(["All Types", "Free Reset Code", "Starter", "Starter Plus", "Expert"])
            search_layout.addWidget(type_filter)

            layout.addWidget(search_group)

            # Results table
            results_group = QGroupBox("📋 Detection History")
            results_layout = QVBoxLayout(results_group)

            results_tree = QTreeWidget()
            results_tree.setHeaderLabels(["Timestamp", "Code", "Account Type", "Confidence", "Valid", "Source"])
            results_tree.setAlternatingRowColors(True)
            results_tree.setSortingEnabled(True)
            results_tree.setRootIsDecorated(False)

            # Add sample data if smart features are available
            if self.smart_features and hasattr(self.smart_features, 'code_history'):
                try:
                    # Get recent codes from smart features
                    recent_codes = self.smart_features.code_history.get_recent_codes(limit=100)
                    for code_data in recent_codes:
                        item = QTreeWidgetItem([
                            code_data.get('timestamp', 'Unknown'),
                            code_data.get('code', 'N/A'),
                            code_data.get('account_type', 'Unknown'),
                            f"{code_data.get('confidence', 0):.2f}",
                            "✅" if code_data.get('valid', False) else "❌",
                            code_data.get('source', 'Live Stream')
                        ])
                        results_tree.addTopLevelItem(item)
                except Exception as e:
                    # Add placeholder item if no data available
                    item = QTreeWidgetItem([
                        "No data available",
                        "Start scanning to see history",
                        "-", "-", "-", "-"
                    ])
                    results_tree.addTopLevelItem(item)
            else:
                # Add sample data for demonstration
                sample_data = [
                    ["2025-01-13 10:30:15", "RESET3J", "Free Reset Code", "0.95", "✅", "Live Stream"],
                    ["2025-01-13 10:25:42", "START55", "Starter", "0.88", "✅", "Live Stream"],
                    ["2025-01-13 10:20:18", "PLUS99", "Starter Plus", "0.92", "❌", "Stream Testing"],
                    ["2025-01-13 10:15:33", "EXPERT77", "Expert", "0.87", "✅", "Live Stream"],
                ]

                for data in sample_data:
                    item = QTreeWidgetItem(data)
                    results_tree.addTopLevelItem(item)

            results_layout.addWidget(results_tree)
            layout.addWidget(results_group)

            # Statistics section
            stats_group = QGroupBox("📊 Statistics")
            stats_layout = QGridLayout(stats_group)

            # Calculate stats
            total_codes = results_tree.topLevelItemCount()
            valid_codes = sum(1 for i in range(total_codes)
                            if results_tree.topLevelItem(i).text(4) == "✅")

            stats_layout.addWidget(QLabel("Total Codes:"), 0, 0)
            stats_layout.addWidget(QLabel(str(total_codes)), 0, 1)

            stats_layout.addWidget(QLabel("Valid Codes:"), 0, 2)
            stats_layout.addWidget(QLabel(str(valid_codes)), 0, 3)

            stats_layout.addWidget(QLabel("Success Rate:"), 1, 0)
            success_rate = (valid_codes / total_codes * 100) if total_codes > 0 else 0
            stats_layout.addWidget(QLabel(f"{success_rate:.1f}%"), 1, 1)

            stats_layout.addWidget(QLabel("Most Recent:"), 1, 2)
            most_recent = results_tree.topLevelItem(0).text(0) if total_codes > 0 else "None"
            stats_layout.addWidget(QLabel(most_recent), 1, 3)

            layout.addWidget(stats_group)

            # Buttons
            button_layout = QHBoxLayout()

            export_btn = QPushButton("📤 Export CSV")
            export_btn.clicked.connect(lambda: self.export_code_history(results_tree))
            button_layout.addWidget(export_btn)

            refresh_btn = QPushButton("🔄 Refresh")
            refresh_btn.clicked.connect(lambda: self.refresh_code_history(results_tree))
            button_layout.addWidget(refresh_btn)

            button_layout.addStretch()

            close_btn = QPushButton("❌ Close")
            close_btn.clicked.connect(dialog.close)
            button_layout.addWidget(close_btn)

            layout.addLayout(button_layout)

            # Connect search functionality
            def perform_search():
                search_text = search_input.text().lower()
                type_filter_text = type_filter.currentText()

                for i in range(results_tree.topLevelItemCount()):
                    item = results_tree.topLevelItem(i)
                    show_item = True

                    # Text search
                    if search_text:
                        item_text = " ".join([item.text(j) for j in range(item.columnCount())]).lower()
                        if search_text not in item_text:
                            show_item = False

                    # Type filter
                    if type_filter_text != "All Types" and item.text(2) != type_filter_text:
                        show_item = False

                    item.setHidden(not show_item)

            search_btn.clicked.connect(perform_search)
            search_input.textChanged.connect(perform_search)
            type_filter.currentTextChanged.connect(perform_search)

            dialog.exec()

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to open code history:\n{e}")

    def export_code_history(self, tree_widget):
        """Export code history to CSV"""
        try:
            filename, _ = QFileDialog.getSaveFileName(
                self, "Export Code History", "code_history.csv",
                "CSV Files (*.csv);;All Files (*)"
            )

            if filename:
                import csv
                with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                    writer = csv.writer(csvfile)

                    # Write header
                    headers = []
                    for i in range(tree_widget.columnCount()):
                        headers.append(tree_widget.headerItem().text(i))
                    writer.writerow(headers)

                    # Write data
                    for i in range(tree_widget.topLevelItemCount()):
                        item = tree_widget.topLevelItem(i)
                        if not item.isHidden():
                            row = []
                            for j in range(item.columnCount()):
                                row.append(item.text(j))
                            writer.writerow(row)

                QMessageBox.information(self, "Export Successful",
                                      f"Code history exported to:\n{filename}")
        except Exception as e:
            QMessageBox.critical(self, "Export Failed", f"Failed to export code history:\n{e}")

    def refresh_code_history(self, tree_widget):
        """Refresh code history data"""
        try:
            tree_widget.clear()

            # Reload data from smart features if available
            if self.smart_features and hasattr(self.smart_features, 'code_history'):
                recent_codes = self.smart_features.code_history.get_recent_codes(limit=100)
                for code_data in recent_codes:
                    item = QTreeWidgetItem([
                        code_data.get('timestamp', 'Unknown'),
                        code_data.get('code', 'N/A'),
                        code_data.get('account_type', 'Unknown'),
                        f"{code_data.get('confidence', 0):.2f}",
                        "✅" if code_data.get('valid', False) else "❌",
                        code_data.get('source', 'Live Stream')
                    ])
                    tree_widget.addTopLevelItem(item)

            QMessageBox.information(self, "Refresh Complete", "Code history has been refreshed.")

        except Exception as e:
            QMessageBox.warning(self, "Refresh Failed", f"Failed to refresh code history:\n{e}")

    def show_system_info(self):
        """Show system information"""
        try:
            import platform

            info_text = f"""
System Information:

Platform: {platform.system()} {platform.release()}
Python Version: {platform.python_version()}
Architecture: {platform.machine()}

Component Status:
• OCR Available: {'✅' if OCR_AVAILABLE else '❌'}
• EasyOCR: {'✅' if EASYOCR_AVAILABLE else '❌'}
• PaddleOCR: {'✅' if PADDLEOCR_AVAILABLE else '❌'}
• Code Submitter: {'✅' if SUBMITTER_AVAILABLE else '❌'}
• Testing Mode: {'✅' if TESTING_MODE_AVAILABLE else '❌'}
• OCR Region Selector: {'✅' if OCR_REGION_SELECTOR_AVAILABLE else '❌'}
• Smart Features: {'✅' if SMART_FEATURES_AVAILABLE else '❌'}

Memory Usage: {psutil.virtual_memory().percent:.1f}% if hasattr(psutil, 'virtual_memory') else 'N/A'
            """.strip()

            QMessageBox.information(self, "System Information", info_text)

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to get system information: {e}")

    def show_settings(self):
        """Show settings dialog"""
        QMessageBox.information(self, "Settings", "Settings dialog would be implemented here.")

    def show_about(self):
        """Show about dialog"""
        about_text = """
MFFUHijack - Real-Time OCR Livestream Code Detection

Version: 2.0
Author: jordan
Description: Advanced OCR system for detecting and validating codes from livestreams

Features:
• Real-time livestream monitoring
• Previous stream testing with configurable intervals
• Manual OCR region selection with resizable rectangles
• Smart features with time-based analysis
• Code validation without purchasing
• Comprehensive analytics and reporting

© 2025 MFFUHijack Project
        """.strip()

        QMessageBox.about(self, "About MFFUHijack", about_text)

    def get_settings(self):
        """Get current settings from settings tab"""
        if hasattr(self, 'settings_tab'):
            return self.settings_tab.get_settings()
        return {}

    def apply_settings(self, settings):
        """Apply settings to settings tab and application"""
        if hasattr(self, 'settings_tab'):
            self.settings_tab.apply_settings(settings)
            self.apply_settings_to_application(settings)

    def apply_settings_to_application(self, settings):
        """Apply settings to the actual application behavior"""
        try:
            # Apply OCR settings
            ocr_settings = settings.get('ocr', {})
            if hasattr(self, 'livestream_tab'):
                # Update OCR engine in livestream tab
                if hasattr(self.livestream_tab, 'ocr_engine_combo'):
                    engine = ocr_settings.get('engine', 'EasyOCR')
                    self.livestream_tab.ocr_engine_combo.setCurrentText(engine)

                # Update GPU setting
                if hasattr(self.livestream_tab, 'gpu_checkbox'):
                    gpu_enabled = ocr_settings.get('gpu_enabled', True)
                    self.livestream_tab.gpu_checkbox.setChecked(gpu_enabled)

            # Apply browser settings to livestream tab
            browser_settings = settings.get('browser', {})
            if hasattr(self, 'livestream_tab'):
                if hasattr(self.livestream_tab, 'username_input'):
                    self.livestream_tab.username_input.setText(browser_settings.get('username', ''))
                if hasattr(self.livestream_tab, 'password_input'):
                    self.livestream_tab.password_input.setText(browser_settings.get('password', ''))

            # Apply account patterns
            patterns = settings.get('account_patterns', {})
            if patterns and hasattr(self, 'livestream_tab'):
                # Update OCR manager with new patterns
                try:
                    from ocr_utils import ocr_manager
                    for account_type, pattern in patterns.items():
                        if pattern.strip():
                            # Convert comma-separated patterns to list
                            pattern_list = [p.strip() for p in pattern.split(',') if p.strip()]
                            ocr_manager.update_account_patterns(account_type, pattern_list)
                except ImportError:
                    pass  # OCR utils not available

            # Apply general settings
            general_settings = settings.get('general', {})

            # Apply theme if supported
            theme = general_settings.get('theme', 'Default')
            if theme != 'Default':
                self.apply_theme(theme)

            # Apply logging level
            logging_level = general_settings.get('logging_level', 'Info')
            self.set_logging_level(logging_level)

        except Exception as e:
            print(f"Error applying settings to application: {e}")

    def apply_theme(self, theme):
        """Apply theme to the application"""
        try:
            if theme == 'Dark':
                # Apply dark theme
                self.setStyleSheet("""
                    QMainWindow { background-color: #2b2b2b; color: #ffffff; }
                    QTabWidget::pane { border: 1px solid #555; background-color: #2b2b2b; }
                    QTabBar::tab { background-color: #3c3c3c; color: #ffffff; padding: 8px; }
                    QTabBar::tab:selected { background-color: #555; }
                    QGroupBox { color: #ffffff; border: 1px solid #555; }
                    QLabel { color: #ffffff; }
                    QPushButton { background-color: #3c3c3c; color: #ffffff; border: 1px solid #555; }
                    QPushButton:hover { background-color: #555; }
                """)
            elif theme == 'Light':
                # Apply light theme
                self.setStyleSheet("""
                    QMainWindow { background-color: #ffffff; color: #000000; }
                    QTabWidget::pane { border: 1px solid #ccc; background-color: #ffffff; }
                    QTabBar::tab { background-color: #f0f0f0; color: #000000; padding: 8px; }
                    QTabBar::tab:selected { background-color: #ffffff; }
                    QGroupBox { color: #000000; border: 1px solid #ccc; }
                    QLabel { color: #000000; }
                """)
            else:
                # Default theme - clear stylesheet
                self.setStyleSheet("")
        except Exception as e:
            print(f"Error applying theme: {e}")

    def set_logging_level(self, level):
        """Set application logging level"""
        try:
            import logging
            level_map = {
                'Debug': logging.DEBUG,
                'Info': logging.INFO,
                'Warning': logging.WARNING,
                'Error': logging.ERROR
            }
            if level in level_map:
                logging.getLogger().setLevel(level_map[level])
        except Exception as e:
            print(f"Error setting logging level: {e}")

    def show_settings(self):
        """Show settings tab"""
        if hasattr(self, 'tab_widget') and hasattr(self, 'settings_tab'):
            # Switch to settings tab
            settings_index = self.tab_widget.indexOf(self.settings_tab)
            if settings_index >= 0:
                self.tab_widget.setCurrentIndex(settings_index)

    def show_documentation(self):
        """Show documentation"""
        docs_text = """
MFFUHijack Documentation:

1. Live Stream Bot Tab:
   • Configure channel and stream URL
   • Select OCR engine (EasyOCR/PaddleOCR)
   • Set polling interval
   • Manual OCR region selection

2. Stream Testing Tab:
   • Test previous livestreams
   • Download streams from YouTube
   • Configurable frame intervals (5s, 10s, 15s, etc.)
   • Code validation without purchasing
   • Real-time testing results

3. Settings Tab:
   • General application settings
   • OCR engine configuration
   • Browser automation setup
   • Account type patterns
   • Advanced performance options

4. Smart Features:
   • Time-based analysis
   • Character confusion correction
   • Code history tracking
   • Comprehensive analytics

For more information, check the documentation files.
        """.strip()

        QMessageBox.information(self, "Documentation", docs_text)


def main():
    """Main application entry point"""
    print("🚀 Starting MFFUHijack Application")
    print("=" * 50)

    # Create application
    app = QApplication(sys.argv)
    app.setApplicationName("MFFUHijack")
    app.setApplicationVersion("2.0")
    app.setOrganizationName("MFFUHijack Project")

    # Set application icon (if available)
    try:
        app.setWindowIcon(QIcon("icon.png"))
    except:
        pass

    # Create and show main window
    main_window = MFFUHijackMainWindow()
    main_window.show()

    # Initialize clean console
    try:
        from MFFUHijack_Support.browser_automation import initialize_console
        initialize_console()
    except Exception:
        print("Application started successfully")
        print("Available features:")
        print(f"  Live Stream Bot: Available")
        print(f"  Stream Testing: {'Available' if TESTING_MODE_AVAILABLE else 'Not Available'}")
        print(f"  OCR Engines: {'Available' if (EASYOCR_AVAILABLE or PADDLEOCR_AVAILABLE) else 'Not Available'}")
        print(f"  Manual OCR Region: {'Available' if OCR_REGION_SELECTOR_AVAILABLE else 'Not Available'}")
        print(f"  Smart Features: {'Available' if SMART_FEATURES_AVAILABLE else 'Not Available'}")
        print("=" * 50)

    # Run application
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
