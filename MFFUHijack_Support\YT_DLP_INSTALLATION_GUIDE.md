# yt-dlp Installation Guide for MFFUHijack

## 🎯 Overview

MFFUHijack now features an **enhanced yt-dlp installation system** that automatically handles all yt-dlp installation scenarios with multiple fallback methods. No manual downloads are required in most cases!

## 🚀 How It Works

### Automatic Installation Process

When you first run MFFUHijack, the system will:

1. **Check if yt-dlp is already available** ✅
2. **Method 1: Install via pip** (preferred method)
   - Runs: `pip install yt-dlp --upgrade`
   - This installs yt-dlp as a Python module
   - Works on all platforms (Windows, macOS, Linux)

3. **Method 2: Download executable** (fallback)
   - If pip fails, automatically downloads the latest yt-dlp executable
   - Windows: Downloads `yt-dlp.exe`
   - macOS/Linux: Downloads `yt-dlp` binary
   - Saves to `bin/` directory in your MFFUHijack folder

4. **Method 3: Manual guidance** (last resort)
   - If automatic methods fail, opens your browser to the yt-dlp releases page
   - Provides clear instructions for manual installation

### What Gets Installed

The system uses **yt-dlp as a Python module** (not the standalone .exe), which is:
- ✅ **More reliable** - Better integration with Python
- ✅ **Easier to update** - Via pip
- ✅ **Cross-platform** - Works on Windows, macOS, Linux
- ✅ **No PATH issues** - Called via `python -m yt_dlp`

## 📋 Installation Methods

### Method 1: Automatic (Recommended)
Simply run MFFUHijack - everything is handled automatically:

```bash
python main.py
```

The system will:
- Detect missing yt-dlp
- Install it automatically
- Continue launching the application

### Method 2: Manual Installation
If you prefer to install manually:

```bash
# Install yt-dlp via pip
pip install yt-dlp

# Or run our enhanced installer
python yt_dlp_installer.py
```

### Method 3: Standalone Executable (Fallback)
If pip installation fails, the system will:
- Automatically download the appropriate executable
- Save it to the `bin/` directory
- Make it executable (on Unix systems)

## 🔧 Technical Details

### How MFFUHijack Uses yt-dlp

The application calls yt-dlp using the command:
```bash
python -m yt_dlp [arguments]
```

This method:
- Works with pip-installed yt-dlp
- Avoids PATH configuration issues
- Provides better error handling
- Is cross-platform compatible

### File Locations

- **Python module**: Installed via pip to your Python environment
- **Executable fallback**: `MFFUHijack/bin/yt-dlp.exe` (Windows) or `MFFUHijack/bin/yt-dlp` (Unix)
- **Configuration**: No additional configuration needed

## 🛠️ Troubleshooting

### If Installation Fails

1. **Check your internet connection**
   - yt-dlp download requires internet access

2. **Try manual pip installation**:
   ```bash
   pip install --upgrade pip
   pip install yt-dlp
   ```

3. **Check Python/pip permissions**:
   - On Windows: Run as Administrator if needed
   - On macOS/Linux: Use `sudo` if required

4. **Use the enhanced installer directly**:
   ```bash
   python yt_dlp_installer.py
   ```

### If yt-dlp Commands Fail

1. **Verify installation**:
   ```bash
   python yt_dlp_installer.py --status-only
   ```

2. **Test the command**:
   ```bash
   python -m yt_dlp --version
   ```

3. **Check for updates**:
   ```bash
   pip install --upgrade yt-dlp
   ```

### Manual Download (Last Resort)

If all automatic methods fail:

1. **Visit**: https://github.com/yt-dlp/yt-dlp/releases
2. **Download**:
   - Windows: `yt-dlp.exe`
   - macOS/Linux: `yt-dlp` (no extension)
3. **Save to**: `MFFUHijack/bin/` directory
4. **Make executable** (Unix only): `chmod +x bin/yt-dlp`
5. **Restart MFFUHijack**

## 📊 Status Checking

You can check the current yt-dlp installation status:

```bash
# Check detailed status
python yt_dlp_installer.py --status-only

# Check if working in MFFUHijack context
python -c "from yt_dlp_installer import get_yt_dlp_status; print(get_yt_dlp_status())"
```

## 🎉 Benefits of Enhanced System

### For Users
- ✅ **Zero manual downloads** in most cases
- ✅ **Automatic updates** via pip
- ✅ **Clear error messages** and guidance
- ✅ **Multiple fallback methods**
- ✅ **Cross-platform compatibility**

### For Developers
- ✅ **Reliable dependency management**
- ✅ **Comprehensive error handling**
- ✅ **Easy testing and debugging**
- ✅ **Consistent behavior across platforms**

## 🔄 Updates

yt-dlp is updated automatically when you:
- Run the MFFUHijack installer
- Use `pip install --upgrade yt-dlp`
- Use the enhanced installer: `python yt_dlp_installer.py`

The system always tries to install the latest version available.

---

## 📞 Support

If you encounter issues with yt-dlp installation:

1. **Check this guide** for troubleshooting steps
2. **Run the status checker**: `python yt_dlp_installer.py --status-only`
3. **Try manual installation**: `pip install yt-dlp`
4. **Check the GitHub releases**: https://github.com/yt-dlp/yt-dlp/releases

The enhanced installation system handles 99% of cases automatically, ensuring MFFUHijack works out of the box!
