#!/usr/bin/env python3
"""
Test script for region selector improvements
Tests black bar detection, smart presets, and enhanced live stream region selector
"""

import sys
import numpy as np
import cv2
from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton
from region_selector import RegionSelectorWidget, detect_black_bars


def create_test_frame_with_black_bars():
    """Create a test frame with black bars like a typical livestream"""
    # Create 1280x720 frame (typical livestream resolution)
    frame = np.ones((720, 1280, 3), dtype=np.uint8) * 200
    
    # Add black bars on left and right (pillarbox)
    frame[:, :160] = 0  # Left black bar
    frame[:, 1120:] = 0  # Right black bar
    
    # Add some content in the center
    cv2.putText(frame, "LIVESTREAM CONTENT", (400, 200),
               cv2.FONT_HERSHEY_SIMPLEX, 1.5, (50, 50, 50), 3)
    cv2.putText(frame, "FREE 100 STARTER: TEST123", (400, 400),
               cv2.FONT_HERSHEY_SIMPLEX, 1.2, (0, 0, 255), 2)
    cv2.putText(frame, "Chat messages here...", (400, 500),
               cv2.FONT_HERSHEY_SIMPLEX, 0.8, (100, 100, 100), 2)
    cv2.putText(frame, "Bottom area for codes", (400, 600),
               cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)
    
    return frame


def create_test_frame_without_black_bars():
    """Create a test frame without black bars"""
    frame = np.ones((720, 1280, 3), dtype=np.uint8) * 200
    
    # Add content across full width
    cv2.putText(frame, "FULL WIDTH CONTENT", (400, 200),
               cv2.FONT_HERSHEY_SIMPLEX, 1.5, (50, 50, 50), 3)
    cv2.putText(frame, "FREE 50 EXPERT: FULL123", (400, 400),
               cv2.FONT_HERSHEY_SIMPLEX, 1.2, (255, 0, 0), 2)
    
    return frame


def test_black_bar_detection():
    """Test the black bar detection function"""
    print("🧪 Testing Black Bar Detection...")
    
    # Test with black bars
    frame_with_bars = create_test_frame_with_black_bars()
    content_area = detect_black_bars(frame_with_bars)
    print(f"Frame with black bars - Content area: {content_area}")
    
    # Expected: should detect left bar at ~160px, right bar at ~1120px
    expected_left = 160
    expected_right = 1120
    detected_left = content_area[0]
    detected_width = content_area[2]
    detected_right = detected_left + detected_width
    
    if abs(detected_left - expected_left) < 20 and abs(detected_right - expected_right) < 20:
        print("✅ Black bar detection: WORKING")
        return True
    else:
        print(f"❌ Black bar detection: FAILED - Expected left ~{expected_left}, right ~{expected_right}")
        print(f"   Got left {detected_left}, right {detected_right}")
        return False


def test_smart_presets():
    """Test smart preset functionality"""
    print("\n🧪 Testing Smart Presets...")
    
    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # Create region selector
        region_selector = RegionSelectorWidget("Test Smart Presets")
        
        # Test with frame that has black bars
        frame_with_bars = create_test_frame_with_black_bars()
        region_selector.set_image(frame_with_bars)
        
        # Test Full Screen preset (should exclude black bars)
        region_selector.set_smart_preset("full_screen")
        x, y, w, h = region_selector.get_region_percent()
        
        # Should exclude black bars (approximately 12.5% on each side for 160px out of 1280px)
        if 10 < x < 15 and w < 90:  # Should start around 12.5% and be less than full width
            print("✅ Smart Full Screen preset: WORKING")
            print(f"   Region: {x:.1f}%, {y:.1f}%, {w:.1f}% × {h:.1f}%")
            smart_presets_working = True
        else:
            print("❌ Smart Full Screen preset: FAILED")
            print(f"   Expected x ~12.5%, w ~75%, got x={x:.1f}%, w={w:.1f}%")
            smart_presets_working = False
        
        # Test with frame without black bars
        frame_without_bars = create_test_frame_without_black_bars()
        region_selector.set_image(frame_without_bars)
        region_selector.set_smart_preset("full_screen")
        x2, y2, w2, h2 = region_selector.get_region_percent()
        
        # Should use full screen when no black bars detected
        if x2 < 5 and w2 > 95:  # Should be close to full width
            print("✅ Smart Full Screen (no bars): WORKING")
            print(f"   Region: {x2:.1f}%, {y2:.1f}%, {w2:.1f}% × {h2:.1f}%")
        else:
            print("❌ Smart Full Screen (no bars): FAILED")
            print(f"   Expected x ~0%, w ~100%, got x={x2:.1f}%, w={w2:.1f}%")
            smart_presets_working = False
        
        return smart_presets_working
        
    except Exception as e:
        print(f"❌ Smart presets test: ERROR - {e}")
        return False


class TestMainWindow(QMainWindow):
    """Test window for manual verification"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Region Selector Test - Manual Verification")
        self.setGeometry(100, 100, 1000, 800)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout()
        
        # Test buttons
        test_with_bars_btn = QPushButton("Test Frame WITH Black Bars")
        test_with_bars_btn.clicked.connect(self.test_with_black_bars)
        layout.addWidget(test_with_bars_btn)
        
        test_without_bars_btn = QPushButton("Test Frame WITHOUT Black Bars")
        test_without_bars_btn.clicked.connect(self.test_without_black_bars)
        layout.addWidget(test_without_bars_btn)
        
        # Region selector
        self.region_selector = RegionSelectorWidget("Manual Test - Try the presets!")
        layout.addWidget(self.region_selector)
        
        central_widget.setLayout(layout)
        
        # Start with black bars test
        self.test_with_black_bars()
    
    def test_with_black_bars(self):
        """Load frame with black bars"""
        frame = create_test_frame_with_black_bars()
        self.region_selector.set_image(frame)
        print("Loaded frame WITH black bars - try the 'Full Screen' preset")
    
    def test_without_black_bars(self):
        """Load frame without black bars"""
        frame = create_test_frame_without_black_bars()
        self.region_selector.set_image(frame)
        print("Loaded frame WITHOUT black bars - try the 'Full Screen' preset")


def main():
    """Run all tests"""
    print("🚀 Region Selector Improvements Test")
    print("=" * 50)
    
    # Automated tests
    black_bar_test = test_black_bar_detection()
    smart_preset_test = test_smart_presets()
    
    print("\n" + "=" * 50)
    print("📊 Automated Test Results:")
    print(f"Black Bar Detection: {'✅ PASS' if black_bar_test else '❌ FAIL'}")
    print(f"Smart Presets: {'✅ PASS' if smart_preset_test else '❌ FAIL'}")
    
    # Manual test
    print("\n🎮 Starting Manual Test Window...")
    print("Instructions:")
    print("1. Try the 'Full Screen' preset with both frame types")
    print("2. Verify that black bars are excluded when present")
    print("3. Try other presets (Bottom Third, Center, etc.)")
    print("4. Check that the red box accurately shows the selected region")
    
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    
    window = TestMainWindow()
    window.show()
    
    return app.exec()


if __name__ == "__main__":
    sys.exit(main())
