#!/usr/bin/env python3
"""
Test script for the enhanced yt-dlp installer
"""

import sys
import os
from pathlib import Path

# Add current directory to path
sys.path.append(str(Path(__file__).parent))

from yt_dlp_installer import YtDlpInstaller, install_yt_dlp_if_needed, get_yt_dlp_status


def test_status_check():
    """Test the status checking functionality"""
    print("🧪 Testing yt-dlp status check...")
    print("=" * 50)
    
    installer = YtDlpInstaller()
    
    print(f"Module available: {installer.check_yt_dlp_module()}")
    print(f"Command working: {installer.check_yt_dlp_command()}")
    
    status = get_yt_dlp_status()
    print("\nDetailed status:")
    for key, value in status.items():
        print(f"  {key}: {value}")
    
    return installer.check_yt_dlp_command()


def test_installation():
    """Test the installation process"""
    print("\n🧪 Testing yt-dlp installation...")
    print("=" * 50)
    
    # Test with auto-download enabled
    success = install_yt_dlp_if_needed(auto_download=True)
    
    if success:
        print("✅ Installation test passed!")
        return True
    else:
        print("❌ Installation test failed!")
        return False


def test_command_execution():
    """Test that yt-dlp commands work after installation"""
    print("\n🧪 Testing yt-dlp command execution...")
    print("=" * 50)
    
    import subprocess
    
    try:
        # Test the command that our application uses
        result = subprocess.run([
            'python', '-m', 'yt_dlp', '--version'
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            version = result.stdout.strip()
            print(f"✅ yt-dlp command works! Version: {version}")
            return True
        else:
            print(f"❌ yt-dlp command failed: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Command execution error: {e}")
        return False


def main():
    """Run all tests"""
    print("🚀 yt-dlp Installer Test Suite")
    print("=" * 60)
    
    tests_passed = 0
    total_tests = 3
    
    # Test 1: Status check
    if test_status_check():
        tests_passed += 1
        print("✅ Test 1 passed: Status check")
    else:
        print("❌ Test 1 failed: Status check")
    
    # Test 2: Installation
    if test_installation():
        tests_passed += 1
        print("✅ Test 2 passed: Installation")
    else:
        print("❌ Test 2 failed: Installation")
    
    # Test 3: Command execution
    if test_command_execution():
        tests_passed += 1
        print("✅ Test 3 passed: Command execution")
    else:
        print("❌ Test 3 failed: Command execution")
    
    # Summary
    print("\n📊 Test Results")
    print("=" * 30)
    print(f"Tests passed: {tests_passed}/{total_tests}")
    
    if tests_passed == total_tests:
        print("🎉 All tests passed! yt-dlp is ready for MFFUHijack")
        return True
    else:
        print("⚠️  Some tests failed. yt-dlp may need manual installation")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
