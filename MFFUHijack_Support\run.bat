@echo off
echo MFFUHijack Launcher
echo ===================
echo.

REM Try different Python commands
python --version >nul 2>&1
if %errorlevel% == 0 (
    echo Using python command
    python launch.py
    goto :end
)

python3 --version >nul 2>&1
if %errorlevel% == 0 (
    echo Using python3 command
    python3 launch.py
    goto :end
)

py --version >nul 2>&1
if %errorlevel% == 0 (
    echo Using py command
    py launch.py
    goto :end
)

echo Error: Python not found in PATH
echo Please install Python 3.8+ or add it to your PATH
echo.
echo Alternative options:
echo   python install.py     - Check dependencies manually
echo   python main.py        - Start app directly (skip dependency check)
echo.
pause

:end
echo.
echo Press any key to exit...
pause >nul
