# MFFUHijack Live Scan Monitor Guide

## 🎯 Overview

The Live Scan Monitor is a dedicated GUI window that provides real-time monitoring and statistics for live scanning sessions in MFFUHijack. This window automatically opens when you start a live scan and displays comprehensive information about the scanning session, including stream status, code detection statistics, and detailed activity logs.

## ✨ Features

### 📺 Stream Information Section
- **Current livestream URL** being monitored
- **Stream duration/elapsed time** since scan started
- **Stream status** (Live/Offline/Error) with visual indicators
- **Current playback speed** (displays 1.5x as per requirements)

### 🎥 Live Preview Section
- **Real-time video feed** with OCR detection box overlay
- **Red OCR region box** showing the exact scanning area
- **Accurate scanning area** that excludes black bars
- **Current frame number** and timestamp display
- **Frame processing statistics**

### 📊 Code Detection Statistics
- **Total codes detected** during the session
- **Valid codes found** (codes matching configured patterns)
- **Invalid codes found** (codes that don't match or are duplicates)
- **Success rate percentage** (valid/total detected)
- **Real-time updates** as codes are detected

### 🏷️ Account Type Tracking
- **Individual counters** for each account type:
  - Reset
  - Starter
  - Starter Plus
  - Expert
  - Free Reset Code
- **Pattern matching display** showing which patterns triggered detections
- **Real-time classification** of detected codes

### 📝 Session Activity Log
- **Timestamped events** for each code detection
- **Real-time status messages** (scanning, code found, etc.)
- **Error messages and warnings** specific to the session
- **Session start/stop notifications**
- **Auto-scrolling** to show latest activity

## 🚀 How It Works

### Automatic Window Management
1. **Opens automatically** when you start a live scan from any GUI interface
2. **Connects to LiveBot signals** for real-time updates
3. **Positions itself** appropriately on screen
4. **Stays on top** or easily accessible during scanning
5. **Closes gracefully** when scanning session ends

### Real-Time Updates
- **Frame preview updates** every time a new frame is processed
- **Statistics refresh** every second during active scanning
- **Immediate code detection** notifications with full details
- **Live session duration** tracking with HH:MM:SS format

### Integration with Main GUI
- **Seamless integration** with all GUI variants (gui.py, system_gui.py, modern_gui.py, elegant_gui.py)
- **Automatic signal connections** when LiveBot is initialized
- **No interference** with existing functionality
- **Independent operation** - can be closed without stopping the scan

## 🎮 Usage Instructions

### Starting a Live Scan Session
1. Open MFFUHijack with any GUI interface
2. Navigate to the Livestream Bot tab
3. Configure your settings (URL, OCR engine, etc.)
4. Click "Start Bot" - the Live Scan Monitor will open automatically
5. The monitor window will display "🟢 Scanning" status

### During a Scanning Session
- **Monitor the live preview** to see the video feed and OCR region
- **Watch statistics update** in real-time as codes are detected
- **Review the activity log** for detailed session information
- **Track account type distribution** in the tracking section
- **Check success rate** to monitor detection accuracy

### Ending a Session
1. Stop the bot from the main GUI or close the monitor window
2. The monitor will display final session statistics
3. Session duration and total results will be logged
4. The window can be closed or left open for review

## 🛠️ Technical Implementation

### Core Components

#### `ScanStatistics` Class
- Tracks all session metrics and statistics
- Calculates success rates and session duration
- Manages detection event history
- Validates detected codes based on confidence and patterns

#### `LiveScanMonitorWindow` Class
- Main window implementation with PyQt6
- Real-time UI updates via QTimer
- Signal connections to LiveBot events
- Comprehensive layout with multiple sections

#### LiveBot Integration
- New methods in `LiveBot` class:
  - `set_monitor_window()` - Connect monitor to bot
  - `open_monitor_window()` - Auto-open on scan start
  - `close_monitor_window()` - Auto-close on scan end
- Automatic signal routing to monitor window
- Frame preview with OCR region overlay

### GUI Integration
All main GUI classes have been updated:
- **gui.py** - Simple GUI integration
- **system_gui.py** - System default GUI integration  
- **modern_gui.py** - Modern GUI integration
- **elegant_gui.py** - Elegant GUI integration

Each integration includes:
- Monitor window initialization in `__init__`
- LiveBot connection in `connect_signals`
- Automatic window management

## 🧪 Testing

### Test Script
Use `test_live_scan_monitor.py` to test the monitor window:

```bash
python test_live_scan_monitor.py
```

The test script provides:
- **Control window** for testing monitor functionality
- **Simulated code detections** with various account types
- **Automatic simulation** every 3 seconds during active sessions
- **Manual controls** for starting/stopping sessions

### Test Scenarios
1. **Window Creation** - Verify monitor window opens correctly
2. **Session Management** - Test start/stop functionality
3. **Code Detection** - Simulate various code types and validation
4. **Statistics Tracking** - Verify counters and success rate calculations
5. **Activity Logging** - Check timestamp and message formatting
6. **Window Behavior** - Test positioning, closing, and reopening

## 🎨 Styling and Appearance

### Visual Design
- **Clean, professional layout** with grouped sections
- **Color-coded statistics** (green for valid, red for invalid)
- **Status indicators** with emoji icons for quick recognition
- **Responsive layout** that adapts to window resizing
- **Consistent styling** with the main application theme

### Window Behavior
- **Minimum size**: 1200x800 pixels for optimal viewing
- **Resizable** with proper layout scaling
- **Independent positioning** from main window
- **Always accessible** during scanning sessions
- **Graceful closing** with proper cleanup

## 🔧 Configuration

### Customization Options
- **OCR region overlay** color and thickness (currently red, 2px)
- **Update intervals** for statistics and display refresh
- **Message limits** for activity log (prevents memory issues)
- **Window positioning** and sizing preferences

### Integration Settings
- **Automatic opening** can be controlled via LiveBot configuration
- **Signal connections** are established automatically
- **Error handling** with graceful fallbacks if monitor unavailable

## 📋 Requirements

### Dependencies
- PyQt6 (for GUI components)
- OpenCV (for frame processing and overlay drawing)
- datetime (for timestamp and duration tracking)
- All existing MFFUHijack dependencies

### Compatibility
- **Works with all existing GUI interfaces**
- **Compatible with all OCR engines**
- **Supports all account types and patterns**
- **Integrates with existing LiveBot functionality**

## 🚨 Troubleshooting

### Common Issues
1. **Monitor window not opening**: Check LiveBot initialization and signal connections
2. **No frame preview**: Verify video capture is working in main application
3. **Statistics not updating**: Check if scanning session is active
4. **Activity log not scrolling**: Verify auto-scroll is enabled

### Debug Information
- Monitor window logs all activities to the session activity log
- Main application message logger also receives monitor events
- Console output available for debugging signal connections
- Test script provides comprehensive functionality verification

---

The Live Scan Monitor provides a comprehensive, real-time view of your MFFUHijack scanning sessions, making it easier to monitor performance, track results, and optimize your code detection setup.
