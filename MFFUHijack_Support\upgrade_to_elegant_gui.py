#!/usr/bin/env python3
"""
Upgrade script to switch MFFUHijack to the new elegant GUI
This script updates the main.py file to use the elegant interface
"""

import os
import shutil
from datetime import datetime


def backup_current_files():
    """Backup current GUI files"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_dir = f"backup_gui_{timestamp}"
    
    print(f"Creating backup directory: {backup_dir}")
    os.makedirs(backup_dir, exist_ok=True)
    
    # Files to backup
    files_to_backup = [
        "main.py",
        "gui.py", 
        "modern_gui.py"
    ]
    
    for file in files_to_backup:
        if os.path.exists(file):
            shutil.copy2(file, os.path.join(backup_dir, file))
            print(f"Backed up: {file}")
    
    return backup_dir


def update_main_py():
    """Update main.py to use elegant GUI"""
    main_py_content = '''#!/usr/bin/env python3
"""
MFFUHijack - Main Application Entry Point
Real-Time OCR Livestream Code Detection with Elegant GUI
"""

import sys
import os
from PyQt6.QtWidgets import QApplication

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from elegant_gui import ElegantMFFUHijackGUI, message_logger


def main():
    """Main application entry point"""
    # Create QApplication
    app = QApplication(sys.argv)
    
    # Set application properties
    app.setApplicationName("MFFUHijack")
    app.setApplicationVersion("2.0")
    app.setOrganizationName("MFFUHijack")
    
    try:
        # Create and show main window with elegant design
        main_window = ElegantMFFUHijackGUI()
        main_window.show()
        
        # Log startup message
        message_logger.info("MFFUHijack started with elegant interface")
        message_logger.info("All systems ready")
        
        # Run the application
        sys.exit(app.exec())
        
    except Exception as e:
        print(f"Error starting MFFUHijack: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
'''
    
    with open("main.py", "w", encoding="utf-8") as f:
        f.write(main_py_content)
    
    print("Updated main.py to use elegant GUI")


def main():
    """Main upgrade function"""
    print("🎨 MFFUHijack Elegant GUI Upgrade")
    print("=" * 40)
    
    # Check if elegant_gui.py exists
    if not os.path.exists("elegant_gui.py"):
        print("❌ Error: elegant_gui.py not found!")
        print("Make sure the elegant GUI file is in the current directory.")
        return
    
    # Backup current files
    backup_dir = backup_current_files()
    print(f"✅ Backup created: {backup_dir}")
    
    # Update main.py
    update_main_py()
    print("✅ Updated main.py")
    
    print("\n🎉 Upgrade Complete!")
    print("\nThe elegant GUI features:")
    print("• ✨ Clean, modern design")
    print("• 📏 Optimized sizing and spacing")
    print("• 🎯 Simplified interface")
    print("• 🚀 Better performance")
    print("• 📱 Responsive layout")
    
    print(f"\nTo run MFFUHijack with the elegant GUI:")
    print("  python main.py")
    print("\nTo test the GUI separately:")
    print("  python test_elegant_gui.py")
    
    print(f"\nYour original files are backed up in: {backup_dir}")


if __name__ == "__main__":
    main()
