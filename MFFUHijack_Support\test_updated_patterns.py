#!/usr/bin/env python3
"""
Test script for updated pattern system
Tests the new patterns without amount detection and with Free Reset Code
"""

import sys
import os

# Add current directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_pattern_detection():
    """Test the updated pattern detection system"""
    print("🧪 Testing Updated Pattern Detection System")
    print("=" * 60)
    
    # Import after adding to path
    from ocr_utils import ocr_manager
    
    # Test cases based on the provided examples
    test_texts = [
        # Free Reset Code examples
        "x50 FREE RESETS USE CODE: RESET3J",
        "x50 FREE RESETS USE CODE: LUNCHRESET",
        "FREE RESETS USE CODE: TESTRESET",
        "RESETS USE CODE: QUICKRESET",
        
        # Starter Plus example
        "x25 FREE 50K STARTER+ ACCOUNTS USE CODE: HAPPY4TH",
        "STARTER+ ACCOUNTS USE CODE: TESTCODE",
        "STARTER PLUS ACCOUNTS USE CODE: PLUSCODE",
        
        # Additional test cases
        "STARTER ACCOUNTS USE CODE: STARTERTEST",
        "EXPERT ACCOUNTS USE CODE: EXPERTCODE",
        "STARTER: SIMPLE123",
        "EXPERT: EXPERT456",
        
        # Should not match
        "This is just random text",
        "No code here at all",
        "USE CODE: (missing account type)"
    ]
    
    print("Available account types:", ocr_manager.get_enabled_account_types())
    print()
    
    success_count = 0
    total_tests = len(test_texts)
    
    for i, text in enumerate(test_texts, 1):
        print(f"Test {i}/{total_tests}: '{text}'")
        
        # Create mock OCR result
        mock_result = [{'text': text, 'confidence': 0.95}]
        codes = ocr_manager.find_giveaway_codes(mock_result)
        
        if codes:
            for code_data in codes:
                print(f"  ✅ DETECTED:")
                print(f"     Code: {code_data['code']}")
                print(f"     Type: {code_data['type']}")
                print(f"     Confidence: {code_data['confidence']:.2f}")
                success_count += 1
        else:
            print(f"  ❌ NO MATCH")
        print()
    
    return success_count, total_tests

def test_specific_examples():
    """Test the specific examples provided by the user"""
    print("🎯 Testing Specific User Examples")
    print("=" * 60)
    
    from ocr_utils import ocr_manager
    
    # Exact examples from user
    user_examples = [
        {
            "text": "x50 FREE RESETS USE CODE: RESET3J",
            "expected_type": "Free Reset Code",
            "expected_code": "RESET3J"
        },
        {
            "text": "x50 FREE RESETS USE CODE: LUNCHRESET",
            "expected_type": "Free Reset Code", 
            "expected_code": "LUNCHRESET"
        },
        {
            "text": "x25 FREE 50K STARTER+ ACCOUNTS USE CODE: HAPPY4TH",
            "expected_type": "Starter Plus",
            "expected_code": "HAPPY4TH"
        }
    ]
    
    all_passed = True
    
    for i, example in enumerate(user_examples, 1):
        text = example["text"]
        expected_type = example["expected_type"]
        expected_code = example["expected_code"]
        
        print(f"Example {i}: '{text}'")
        
        # Test detection
        mock_result = [{'text': text, 'confidence': 0.95}]
        codes = ocr_manager.find_giveaway_codes(mock_result)
        
        if codes:
            detected = codes[0]  # Should only be one match
            detected_type = detected['type']
            detected_code = detected['code']
            
            if detected_type == expected_type and detected_code == expected_code:
                print(f"  ✅ CORRECT: Type={detected_type}, Code={detected_code}")
            else:
                print(f"  ❌ INCORRECT:")
                print(f"     Expected: Type={expected_type}, Code={expected_code}")
                print(f"     Got:      Type={detected_type}, Code={detected_code}")
                all_passed = False
        else:
            print(f"  ❌ NO DETECTION (Expected: Type={expected_type}, Code={expected_code})")
            all_passed = False
        print()
    
    return all_passed

def test_pattern_config():
    """Test that pattern configuration is working correctly"""
    print("⚙️  Testing Pattern Configuration")
    print("=" * 60)
    
    from ocr_utils import ocr_manager
    
    config = ocr_manager.get_pattern_config()
    
    # Check that Free Reset Code is included
    if "Free Reset Code" in config:
        print("✅ Free Reset Code account type: PRESENT")
        reset_patterns = config["Free Reset Code"]["patterns"]
        print(f"   Patterns: {reset_patterns}")
    else:
        print("❌ Free Reset Code account type: MISSING")
        return False
    
    # Check that patterns don't contain {amount}
    amount_found = False
    for account_type, account_config in config.items():
        patterns = account_config.get("patterns", [])
        for pattern in patterns:
            if "{amount}" in pattern:
                print(f"❌ Found {amount} in {account_type} pattern: {pattern}")
                amount_found = True
    
    if not amount_found:
        print("✅ Amount placeholders: REMOVED (as expected)")
    
    # Check that {code} placeholders are present
    code_found = False
    for account_type, account_config in config.items():
        patterns = account_config.get("patterns", [])
        for pattern in patterns:
            if "{code}" in pattern:
                code_found = True
                break
        if code_found:
            break
    
    if code_found:
        print("✅ Code placeholders: PRESENT (as expected)")
    else:
        print("❌ Code placeholders: MISSING")
        return False
    
    return True

def main():
    """Run all tests"""
    print("🚀 Updated Pattern System Test Suite")
    print("=" * 60)
    print()
    
    # Test pattern configuration
    config_ok = test_pattern_config()
    print()
    
    # Test general pattern detection
    success_count, total_tests = test_pattern_detection()
    print()
    
    # Test specific user examples
    examples_passed = test_specific_examples()
    
    # Summary
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 60)
    print(f"Pattern Configuration: {'✅ PASS' if config_ok else '❌ FAIL'}")
    print(f"General Detection: {success_count}/{total_tests} patterns detected")
    print(f"User Examples: {'✅ ALL PASSED' if examples_passed else '❌ SOME FAILED'}")
    
    overall_success = config_ok and examples_passed and success_count > 0
    print(f"\nOverall Result: {'🎉 SUCCESS' if overall_success else '❌ NEEDS FIXES'}")
    
    if overall_success:
        print("\n✅ The updated pattern system is working correctly!")
        print("   - Free Reset Code account type added")
        print("   - Amount detection removed")
        print("   - Only code and account type are returned")
        print("   - All user examples detected correctly")
    
    return overall_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
