#!/usr/bin/env python3
"""
Test GUI with Mock Video Capture
Tests the video preview and logging functionality using synthetic video
"""

import sys
import time
from pathlib import Path

# Add current directory to path
sys.path.append(str(Path(__file__).parent))

# Enable mock mode before importing live_bot
from mock_video_capture import enable_mock_mode
enable_mock_mode()

from live_bot import LiveBot
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QTimer


def test_gui_preview_with_mock():
    """Test the GUI preview functionality with mock video"""
    print("🧪 Testing GUI Preview with Mock Video")
    print("=" * 50)
    
    # Create QApplication
    app = QApplication(sys.argv)
    
    # Create LiveBot
    bot = LiveBot()
    
    # Track received signals
    frame_count = 0
    code_count = 0
    status_updates = []
    
    def on_frame(pixmap):
        nonlocal frame_count
        frame_count += 1
        print(f"📺 GUI: Received frame {frame_count} - Size: {pixmap.width()}x{pixmap.height()}")
    
    def on_code(code_data):
        nonlocal code_count
        code_count += 1
        print(f"🎯 GUI: Received code {code_count}: {code_data}")
    
    def on_status(status):
        status_updates.append(status)
        print(f"📊 GUI: Status update: {status}")
    
    def on_error(error):
        print(f"❌ GUI: Error: {error}")
    
    # Connect signals
    bot.frame_captured.connect(on_frame)
    bot.code_detected.connect(on_code)
    bot.status_changed.connect(on_status)
    bot.error_occurred.connect(on_error)
    
    print("✅ Signal connections established")
    
    # Start the bot with mock video
    print("\n🚀 Starting bot with mock video...")
    test_url = "https://mock-test-url.com"
    
    success = bot.start(test_url, "EasyOCR", 1.0, region=(0, 67, 100, 33))
    
    if success:
        print("✅ Bot started successfully!")
        print("⏳ Running for 15 seconds to test preview and logging...")
        
        # Create a timer to stop the test
        def stop_test():
            print("\n⏹️  Stopping test...")
            bot.stop()
            app.quit()
        
        timer = QTimer()
        timer.timeout.connect(stop_test)
        timer.start(15000)  # 15 seconds
        
        # Run the application
        app.exec()
        
        # Print results
        print("\n📊 Test Results:")
        print(f"   Frames received: {frame_count}")
        print(f"   Codes detected: {code_count}")
        print(f"   Status updates: {len(status_updates)}")
        
        if frame_count > 0:
            print("✅ Frame preview is working!")
        else:
            print("❌ No frames received - preview not working")
        
        if len(status_updates) > 0:
            print("✅ Status updates are working!")
        else:
            print("❌ No status updates received")
        
        return frame_count > 0 and len(status_updates) > 0
    
    else:
        print("❌ Bot failed to start")
        return False


def test_console_logging():
    """Test console logging functionality"""
    print("\n🧪 Testing Console Logging")
    print("=" * 50)
    
    # Enable mock mode
    from mock_video_capture import MockLivestreamCapture
    
    # Test the capture directly
    capture = MockLivestreamCapture()
    
    print("🚀 Testing mock capture with detailed logging...")
    
    success = capture.start_capture("https://test-logging.com")
    
    if success:
        print("📹 Capturing frames with logging...")
        
        for i in range(5):
            frame = capture.get_frame()
            if frame is not None:
                print(f"   ✅ Frame {i+1} captured successfully")
            else:
                print(f"   ❌ Frame {i+1} failed")
            time.sleep(0.5)
        
        capture.stop_capture()
        return True
    else:
        print("❌ Mock capture failed to start")
        return False


def main():
    """Run all tests"""
    print("🚀 GUI Preview and Logging Test Suite")
    print("=" * 60)
    print("🎭 Using MOCK VIDEO CAPTURE for safe testing")
    print("=" * 60)
    
    tests_passed = 0
    total_tests = 2
    
    # Test 1: Console logging
    try:
        print("\n" + "="*50)
        print("TEST 1: Console Logging")
        print("="*50)
        
        if test_console_logging():
            tests_passed += 1
            print("✅ Test 1 passed: Console logging")
        else:
            print("❌ Test 1 failed: Console logging")
    except Exception as e:
        print(f"❌ Test 1 error: {e}")
    
    # Test 2: GUI preview
    try:
        print("\n" + "="*50)
        print("TEST 2: GUI Preview with Mock Video")
        print("="*50)
        
        if test_gui_preview_with_mock():
            tests_passed += 1
            print("✅ Test 2 passed: GUI preview")
        else:
            print("❌ Test 2 failed: GUI preview")
    except Exception as e:
        print(f"❌ Test 2 error: {e}")
    
    # Summary
    print("\n📊 Final Test Results")
    print("=" * 30)
    print(f"Tests passed: {tests_passed}/{total_tests}")
    
    if tests_passed == total_tests:
        print("🎉 All tests passed! GUI preview and logging are working!")
        print("\n💡 To use mock mode in the main application:")
        print("   1. Import mock_video_capture")
        print("   2. Call enable_mock_mode() before starting the bot")
        print("   3. Use any URL - it will generate synthetic test video")
        return True
    else:
        print("⚠️  Some tests failed. Check the detailed logs above")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
