#!/usr/bin/env python3
"""
Quick Launch Script for MFFUHijack GUI Testing
Provides easy access to all testing components
"""

import sys
import os
from PyQt6.QtWidgets import *
from PyQt6.QtCore import *
from PyQt6.QtGui import *


class QuickLauncher(QMainWindow):
    """Quick launcher for all GUI components"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
    
    def init_ui(self):
        """Initialize launcher UI"""
        self.setWindowTitle("🚀 MFFUHijack GUI Quick Launcher")
        self.setGeometry(200, 200, 600, 500)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # Header
        header_label = QLabel("🚀 MFFUHijack GUI Quick Launcher")
        header_label.setFont(QFont("Arial", 18, QFont.Weight.Bold))
        header_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(header_label)
        
        # Description
        desc_label = QLabel("Choose which component to test:")
        desc_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(desc_label)
        
        layout.addWidget(QLabel(""))  # Spacer
        
        # Launch buttons
        button_layout = QVBoxLayout()
        
        # Test Session Monitor
        monitor_btn = QPushButton("🧪 Test Session Monitor")
        monitor_btn.setMinimumHeight(50)
        monitor_btn.setStyleSheet("""
            QPushButton {
                font-size: 14px;
                font-weight: bold;
                background-color: #2196F3;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 10px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """)
        monitor_btn.clicked.connect(self.launch_test_monitor)
        button_layout.addWidget(monitor_btn)
        
        # Live Scan Monitor
        live_btn = QPushButton("🔍 Enhanced Live Scan Monitor")
        live_btn.setMinimumHeight(50)
        live_btn.setStyleSheet("""
            QPushButton {
                font-size: 14px;
                font-weight: bold;
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 10px;
            }
            QPushButton:hover {
                background-color: #388E3C;
            }
        """)
        live_btn.clicked.connect(self.launch_live_monitor)
        button_layout.addWidget(live_btn)
        
        # Testing Mode
        testing_btn = QPushButton("🧪 Live Stream Testing Mode")
        testing_btn.setMinimumHeight(50)
        testing_btn.setStyleSheet("""
            QPushButton {
                font-size: 14px;
                font-weight: bold;
                background-color: #FF9800;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 10px;
            }
            QPushButton:hover {
                background-color: #F57C00;
            }
        """)
        testing_btn.clicked.connect(self.launch_testing_mode)
        button_layout.addWidget(testing_btn)
        
        # Smart Features Demo
        smart_btn = QPushButton("🧠 Smart Features Demo")
        smart_btn.setMinimumHeight(50)
        smart_btn.setStyleSheet("""
            QPushButton {
                font-size: 14px;
                font-weight: bold;
                background-color: #9C27B0;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 10px;
            }
            QPushButton:hover {
                background-color: #7B1FA2;
            }
        """)
        smart_btn.clicked.connect(self.launch_smart_features)
        button_layout.addWidget(smart_btn)
        
        # Temporal Dashboard
        temporal_btn = QPushButton("📊 Temporal Analytics Dashboard")
        temporal_btn.setMinimumHeight(50)
        temporal_btn.setStyleSheet("""
            QPushButton {
                font-size: 14px;
                font-weight: bold;
                background-color: #607D8B;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 10px;
            }
            QPushButton:hover {
                background-color: #455A64;
            }
        """)
        temporal_btn.clicked.connect(self.launch_temporal_dashboard)
        button_layout.addWidget(temporal_btn)
        
        layout.addLayout(button_layout)
        
        layout.addWidget(QLabel(""))  # Spacer
        
        # Instructions
        instructions_text = """
📋 Testing Instructions:

1. 🧪 Test Session Monitor - Comprehensive testing with logging
2. 🔍 Live Scan Monitor - Main monitoring interface with smart features
3. 🧪 Testing Mode - Previous livestream testing with intervals
4. 🧠 Smart Features - Time analysis, retry system, code history
5. 📊 Temporal Dashboard - Interactive analytics visualization

💡 Tip: Start with the Test Session Monitor for comprehensive testing!
        """
        
        instructions_label = QLabel(instructions_text.strip())
        instructions_label.setWordWrap(True)
        instructions_label.setStyleSheet("""
            QLabel {
                background-color: #f5f5f5;
                border: 1px solid #ddd;
                border-radius: 5px;
                padding: 10px;
                font-size: 11px;
            }
        """)
        layout.addWidget(instructions_label)
    
    def launch_test_monitor(self):
        """Launch test session monitor"""
        try:
            os.system("python test_session_monitor.py")
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to launch Test Session Monitor: {e}")
    
    def launch_live_monitor(self):
        """Launch live scan monitor"""
        try:
            from live_scan_monitor import LiveScanMonitorWindow
            self.live_window = LiveScanMonitorWindow()
            self.live_window.show()
            QMessageBox.information(self, "Launched", "Enhanced Live Scan Monitor opened!")
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to launch Live Scan Monitor: {e}")
    
    def launch_testing_mode(self):
        """Launch testing mode"""
        try:
            from livestream_testing_gui import LiveStreamTestingWindow
            self.testing_window = LiveStreamTestingWindow()
            self.testing_window.show()
            QMessageBox.information(self, "Launched", "Live Stream Testing Mode opened!")
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to launch Testing Mode: {e}")
    
    def launch_smart_features(self):
        """Launch smart features demo"""
        try:
            from test_smart_features import SmartFeaturesTestWindow
            self.smart_window = SmartFeaturesTestWindow()
            self.smart_window.show()
            QMessageBox.information(self, "Launched", "Smart Features Demo opened!")
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to launch Smart Features Demo: {e}")
    
    def launch_temporal_dashboard(self):
        """Launch temporal dashboard"""
        try:
            from temporal_analytics_dashboard import TemporalAnalyticsDashboard
            self.temporal_window = TemporalAnalyticsDashboard()
            self.temporal_window.show()
            QMessageBox.information(self, "Launched", "Temporal Analytics Dashboard opened!")
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to launch Temporal Dashboard: {e}")


def main():
    """Main launcher function"""
    print("🚀 MFFUHijack GUI Quick Launcher")
    print("=" * 40)
    print("Choose which component to test from the GUI")
    
    app = QApplication(sys.argv)
    
    launcher = QuickLauncher()
    launcher.show()
    
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
