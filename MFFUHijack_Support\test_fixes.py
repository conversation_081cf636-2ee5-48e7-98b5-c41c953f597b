#!/usr/bin/env python3
"""
Quick test script to verify the fixes applied to MFFUHijack
Tests YouTube URL processing and PaddleOCR removal
"""

import sys
import subprocess

def test_youtube_url_processing():
    """Test that yt-dlp commands work correctly"""
    print("🧪 Testing YouTube URL processing...")
    
    try:
        # Test the new yt-dlp command format
        cmd = ['python', '-m', 'yt_dlp', '--get-url', '--format', 'best[height<=480]', 'https://www.youtube.com/watch?v=dQw4w9WgXcQ']
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0 and result.stdout.strip():
            print("✅ YouTube URL extraction: WORKING")
            print(f"   URL length: {len(result.stdout.strip())} characters")
            return True
        else:
            print("❌ YouTube URL extraction: FAILED")
            print(f"   Error: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ YouTube URL extraction: ERROR - {e}")
        return False

def test_imports():
    """Test that core modules can be imported"""
    print("\n🧪 Testing module imports...")
    
    modules_to_test = [
        'dataset_builder',
        'live_bot', 
        'frame_selector',
        'startup_checker',
        'dependency_installer_gui'
    ]
    
    success_count = 0
    
    for module in modules_to_test:
        try:
            __import__(module)
            print(f"✅ {module}: OK")
            success_count += 1
        except Exception as e:
            print(f"❌ {module}: FAILED - {e}")
    
    return success_count == len(modules_to_test)

def test_ocr_manager_without_paddleocr():
    """Test that OCR manager works without PaddleOCR"""
    print("\n🧪 Testing OCR manager (without PaddleOCR)...")
    
    try:
        # Import without initializing EasyOCR (which takes time)
        import ocr_utils
        
        # Check that PaddleOCR is not in the engines
        manager = ocr_utils.OCRManager()
        available_engines = list(manager.engines.keys())
        
        if 'PaddleOCR' not in available_engines:
            print("✅ PaddleOCR successfully removed from engines")
            print(f"   Available engines: {available_engines}")
            return True
        else:
            print("❌ PaddleOCR still present in engines")
            return False
            
    except Exception as e:
        print(f"❌ OCR manager test: ERROR - {e}")
        return False

def test_requirements_file():
    """Test that requirements.txt doesn't contain PaddleOCR"""
    print("\n🧪 Testing requirements.txt...")
    
    try:
        with open('requirements.txt', 'r') as f:
            content = f.read().lower()
        
        if 'paddleocr' not in content and 'paddlepaddle' not in content:
            print("✅ PaddleOCR successfully removed from requirements.txt")
            return True
        else:
            print("❌ PaddleOCR still present in requirements.txt")
            return False
            
    except Exception as e:
        print(f"❌ Requirements file test: ERROR - {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 MFFUHijack Fixes Verification Test")
    print("=" * 50)
    
    tests = [
        ("YouTube URL Processing", test_youtube_url_processing),
        ("Module Imports", test_imports),
        ("OCR Manager (No PaddleOCR)", test_ocr_manager_without_paddleocr),
        ("Requirements File", test_requirements_file),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ {test_name}: EXCEPTION - {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All fixes verified successfully!")
        return True
    else:
        print("⚠️  Some tests failed. Check the output above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
