#!/usr/bin/env python3
"""
Test script for the improved two-step pattern detection logic
"""

import sys
from pathlib import Path

# Add current directory to path
sys.path.append(str(Path(__file__).parent))

from ocr_utils import ocr_manager


def test_improved_pattern_detection():
    """Test the new two-step pattern detection logic"""
    print("🧪 Testing Improved Pattern Detection Logic")
    print("=" * 60)
    print("New Logic:")
    print("  Step 1: Detect account type from keywords")
    print("  Step 2: Extract code after 'CODE:' keyword")
    print("=" * 60)
    
    # Test cases that should work with the new logic
    test_cases = [
        # Standard format cases
        {
            "text": "x5 FREE RESETS USE CODE: RESET3J",
            "expected_type": "Free Reset Code",
            "expected_code": "RESET3J"
        },
        {
            "text": "FREE 50 STARTER: ABC123",
            "expected_type": "Starter",
            "expected_code": "ABC123"
        },
        {
            "text": "STARTER PLUS ACCOUNT USE CODE: XYZ789",
            "expected_type": "Starter Plus", 
            "expected_code": "XYZ789"
        },
        {
            "text": "EXPERT CHALLENGE USE CODE: EXPERT99",
            "expected_type": "Expert",
            "expected_code": "EXPERT99"
        },
        
        # Variations in formatting
        {
            "text": "Get your FREE RESET with CODE: NEWBIE42",
            "expected_type": "Free Reset Code",
            "expected_code": "NEWBIE42"
        },
        {
            "text": "STARTER+ BONUS CODE: START55",
            "expected_type": "Starter Plus",
            "expected_code": "START55"
        },
        {
            "text": "Limited time EXPERT offer CODE: LIMIT99",
            "expected_type": "Expert",
            "expected_code": "LIMIT99"
        },
        
        # Edge cases
        {
            "text": "STARTER account available, use CODE: TEST123",
            "expected_type": "Starter",
            "expected_code": "TEST123"
        },
        {
            "text": "Multiple RESETS available CODE: MULTI88",
            "expected_type": "Free Reset Code",
            "expected_code": "MULTI88"
        },
        
        # Cases that should NOT match
        {
            "text": "This is just random text",
            "expected_type": None,
            "expected_code": None
        },
        {
            "text": "STARTER account but no code here",
            "expected_type": "Starter",
            "expected_code": None
        },
        {
            "text": "Random CODE: ABC123 without account type",
            "expected_type": None,
            "expected_code": "ABC123"
        }
    ]
    
    print(f"Running {len(test_cases)} test cases...\n")
    
    passed = 0
    failed = 0
    
    for i, test_case in enumerate(test_cases, 1):
        text = test_case["text"]
        expected_type = test_case["expected_type"]
        expected_code = test_case["expected_code"]
        
        print(f"Test {i}: '{text}'")
        
        # Test individual methods
        detected_type = ocr_manager.detect_account_type(text)
        extracted_code = ocr_manager.extract_code_after_keyword(text)
        
        # Test full pipeline
        mock_result = [{'text': text, 'confidence': 0.95}]
        codes = ocr_manager.find_giveaway_codes(mock_result)
        
        # Check results
        type_correct = detected_type == expected_type
        code_correct = extracted_code == expected_code
        
        if expected_type and expected_code:
            # Should find a complete match
            pipeline_correct = len(codes) == 1 and codes[0]['type'] == expected_type and codes[0]['code'] == expected_code
        else:
            # Should not find a complete match
            pipeline_correct = len(codes) == 0
        
        if type_correct and code_correct and pipeline_correct:
            print(f"   ✅ PASS")
            if codes:
                print(f"      → Found: {codes[0]['type']} code '{codes[0]['code']}'")
            else:
                print(f"      → Correctly found no complete match")
            passed += 1
        else:
            print(f"   ❌ FAIL")
            print(f"      Expected type: {expected_type}, Got: {detected_type}")
            print(f"      Expected code: {expected_code}, Got: {extracted_code}")
            print(f"      Pipeline result: {len(codes)} codes found")
            if codes:
                for code in codes:
                    print(f"        - {code['type']}: {code['code']}")
            failed += 1
        
        print()
    
    # Summary
    print("📊 Test Results Summary")
    print("=" * 30)
    print(f"✅ Passed: {passed}")
    print(f"❌ Failed: {failed}")
    print(f"📈 Success Rate: {passed/(passed+failed)*100:.1f}%")
    
    return passed, failed


def test_step_by_step_examples():
    """Show step-by-step examples of the new logic"""
    print("\n🔍 Step-by-Step Examples")
    print("=" * 40)
    
    examples = [
        "x5 FREE RESETS USE CODE: RESET3J",
        "FREE 50 STARTER: ABC123", 
        "STARTER+ BONUS CODE: START55",
        "EXPERT CHALLENGE USE CODE: EXPERT99"
    ]
    
    for example in examples:
        print(f"\nExample: '{example}'")
        print("-" * 50)
        
        # Step 1: Account type detection
        account_type = ocr_manager.detect_account_type(example)
        print(f"Step 1 - Account Type: {account_type}")
        
        # Step 2: Code extraction
        code = ocr_manager.extract_code_after_keyword(example)
        print(f"Step 2 - Code: {code}")
        
        # Combined result
        if account_type and code:
            print(f"Result: ✅ {account_type} code '{code}'")
        else:
            print(f"Result: ❌ Incomplete match")


def main():
    """Run all tests"""
    print("🚀 Improved Pattern Detection Test Suite")
    print("=" * 60)
    
    # Test the improved logic
    passed, failed = test_improved_pattern_detection()
    
    # Show step-by-step examples
    test_step_by_step_examples()
    
    # Final result
    if failed == 0:
        print("\n🎉 All tests passed! The improved pattern detection is working correctly.")
        return True
    else:
        print(f"\n⚠️  {failed} tests failed. Check the results above.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
