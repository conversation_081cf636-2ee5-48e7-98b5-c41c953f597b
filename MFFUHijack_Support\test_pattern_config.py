#!/usr/bin/env python3
"""
Test script for the new pattern configuration system
"""

import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))


def test_pattern_creation():
    """Test creating regex patterns from user input"""
    print("=" * 60)
    print("TESTING PATTERN CREATION")
    print("=" * 60)
    
    from ocr_utils import create_regex_from_patterns
    
    # Test patterns
    test_cases = [
        {
            "name": "Starter Patterns",
            "patterns": [
                "FREE {amount} STARTER: {code}",
                "{amount} STARTER: {code}",
                "GIVEAWAY {amount} STARTER: {code}"
            ],
            "test_texts": [
                "FREE 50 STARTER: ABC123",
                "100 STARTER: XYZ789",
                "GIVEAWAY 75 STARTER: DEF456",
                "This is not a match"
            ]
        },
        {
            "name": "Expert Patterns",
            "patterns": [
                "FREE {amount} EXPERT: {code}",
                "FREE {amount} EXPERT ACCOUNT: {code}"
            ],
            "test_texts": [
                "FREE 200 EXPERT: EXPERT123",
                "FREE 150 EXPERT ACCOUNT: ACCOUNT456",
                "FREE 50 STARTER: STARTER789"  # Should not match
            ]
        }
    ]
    
    for test_case in test_cases:
        print(f"\n--- {test_case['name']} ---")
        print(f"Patterns: {test_case['patterns']}")
        
        regex = create_regex_from_patterns(test_case['patterns'])
        print(f"Generated regex: {regex.pattern}")
        
        for text in test_case['test_texts']:
            matches = regex.findall(text)
            if matches:
                print(f"✅ '{text}' → {matches}")
            else:
                print(f"❌ '{text}' → No match")
    
    print()


def test_pattern_config_loading():
    """Test loading and saving pattern configuration"""
    print("=" * 60)
    print("TESTING PATTERN CONFIG LOADING")
    print("=" * 60)
    
    from ocr_utils import load_pattern_config, ocr_manager
    
    # Load default config
    config = load_pattern_config()
    print("Default configuration loaded:")
    for account_type, settings in config.items():
        enabled = settings.get('enabled', False)
        patterns = settings.get('patterns', [])
        print(f"  {account_type}: {'✅' if enabled else '❌'} ({len(patterns)} patterns)")
    
    print(f"\nEnabled account types: {ocr_manager.get_enabled_account_types()}")
    print()


def test_code_detection():
    """Test the complete code detection pipeline"""
    print("=" * 60)
    print("TESTING CODE DETECTION PIPELINE")
    print("=" * 60)
    
    from ocr_utils import ocr_manager
    
    # Test texts with different account types
    test_texts = [
        "FREE 50 STARTER: ABC123",
        "FREE 100 STARTER PLUS: XYZ789",
        "FREE 200 EXPERT: DEF456",
        "GIVEAWAY 75 STARTER: GHI012",
        "100 EXPERT ACCOUNT: JKL345",
        "This is not a valid code",
        "FREE 25 STARTER+: MNO678"
    ]
    
    print("Testing code detection with current patterns...")
    print()
    
    for text in test_texts:
        # Simulate OCR result
        mock_ocr_result = [{'text': text, 'confidence': 0.95, 'bbox': []}]
        
        # Detect codes
        codes = ocr_manager.find_giveaway_codes(mock_ocr_result)
        
        if codes:
            for code_data in codes:
                print(f"🎯 DETECTED in '{text}':")
                print(f"   Code: {code_data['code']}")
                print(f"   Type: {code_data['type']}")
                print(f"   Amount: ${code_data.get('amount', 'Unknown')}")
                print(f"   Confidence: {code_data['confidence']:.2f}")
        else:
            print(f"❌ No codes detected in '{text}'")
        print()


def test_gui_dialog():
    """Test the pattern configuration GUI dialog"""
    print("=" * 60)
    print("TESTING PATTERN CONFIG GUI")
    print("=" * 60)
    
    try:
        from PyQt6.QtWidgets import QApplication
        from pattern_config_dialog import PatternConfigDialog
        
        # Create QApplication if needed
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        print("✅ Pattern configuration dialog can be imported")
        print("✅ PyQt6 components available")
        
        # Test dialog creation (don't show it)
        dialog = PatternConfigDialog()
        print("✅ Dialog created successfully")
        
        # Test pattern loading
        patterns = dialog.load_patterns()
        print(f"✅ Patterns loaded: {len(patterns)} account types")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def main():
    """Run all pattern configuration tests"""
    print("🧪 MFFUHijack Pattern Configuration Test")
    print("=" * 60)
    
    tests = [
        ("Pattern Creation", test_pattern_creation),
        ("Config Loading", test_pattern_config_loading),
        ("Code Detection", test_code_detection),
        ("GUI Dialog", test_gui_dialog),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 Running {test_name} test...")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}: PASSED")
            else:
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            print(f"❌ {test_name}: ERROR - {e}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 60)
    print(f"📊 PATTERN CONFIG TEST RESULTS: {passed}/{total} passed")
    print("=" * 60)
    
    if passed == total:
        print("🎉 All pattern configuration tests passed!")
        print("\nYou can now:")
        print("• Use custom account type patterns")
        print("• Configure detection patterns via GUI")
        print("• Add multiple text patterns per account type")
        print("• Enable/disable specific account types")
    else:
        print("⚠️  Some pattern configuration tests failed.")
        print("Check the errors above for details.")
    
    print("=" * 60)
    
    return passed == total


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\nTest interrupted by user")
    except Exception as e:
        print(f"\nTest failed with error: {e}")
        import traceback
        traceback.print_exc()
