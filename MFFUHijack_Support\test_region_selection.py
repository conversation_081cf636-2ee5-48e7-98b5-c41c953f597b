#!/usr/bin/env python3
"""
Test script for the new region selection system
"""

import sys
import os
import numpy as np
import cv2

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))


def test_region_selector_import():
    """Test importing region selector components"""
    print("=" * 60)
    print("TESTING REGION SELECTOR IMPORTS")
    print("=" * 60)
    
    try:
        from region_selector import RegionSelectorWidget, SelectableImageLabel
        print("✅ RegionSelectorWidget imported successfully")
        
        from frame_selector import FrameSelectorWidget, VideoFrameExtractor
        print("✅ FrameSelectorWidget imported successfully")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def test_region_cropping():
    """Test the updated region cropping functionality"""
    print("\n" + "=" * 60)
    print("TESTING REGION CROPPING")
    print("=" * 60)
    
    try:
        from ocr_utils import ocr_manager
        
        # Create test image
        test_image = np.ones((480, 640, 3), dtype=np.uint8) * 255
        cv2.putText(test_image, "TOP AREA", (250, 100), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
        cv2.putText(test_image, "MIDDLE AREA", (250, 240), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
        cv2.putText(test_image, "FREE 50 STARTER: ABC123", (150, 400), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 200), 2)
        
        print(f"Original image size: {test_image.shape}")
        
        # Test different region types
        test_regions = [
            ("bottom_third", "bottom_third"),
            ("bottom_center", "bottom_center"),
            ("full", "full"),
            ("custom_bottom_third", (0, 67, 100, 33)),  # x%, y%, w%, h%
            ("custom_center", (25, 25, 50, 50)),
            ("custom_bottom_half", (0, 50, 100, 50))
        ]
        
        for region_name, region_param in test_regions:
            cropped = ocr_manager.crop_region_of_interest(test_image, region_param)
            print(f"✅ {region_name}: {cropped.shape}")
            
            # Save cropped image for inspection
            filename = f"test_crop_{region_name}.jpg"
            cv2.imwrite(filename, cropped)
            print(f"   Saved: {filename}")
        
        return True
        
    except Exception as e:
        print(f"❌ Region cropping test failed: {e}")
        return False


def test_region_percentage_conversion():
    """Test percentage to pixel conversion"""
    print("\n" + "=" * 60)
    print("TESTING PERCENTAGE CONVERSION")
    print("=" * 60)
    
    try:
        from region_selector import RegionSelectorWidget
        
        # Test conversions
        test_cases = [
            ((0, 0, 100, 100), (640, 480)),      # Full image
            ((25, 25, 50, 50), (640, 480)),      # Center quarter
            ((0, 67, 100, 33), (640, 480)),      # Bottom third
            ((25, 67, 50, 33), (1920, 1080)),    # Bottom center HD
        ]
        
        widget = RegionSelectorWidget()
        
        for (x_p, y_p, w_p, h_p), (img_w, img_h) in test_cases:
            x, y, w, h = widget.get_region_pixels(img_w, img_h)
            print(f"✅ {x_p}%, {y_p}%, {w_p}%, {h_p}% on {img_w}×{img_h} → {x}, {y}, {w}, {h}")
        
        return True
        
    except Exception as e:
        print(f"❌ Percentage conversion test failed: {e}")
        return False


def test_gui_integration():
    """Test GUI integration with region selection"""
    print("\n" + "=" * 60)
    print("TESTING GUI INTEGRATION")
    print("=" * 60)
    
    try:
        from PyQt6.QtWidgets import QApplication
        from gui import LivestreamTab, DatasetTab
        
        # Create QApplication if needed
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # Test livestream tab
        livestream_tab = LivestreamTab()
        print("✅ LivestreamTab created with region support")
        print(f"   Default OCR region: {livestream_tab.ocr_region}")
        
        # Test dataset tab
        dataset_tab = DatasetTab()
        print("✅ DatasetTab created with frame/region support")
        print(f"   Default dataset region: {dataset_tab.dataset_region}")
        
        return True
        
    except Exception as e:
        print(f"❌ GUI integration test failed: {e}")
        return False


def test_ocr_with_custom_region():
    """Test OCR processing with custom regions"""
    print("\n" + "=" * 60)
    print("TESTING OCR WITH CUSTOM REGIONS")
    print("=" * 60)
    
    try:
        from ocr_utils import ocr_manager
        
        # Create test image with code in specific location
        test_image = np.ones((480, 640, 3), dtype=np.uint8) * 255
        
        # Add noise in top area
        cv2.putText(test_image, "NOISE TEXT HERE", (50, 100), cv2.FONT_HERSHEY_SIMPLEX, 1, (100, 100, 100), 2)
        cv2.putText(test_image, "MORE NOISE", (300, 150), cv2.FONT_HERSHEY_SIMPLEX, 1, (100, 100, 100), 2)
        
        # Add target code in bottom area
        cv2.putText(test_image, "FREE 50 STARTER: TEST123", (150, 400), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 200), 2)
        
        # Test with different regions
        regions_to_test = [
            ("full_image", "full"),
            ("bottom_third", "bottom_third"),
            ("custom_bottom", (0, 67, 100, 33)),
            ("custom_target_area", (20, 75, 60, 20))  # Focused on code area
        ]
        
        for region_name, region_param in regions_to_test:
            print(f"\nTesting region: {region_name}")
            
            # Crop to region
            cropped = ocr_manager.crop_region_of_interest(test_image, region_param)
            print(f"  Cropped size: {cropped.shape}")
            
            # Process with OCR
            codes = ocr_manager.process_frame_for_codes(cropped, region="full")  # Already cropped
            
            if codes:
                for code_data in codes:
                    print(f"  ✅ Found: {code_data['code']} (Type: {code_data['type']}, Confidence: {code_data['confidence']:.2f})")
            else:
                print(f"  ❌ No codes detected")
        
        return True
        
    except Exception as e:
        print(f"❌ OCR with custom region test failed: {e}")
        return False


def main():
    """Run all region selection tests"""
    print("🧪 MFFUHijack Region Selection Test")
    print("=" * 60)
    
    tests = [
        ("Region Selector Import", test_region_selector_import),
        ("Region Cropping", test_region_cropping),
        ("Percentage Conversion", test_region_percentage_conversion),
        ("GUI Integration", test_gui_integration),
        ("OCR with Custom Regions", test_ocr_with_custom_region),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"\n✅ {test_name}: PASSED")
            else:
                print(f"\n❌ {test_name}: FAILED")
        except Exception as e:
            print(f"\n❌ {test_name}: ERROR - {e}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 60)
    print(f"📊 REGION SELECTION TEST RESULTS: {passed}/{total} passed")
    print("=" * 60)
    
    if passed == total:
        print("🎉 All region selection tests passed!")
        print("\nNew features available:")
        print("• ✅ Resizable rectangular OCR region selection")
        print("• ✅ Live stream region selector with preview")
        print("• ✅ Frame selector with time slider for dataset building")
        print("• ✅ Custom region support in OCR processing")
        print("• ✅ Default MyFundedFutures channel URL")
        print("• ✅ Enhanced GUI with region configuration buttons")
    else:
        print("⚠️  Some region selection tests failed.")
        print("Check the errors above for details.")
    
    # Cleanup test files
    test_files = [f for f in os.listdir('.') if f.startswith('test_crop_') and f.endswith('.jpg')]
    for file in test_files:
        try:
            os.remove(file)
            print(f"Cleaned up: {file}")
        except:
            pass
    
    print("=" * 60)
    
    return passed == total


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\nTest interrupted by user")
    except Exception as e:
        print(f"\nTest failed with error: {e}")
        import traceback
        traceback.print_exc()
