"""
OCR Model Training System for MFFUHijack
Trains custom OCR models using collected dataset
"""

import os
import cv2
import numpy as np
import json
import time
from datetime import datetime
from typing import List, Dict, Any, Tuple, Optional
import logging
from pathlib import Path

from PyQt6.QtCore import QThread, pyqtSignal

# Machine Learning imports
try:
    import torch
    import torch.nn as nn
    import torch.optim as optim
    from torch.utils.data import Dataset, DataLoader
    import torchvision.transforms as transforms
    from PIL import Image
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class OCRDataset(Dataset):
    """Custom dataset for OCR training"""
    
    def __init__(self, frames_dir: str, log_file: str, transform=None):
        self.frames_dir = frames_dir
        self.transform = transform
        self.samples = []
        
        # Load training data from log file
        self.load_samples(log_file)
    
    def load_samples(self, log_file: str):
        """Load training samples from log file"""
        if not os.path.exists(log_file):
            logger.warning(f"Log file not found: {log_file}")
            return
        
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                for line in f:
                    if line.startswith('#') or not line.strip():
                        continue
                    
                    parts = line.strip().split(',')
                    if len(parts) >= 5:
                        timestamp, filename, video_title, video_url, code = parts[:5]
                        confidence = float(parts[5]) if len(parts) > 5 else 1.0
                        
                        # Check if frame file exists
                        frame_path = os.path.join(self.frames_dir, filename)
                        if os.path.exists(frame_path):
                            self.samples.append({
                                'image_path': frame_path,
                                'text': code.strip(),
                                'confidence': confidence
                            })
        
        except Exception as e:
            logger.error(f"Error loading samples: {e}")
        
        logger.info(f"Loaded {len(self.samples)} training samples")
    
    def __len__(self):
        return len(self.samples)
    
    def __getitem__(self, idx):
        sample = self.samples[idx]
        
        # Load image
        try:
            image = Image.open(sample['image_path']).convert('RGB')
            if self.transform:
                image = self.transform(image)
        except Exception as e:
            logger.error(f"Error loading image {sample['image_path']}: {e}")
            # Return a blank image if loading fails
            image = Image.new('RGB', (224, 224), color='white')
            if self.transform:
                image = self.transform(image)
        
        return {
            'image': image,
            'text': sample['text'],
            'confidence': sample['confidence']
        }


class SimpleOCRModel(nn.Module):
    """Simple CNN-based OCR model for code recognition"""
    
    def __init__(self, vocab_size: int = 37, max_length: int = 20):
        super(SimpleOCRModel, self).__init__()
        
        # Character vocabulary: A-Z, 0-9, -
        self.vocab_size = vocab_size
        self.max_length = max_length
        
        # CNN feature extractor
        self.features = nn.Sequential(
            nn.Conv2d(3, 32, 3, padding=1),
            nn.ReLU(),
            nn.MaxPool2d(2),
            nn.Conv2d(32, 64, 3, padding=1),
            nn.ReLU(),
            nn.MaxPool2d(2),
            nn.Conv2d(64, 128, 3, padding=1),
            nn.ReLU(),
            nn.MaxPool2d(2),
            nn.AdaptiveAvgPool2d((8, 8))
        )
        
        # Classifier
        self.classifier = nn.Sequential(
            nn.Flatten(),
            nn.Linear(128 * 8 * 8, 512),
            nn.ReLU(),
            nn.Dropout(0.5),
            nn.Linear(512, vocab_size * max_length)
        )
    
    def forward(self, x):
        features = self.features(x)
        output = self.classifier(features)
        # Reshape to (batch_size, max_length, vocab_size)
        output = output.view(-1, self.max_length, self.vocab_size)
        return output


class ModelTrainerWorker(QThread):
    """Worker thread for model training"""
    
    # Signals
    progress_update = pyqtSignal(int)      # Progress percentage
    status_update = pyqtSignal(str)        # Status message
    loss_update = pyqtSignal(float)        # Training loss
    error_occurred = pyqtSignal(str)       # Error message
    training_complete = pyqtSignal(str)    # Model save path
    
    def __init__(self):
        super().__init__()
        self.is_running = False
        
        # Paths
        self.frames_dir = "ocr_training_data/frames"
        self.log_file = "ocr_training_data/log.txt"
        self.model_save_path = "ocr_models/custom_model.pth"
        
        # Training parameters
        self.epochs = 50
        self.batch_size = 8
        self.learning_rate = 0.001
        
        # Character mapping
        self.char_to_idx = {}
        self.idx_to_char = {}
        self.setup_vocabulary()
    
    def setup_vocabulary(self):
        """Setup character vocabulary for training"""
        # A-Z, 0-9, -, space, padding
        chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789- <PAD>'
        
        for i, char in enumerate(chars):
            self.char_to_idx[char] = i
            self.idx_to_char[i] = char
    
    def text_to_indices(self, text: str, max_length: int = 20) -> List[int]:
        """Convert text to list of character indices"""
        indices = []
        text = text.upper()[:max_length]  # Truncate if too long
        
        for char in text:
            if char in self.char_to_idx:
                indices.append(self.char_to_idx[char])
            else:
                indices.append(self.char_to_idx[' '])  # Unknown char -> space
        
        # Pad to max_length
        while len(indices) < max_length:
            indices.append(self.char_to_idx['<PAD>'])
        
        return indices
    
    def indices_to_text(self, indices: List[int]) -> str:
        """Convert list of indices back to text"""
        text = ""
        for idx in indices:
            if idx in self.idx_to_char:
                char = self.idx_to_char[idx]
                if char == '<PAD>':
                    break
                text += char
        return text.strip()
    
    def run(self):
        """Main training process"""
        if not TORCH_AVAILABLE:
            self.error_occurred.emit("PyTorch not available. Please install torch and torchvision.")
            return
        
        self.is_running = True
        
        try:
            # Check if we have training data
            if not os.path.exists(self.frames_dir) or not os.path.exists(self.log_file):
                self.error_occurred.emit("No training data found. Please run dataset builder first.")
                return
            
            self.status_update.emit("Loading training data...")
            
            # Create dataset
            transform = transforms.Compose([
                transforms.Resize((224, 224)),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], 
                                   std=[0.229, 0.224, 0.225])
            ])
            
            dataset = OCRDataset(self.frames_dir, self.log_file, transform=transform)
            
            if len(dataset) == 0:
                self.error_occurred.emit("No valid training samples found.")
                return
            
            # Create data loader
            dataloader = DataLoader(dataset, batch_size=self.batch_size, shuffle=True)
            
            self.status_update.emit(f"Training on {len(dataset)} samples...")
            
            # Create model
            model = SimpleOCRModel(vocab_size=len(self.char_to_idx), max_length=20)
            
            # Setup training
            device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
            model.to(device)
            
            criterion = nn.CrossEntropyLoss(ignore_index=self.char_to_idx['<PAD>'])
            optimizer = optim.Adam(model.parameters(), lr=self.learning_rate)
            
            self.status_update.emit(f"Training on {device}")
            
            # Training loop
            model.train()
            total_batches = len(dataloader) * self.epochs
            batch_count = 0
            
            for epoch in range(self.epochs):
                if not self.is_running:
                    break
                
                epoch_loss = 0.0
                
                for batch_idx, batch in enumerate(dataloader):
                    if not self.is_running:
                        break
                    
                    # Prepare batch
                    images = batch['image'].to(device)
                    texts = batch['text']
                    
                    # Convert texts to target indices
                    targets = []
                    for text in texts:
                        indices = self.text_to_indices(text)
                        targets.append(indices)
                    
                    targets = torch.tensor(targets, dtype=torch.long).to(device)
                    
                    # Forward pass
                    optimizer.zero_grad()
                    outputs = model(images)
                    
                    # Reshape for loss calculation
                    outputs = outputs.view(-1, len(self.char_to_idx))
                    targets = targets.view(-1)
                    
                    loss = criterion(outputs, targets)
                    
                    # Backward pass
                    loss.backward()
                    optimizer.step()
                    
                    epoch_loss += loss.item()
                    batch_count += 1
                    
                    # Update progress
                    progress = int((batch_count / total_batches) * 100)
                    self.progress_update.emit(progress)
                    
                    if batch_idx % 5 == 0:
                        self.loss_update.emit(loss.item())
                
                avg_loss = epoch_loss / len(dataloader)
                self.status_update.emit(f"Epoch {epoch+1}/{self.epochs}, Loss: {avg_loss:.4f}")
            
            if self.is_running:
                # Save model
                self.status_update.emit("Saving model...")
                os.makedirs(os.path.dirname(self.model_save_path), exist_ok=True)
                
                # Save model state and metadata
                save_dict = {
                    'model_state_dict': model.state_dict(),
                    'char_to_idx': self.char_to_idx,
                    'idx_to_char': self.idx_to_char,
                    'vocab_size': len(self.char_to_idx),
                    'max_length': 20,
                    'training_samples': len(dataset),
                    'epochs': self.epochs,
                    'final_loss': avg_loss,
                    'timestamp': datetime.now().isoformat()
                }
                
                torch.save(save_dict, self.model_save_path)
                
                self.status_update.emit("Training completed successfully!")
                self.training_complete.emit(self.model_save_path)
        
        except Exception as e:
            logger.error(f"Training error: {e}")
            self.error_occurred.emit(f"Training failed: {str(e)}")
    
    def stop(self):
        """Stop training"""
        self.is_running = False


class ModelTrainer:
    """Main model trainer controller"""
    
    def __init__(self):
        self.worker = None
        self.progress_callback = None
        self.status_callback = None
        self.loss_callback = None
        self.error_callback = None
        self.complete_callback = None
    
    def set_callbacks(self, progress_cb=None, status_cb=None, loss_cb=None,
                     error_cb=None, complete_cb=None):
        """Set callback functions for training events"""
        self.progress_callback = progress_cb
        self.status_callback = status_cb
        self.loss_callback = loss_cb
        self.error_callback = error_cb
        self.complete_callback = complete_cb
    
    def start_training(self):
        """Start model training"""
        if self.worker and self.worker.isRunning():
            logger.warning("Model training is already running")
            return False
        
        # Create new worker
        self.worker = ModelTrainerWorker()
        
        # Connect signals
        if self.progress_callback:
            self.worker.progress_update.connect(self.progress_callback)
        if self.status_callback:
            self.worker.status_update.connect(self.status_callback)
        if self.loss_callback:
            self.worker.loss_update.connect(self.loss_callback)
        if self.error_callback:
            self.worker.error_occurred.connect(self.error_callback)
        if self.complete_callback:
            self.worker.training_complete.connect(self.complete_callback)
        
        # Start worker
        self.worker.start()
        logger.info("Started model training")
        return True
    
    def stop_training(self):
        """Stop model training"""
        if self.worker:
            self.worker.stop()
            self.worker.wait()
            self.worker = None
            logger.info("Stopped model training")
    
    def is_training(self) -> bool:
        """Check if training is currently running"""
        return self.worker is not None and self.worker.isRunning()
    
    def get_model_info(self) -> Optional[Dict[str, Any]]:
        """Get information about the trained model"""
        model_path = "ocr_models/custom_model.pth"
        
        if not os.path.exists(model_path):
            return None
        
        try:
            if TORCH_AVAILABLE:
                model_data = torch.load(model_path, map_location='cpu')
                return {
                    'path': model_path,
                    'training_samples': model_data.get('training_samples', 0),
                    'epochs': model_data.get('epochs', 0),
                    'final_loss': model_data.get('final_loss', 0.0),
                    'timestamp': model_data.get('timestamp', 'Unknown'),
                    'vocab_size': model_data.get('vocab_size', 0)
                }
            else:
                # Basic file info if torch not available
                stat = os.stat(model_path)
                return {
                    'path': model_path,
                    'size_mb': stat.st_size / (1024 * 1024),
                    'modified': datetime.fromtimestamp(stat.st_mtime).isoformat()
                }
        
        except Exception as e:
            logger.error(f"Error getting model info: {e}")
            return None
