#!/usr/bin/env python3
"""
Comprehensive Test Session Monitor for MFFUHijack GUI Testing
Monitors all components, logs activities, and watches for issues
"""

import sys
import os
import time
import json
import traceback
from datetime import datetime
from typing import Dict, List, Any
from PyQt6.QtWidgets import *
from PyQt6.QtCore import *
from PyQt6.QtGui import *

# Import all GUI components for testing
try:
    from live_scan_monitor import LiveScanMonitorWindow
    LIVE_MONITOR_AVAILABLE = True
except ImportError as e:
    print(f"❌ Live Monitor not available: {e}")
    LIVE_MONITOR_AVAILABLE = False

try:
    from livestream_testing_gui import LiveStreamTestingWindow
    TESTING_MODE_AVAILABLE = True
except ImportError as e:
    print(f"❌ Testing Mode not available: {e}")
    TESTING_MODE_AVAILABLE = False

try:
    from smart_features import SmartFeaturesManager
    SMART_FEATURES_AVAILABLE = True
except ImportError as e:
    print(f"❌ Smart Features not available: {e}")
    SMART_FEATURES_AVAILABLE = False

try:
    from temporal_analytics_dashboard import TemporalAnalyticsDashboard
    TEMPORAL_DASHBOARD_AVAILABLE = True
except ImportError as e:
    print(f"❌ Temporal Dashboard not available: {e}")
    TEMPORAL_DASHBOARD_AVAILABLE = False


class TestSessionLogger:
    """Comprehensive logging system for test session"""
    
    def __init__(self):
        self.session_start = datetime.now()
        self.log_entries = []
        self.error_count = 0
        self.warning_count = 0
        self.info_count = 0
        self.component_status = {}
        
        # Create logs directory
        os.makedirs("test_logs", exist_ok=True)
        
        # Setup log file
        timestamp = self.session_start.strftime("%Y%m%d_%H%M%S")
        self.log_file = f"test_logs/test_session_{timestamp}.log"
        
        self.log("INFO", "Test session started", {"timestamp": self.session_start.isoformat()})
    
    def log(self, level: str, message: str, details: Dict = None):
        """Log an entry with timestamp and details"""
        timestamp = datetime.now()
        
        entry = {
            "timestamp": timestamp.isoformat(),
            "level": level,
            "message": message,
            "details": details or {}
        }
        
        self.log_entries.append(entry)
        
        # Update counters
        if level == "ERROR":
            self.error_count += 1
        elif level == "WARNING":
            self.warning_count += 1
        elif level == "INFO":
            self.info_count += 1
        
        # Write to file
        try:
            with open(self.log_file, "a", encoding="utf-8") as f:
                log_line = f"[{timestamp.strftime('%H:%M:%S')}] {level}: {message}"
                if details:
                    log_line += f" | Details: {json.dumps(details, default=str)}"
                f.write(log_line + "\n")
        except Exception as e:
            print(f"Failed to write log: {e}")
        
        # Print to console with color coding
        colors = {
            "ERROR": "\033[91m",    # Red
            "WARNING": "\033[93m",  # Yellow
            "INFO": "\033[92m",     # Green
            "DEBUG": "\033[94m"     # Blue
        }
        reset_color = "\033[0m"
        
        color = colors.get(level, "")
        print(f"{color}[{timestamp.strftime('%H:%M:%S')}] {level}: {message}{reset_color}")
        
        if details:
            print(f"  Details: {details}")
    
    def log_component_status(self, component: str, status: str, details: Dict = None):
        """Log component status"""
        self.component_status[component] = {
            "status": status,
            "timestamp": datetime.now().isoformat(),
            "details": details or {}
        }
        
        self.log("INFO", f"Component {component}: {status}", details)
    
    def log_error(self, component: str, error: Exception, context: str = ""):
        """Log an error with full traceback"""
        error_details = {
            "component": component,
            "error_type": type(error).__name__,
            "error_message": str(error),
            "context": context,
            "traceback": traceback.format_exc()
        }
        
        self.log("ERROR", f"Error in {component}: {str(error)}", error_details)
    
    def get_session_summary(self) -> Dict:
        """Get comprehensive session summary"""
        duration = datetime.now() - self.session_start
        
        return {
            "session_duration": str(duration).split('.')[0],
            "total_entries": len(self.log_entries),
            "error_count": self.error_count,
            "warning_count": self.warning_count,
            "info_count": self.info_count,
            "component_status": self.component_status,
            "log_file": self.log_file
        }


class ComponentTester:
    """Tests individual GUI components"""
    
    def __init__(self, logger: TestSessionLogger):
        self.logger = logger
        self.test_results = {}
    
    def test_live_monitor(self) -> bool:
        """Test Live Scan Monitor component"""
        try:
            self.logger.log("INFO", "Testing Live Scan Monitor component")
            
            if not LIVE_MONITOR_AVAILABLE:
                self.logger.log("WARNING", "Live Monitor not available for testing")
                return False
            
            # Create monitor window
            monitor = LiveScanMonitorWindow()
            self.logger.log("INFO", "Live Monitor window created successfully")
            
            # Test basic functionality
            monitor.show()
            self.logger.log("INFO", "Live Monitor window displayed")
            
            # Test smart features integration
            if hasattr(monitor, 'smart_features') and monitor.smart_features:
                self.logger.log("INFO", "Smart features integration confirmed")
            else:
                self.logger.log("WARNING", "Smart features not integrated")
            
            # Test menu system
            menubar = monitor.menuBar()
            if menubar:
                self.logger.log("INFO", "Menu bar accessible")
                
                # Check for testing mode menu
                actions = []
                for action in menubar.actions():
                    menu = action.menu()
                    if menu:
                        for sub_action in menu.actions():
                            actions.append(sub_action.text())
                
                if any("Testing Mode" in action for action in actions):
                    self.logger.log("INFO", "Testing Mode menu item found")
                else:
                    self.logger.log("WARNING", "Testing Mode menu item not found")
            
            monitor.close()
            self.logger.log("INFO", "Live Monitor test completed successfully")
            return True
            
        except Exception as e:
            self.logger.log_error("LiveMonitor", e, "Component testing")
            return False
    
    def test_testing_mode(self) -> bool:
        """Test Live Stream Testing Mode component"""
        try:
            self.logger.log("INFO", "Testing Live Stream Testing Mode component")
            
            if not TESTING_MODE_AVAILABLE:
                self.logger.log("WARNING", "Testing Mode not available for testing")
                return False
            
            # Create testing window
            testing_window = LiveStreamTestingWindow()
            self.logger.log("INFO", "Testing Mode window created successfully")
            
            # Test basic functionality
            testing_window.show()
            self.logger.log("INFO", "Testing Mode window displayed")
            
            # Test configuration controls
            if hasattr(testing_window, 'interval_spin'):
                self.logger.log("INFO", "Frame interval control accessible")
            else:
                self.logger.log("WARNING", "Frame interval control not found")
            
            if hasattr(testing_window, 'ocr_combo'):
                self.logger.log("INFO", "OCR engine selector accessible")
            else:
                self.logger.log("WARNING", "OCR engine selector not found")
            
            # Test stream list
            if hasattr(testing_window, 'stream_list'):
                self.logger.log("INFO", "Stream list widget accessible")
            else:
                self.logger.log("WARNING", "Stream list widget not found")
            
            testing_window.close()
            self.logger.log("INFO", "Testing Mode test completed successfully")
            return True
            
        except Exception as e:
            self.logger.log_error("TestingMode", e, "Component testing")
            return False
    
    def test_smart_features(self) -> bool:
        """Test Smart Features component"""
        try:
            self.logger.log("INFO", "Testing Smart Features component")
            
            if not SMART_FEATURES_AVAILABLE:
                self.logger.log("WARNING", "Smart Features not available for testing")
                return False
            
            # Create smart features manager
            smart_manager = SmartFeaturesManager()
            self.logger.log("INFO", "Smart Features Manager created successfully")
            
            # Test session start
            smart_manager.start_session("test_session_001")
            self.logger.log("INFO", "Smart Features session started")
            
            # Test code processing
            test_code_data = {
                "code": "TEST123",
                "type": "Starter",
                "confidence": 0.85,
                "full_text": "FREE STARTER: TEST123"
            }
            
            processed_data = smart_manager.process_code_detection(test_code_data)
            if processed_data:
                self.logger.log("INFO", "Code processing successful", {
                    "original_code": test_code_data["code"],
                    "processed": bool(processed_data.get("smart_features_processed"))
                })
            else:
                self.logger.log("WARNING", "Code processing returned no data")
            
            # Test analytics
            analytics = smart_manager.get_comprehensive_analytics()
            if analytics:
                self.logger.log("INFO", "Analytics generation successful", {
                    "analytics_keys": list(analytics.keys())
                })
            else:
                self.logger.log("WARNING", "Analytics generation failed")
            
            self.logger.log("INFO", "Smart Features test completed successfully")
            return True
            
        except Exception as e:
            self.logger.log_error("SmartFeatures", e, "Component testing")
            return False
    
    def test_temporal_dashboard(self) -> bool:
        """Test Temporal Analytics Dashboard component"""
        try:
            self.logger.log("INFO", "Testing Temporal Analytics Dashboard component")
            
            if not TEMPORAL_DASHBOARD_AVAILABLE:
                self.logger.log("WARNING", "Temporal Dashboard not available for testing")
                return False
            
            # Create dashboard
            dashboard = TemporalAnalyticsDashboard()
            self.logger.log("INFO", "Temporal Dashboard created successfully")
            
            # Test basic functionality
            dashboard.show()
            self.logger.log("INFO", "Temporal Dashboard displayed")
            
            # Test adding detection
            test_timestamp = datetime.now()
            dashboard.add_detection(test_timestamp, "Starter", True)
            self.logger.log("INFO", "Test detection added to dashboard")
            
            dashboard.close()
            self.logger.log("INFO", "Temporal Dashboard test completed successfully")
            return True
            
        except Exception as e:
            self.logger.log_error("TemporalDashboard", e, "Component testing")
            return False


class TestSessionMonitor(QMainWindow):
    """Main test session monitoring window"""
    
    def __init__(self):
        super().__init__()
        self.logger = TestSessionLogger()
        self.component_tester = ComponentTester(self.logger)
        self.test_windows = {}
        
        self.init_ui()
        self.run_initial_tests()
        
        # Setup monitoring timer
        self.monitor_timer = QTimer()
        self.monitor_timer.timeout.connect(self.update_monitoring)
        self.monitor_timer.start(1000)  # Update every second
    
    def init_ui(self):
        """Initialize monitoring UI"""
        self.setWindowTitle("🧪 MFFUHijack GUI Test Session Monitor")
        self.setGeometry(100, 100, 1000, 700)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # Header
        header_layout = QHBoxLayout()
        
        title_label = QLabel("🧪 GUI Test Session Monitor")
        title_label.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        
        # Session info
        self.session_info_label = QLabel("Session: Starting...")
        header_layout.addWidget(self.session_info_label)
        
        layout.addLayout(header_layout)
        
        # Main content
        content_splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # Left panel - Controls
        left_panel = self.create_control_panel()
        content_splitter.addWidget(left_panel)
        
        # Right panel - Monitoring
        right_panel = self.create_monitoring_panel()
        content_splitter.addWidget(right_panel)
        
        content_splitter.setSizes([300, 700])
        layout.addWidget(content_splitter)
        
        # Status bar
        self.status_bar = self.statusBar()
        self.status_bar.showMessage("Test session monitoring active")
    
    def create_control_panel(self):
        """Create control panel"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # Component Testing
        testing_group = QGroupBox("🧪 Component Testing")
        testing_layout = QVBoxLayout(testing_group)
        
        test_live_btn = QPushButton("🔍 Test Live Monitor")
        test_live_btn.clicked.connect(self.test_live_monitor)
        testing_layout.addWidget(test_live_btn)
        
        test_testing_btn = QPushButton("🧪 Test Testing Mode")
        test_testing_btn.clicked.connect(self.test_testing_mode)
        testing_layout.addWidget(test_testing_btn)
        
        test_smart_btn = QPushButton("🧠 Test Smart Features")
        test_smart_btn.clicked.connect(self.test_smart_features)
        testing_layout.addWidget(test_smart_btn)
        
        test_temporal_btn = QPushButton("📊 Test Temporal Dashboard")
        test_temporal_btn.clicked.connect(self.test_temporal_dashboard)
        testing_layout.addWidget(test_temporal_btn)
        
        testing_layout.addWidget(QLabel(""))  # Spacer
        
        test_all_btn = QPushButton("🚀 Test All Components")
        test_all_btn.clicked.connect(self.test_all_components)
        test_all_btn.setStyleSheet("font-weight: bold; background-color: #4CAF50; color: white;")
        testing_layout.addWidget(test_all_btn)
        
        layout.addWidget(testing_group)
        
        # Manual Testing
        manual_group = QGroupBox("👤 Manual Testing")
        manual_layout = QVBoxLayout(manual_group)
        
        open_live_btn = QPushButton("🔍 Open Live Monitor")
        open_live_btn.clicked.connect(self.open_live_monitor)
        manual_layout.addWidget(open_live_btn)
        
        open_testing_btn = QPushButton("🧪 Open Testing Mode")
        open_testing_btn.clicked.connect(self.open_testing_mode)
        manual_layout.addWidget(open_testing_btn)
        
        open_temporal_btn = QPushButton("📊 Open Temporal Dashboard")
        open_temporal_btn.clicked.connect(self.open_temporal_dashboard)
        manual_layout.addWidget(open_temporal_btn)
        
        layout.addWidget(manual_group)
        
        # Session Control
        session_group = QGroupBox("📋 Session Control")
        session_layout = QVBoxLayout(session_group)
        
        export_logs_btn = QPushButton("💾 Export Logs")
        export_logs_btn.clicked.connect(self.export_logs)
        session_layout.addWidget(export_logs_btn)
        
        clear_logs_btn = QPushButton("🗑️ Clear Logs")
        clear_logs_btn.clicked.connect(self.clear_logs)
        session_layout.addWidget(clear_logs_btn)
        
        session_summary_btn = QPushButton("📊 Session Summary")
        session_summary_btn.clicked.connect(self.show_session_summary)
        session_layout.addWidget(session_summary_btn)
        
        layout.addWidget(session_group)
        
        layout.addStretch()
        
        return panel
    
    def create_monitoring_panel(self):
        """Create monitoring panel"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # Statistics
        stats_group = QGroupBox("📈 Session Statistics")
        stats_layout = QGridLayout(stats_group)
        
        stats_layout.addWidget(QLabel("Errors:"), 0, 0)
        self.error_count_label = QLabel("0")
        self.error_count_label.setStyleSheet("color: red; font-weight: bold;")
        stats_layout.addWidget(self.error_count_label, 0, 1)
        
        stats_layout.addWidget(QLabel("Warnings:"), 0, 2)
        self.warning_count_label = QLabel("0")
        self.warning_count_label.setStyleSheet("color: orange; font-weight: bold;")
        stats_layout.addWidget(self.warning_count_label, 0, 3)
        
        stats_layout.addWidget(QLabel("Info:"), 1, 0)
        self.info_count_label = QLabel("0")
        self.info_count_label.setStyleSheet("color: green; font-weight: bold;")
        stats_layout.addWidget(self.info_count_label, 1, 1)
        
        stats_layout.addWidget(QLabel("Total:"), 1, 2)
        self.total_count_label = QLabel("0")
        self.total_count_label.setStyleSheet("font-weight: bold;")
        stats_layout.addWidget(self.total_count_label, 1, 3)
        
        layout.addWidget(stats_group)
        
        # Live Log
        log_group = QGroupBox("📝 Live Test Log")
        log_layout = QVBoxLayout(log_group)
        
        self.log_display = QTextEdit()
        self.log_display.setReadOnly(True)
        self.log_display.setFont(QFont("Consolas", 9))
        log_layout.addWidget(self.log_display)
        
        layout.addWidget(log_group)
        
        return panel

    def run_initial_tests(self):
        """Run initial component availability tests"""
        self.logger.log("INFO", "Running initial component availability tests")

        # Test component imports
        components = {
            "Live Monitor": LIVE_MONITOR_AVAILABLE,
            "Testing Mode": TESTING_MODE_AVAILABLE,
            "Smart Features": SMART_FEATURES_AVAILABLE,
            "Temporal Dashboard": TEMPORAL_DASHBOARD_AVAILABLE
        }

        for component, available in components.items():
            if available:
                self.logger.log_component_status(component, "AVAILABLE")
            else:
                self.logger.log_component_status(component, "NOT_AVAILABLE")

    def update_monitoring(self):
        """Update monitoring display"""
        # Update statistics
        self.error_count_label.setText(str(self.logger.error_count))
        self.warning_count_label.setText(str(self.logger.warning_count))
        self.info_count_label.setText(str(self.logger.info_count))
        self.total_count_label.setText(str(len(self.logger.log_entries)))

        # Update session info
        duration = datetime.now() - self.logger.session_start
        duration_str = str(duration).split('.')[0]
        self.session_info_label.setText(f"Session: {duration_str}")

        # Update log display (show last 50 entries)
        recent_logs = self.logger.log_entries[-50:]
        log_text = ""

        for entry in recent_logs:
            timestamp = entry["timestamp"].split("T")[1].split(".")[0]
            level = entry["level"]
            message = entry["message"]

            # Color coding for different levels
            if level == "ERROR":
                log_text += f'<span style="color: red;">[{timestamp}] {level}: {message}</span><br>'
            elif level == "WARNING":
                log_text += f'<span style="color: orange;">[{timestamp}] {level}: {message}</span><br>'
            elif level == "INFO":
                log_text += f'<span style="color: green;">[{timestamp}] {level}: {message}</span><br>'
            else:
                log_text += f'[{timestamp}] {level}: {message}<br>'

        self.log_display.setHtml(log_text)

        # Auto-scroll to bottom
        scrollbar = self.log_display.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())

    def test_live_monitor(self):
        """Test Live Monitor component"""
        self.logger.log("INFO", "Manual test: Live Monitor component")
        success = self.component_tester.test_live_monitor()

        if success:
            self.logger.log("INFO", "Live Monitor component test PASSED")
        else:
            self.logger.log("ERROR", "Live Monitor component test FAILED")

    def test_testing_mode(self):
        """Test Testing Mode component"""
        self.logger.log("INFO", "Manual test: Testing Mode component")
        success = self.component_tester.test_testing_mode()

        if success:
            self.logger.log("INFO", "Testing Mode component test PASSED")
        else:
            self.logger.log("ERROR", "Testing Mode component test FAILED")

    def test_smart_features(self):
        """Test Smart Features component"""
        self.logger.log("INFO", "Manual test: Smart Features component")
        success = self.component_tester.test_smart_features()

        if success:
            self.logger.log("INFO", "Smart Features component test PASSED")
        else:
            self.logger.log("ERROR", "Smart Features component test FAILED")

    def test_temporal_dashboard(self):
        """Test Temporal Dashboard component"""
        self.logger.log("INFO", "Manual test: Temporal Dashboard component")
        success = self.component_tester.test_temporal_dashboard()

        if success:
            self.logger.log("INFO", "Temporal Dashboard component test PASSED")
        else:
            self.logger.log("ERROR", "Temporal Dashboard component test FAILED")

    def test_all_components(self):
        """Test all components sequentially"""
        self.logger.log("INFO", "Starting comprehensive component testing")

        tests = [
            ("Live Monitor", self.component_tester.test_live_monitor),
            ("Testing Mode", self.component_tester.test_testing_mode),
            ("Smart Features", self.component_tester.test_smart_features),
            ("Temporal Dashboard", self.component_tester.test_temporal_dashboard)
        ]

        passed = 0
        failed = 0

        for test_name, test_func in tests:
            self.logger.log("INFO", f"Running {test_name} test...")
            try:
                if test_func():
                    passed += 1
                    self.logger.log("INFO", f"{test_name} test PASSED")
                else:
                    failed += 1
                    self.logger.log("ERROR", f"{test_name} test FAILED")
            except Exception as e:
                failed += 1
                self.logger.log_error(test_name, e, "Comprehensive testing")

        self.logger.log("INFO", f"Comprehensive testing complete: {passed} passed, {failed} failed")

        # Show summary
        QMessageBox.information(self, "Test Results",
                              f"Comprehensive Testing Complete\n\n"
                              f"✅ Passed: {passed}\n"
                              f"❌ Failed: {failed}\n\n"
                              f"Check the log for detailed results.")

    def open_live_monitor(self):
        """Open Live Monitor for manual testing"""
        try:
            if not LIVE_MONITOR_AVAILABLE:
                self.logger.log("WARNING", "Live Monitor not available")
                QMessageBox.warning(self, "Not Available", "Live Monitor component is not available")
                return

            if "live_monitor" not in self.test_windows:
                self.test_windows["live_monitor"] = LiveScanMonitorWindow()
                self.logger.log("INFO", "Live Monitor opened for manual testing")

            window = self.test_windows["live_monitor"]
            window.show()
            window.raise_()
            window.activateWindow()

        except Exception as e:
            self.logger.log_error("LiveMonitor", e, "Manual opening")
            QMessageBox.critical(self, "Error", f"Failed to open Live Monitor: {e}")

    def open_testing_mode(self):
        """Open Testing Mode for manual testing"""
        try:
            if not TESTING_MODE_AVAILABLE:
                self.logger.log("WARNING", "Testing Mode not available")
                QMessageBox.warning(self, "Not Available", "Testing Mode component is not available")
                return

            if "testing_mode" not in self.test_windows:
                self.test_windows["testing_mode"] = LiveStreamTestingWindow()
                self.logger.log("INFO", "Testing Mode opened for manual testing")

            window = self.test_windows["testing_mode"]
            window.show()
            window.raise_()
            window.activateWindow()

        except Exception as e:
            self.logger.log_error("TestingMode", e, "Manual opening")
            QMessageBox.critical(self, "Error", f"Failed to open Testing Mode: {e}")

    def open_temporal_dashboard(self):
        """Open Temporal Dashboard for manual testing"""
        try:
            if not TEMPORAL_DASHBOARD_AVAILABLE:
                self.logger.log("WARNING", "Temporal Dashboard not available")
                QMessageBox.warning(self, "Not Available", "Temporal Dashboard component is not available")
                return

            if "temporal_dashboard" not in self.test_windows:
                self.test_windows["temporal_dashboard"] = TemporalAnalyticsDashboard()
                self.logger.log("INFO", "Temporal Dashboard opened for manual testing")

            window = self.test_windows["temporal_dashboard"]
            window.show()
            window.raise_()
            window.activateWindow()

        except Exception as e:
            self.logger.log_error("TemporalDashboard", e, "Manual opening")
            QMessageBox.critical(self, "Error", f"Failed to open Temporal Dashboard: {e}")

    def export_logs(self):
        """Export session logs"""
        try:
            filename, _ = QFileDialog.getSaveFileName(
                self, "Export Test Logs",
                f"test_session_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                "JSON Files (*.json);;Text Files (*.txt)"
            )

            if filename:
                summary = self.logger.get_session_summary()
                summary["log_entries"] = self.logger.log_entries

                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(summary, f, indent=2, default=str)

                self.logger.log("INFO", f"Logs exported to {filename}")
                QMessageBox.information(self, "Export Complete", f"Logs exported to:\n{filename}")

        except Exception as e:
            self.logger.log_error("Export", e, "Log export")
            QMessageBox.critical(self, "Export Failed", f"Failed to export logs: {e}")

    def clear_logs(self):
        """Clear session logs"""
        reply = QMessageBox.question(self, "Clear Logs",
                                   "Are you sure you want to clear all logs?\n"
                                   "This action cannot be undone.")

        if reply == QMessageBox.StandardButton.Yes:
            self.logger.log_entries.clear()
            self.logger.error_count = 0
            self.logger.warning_count = 0
            self.logger.info_count = 0
            self.log_display.clear()

            self.logger.log("INFO", "Logs cleared by user")

    def show_session_summary(self):
        """Show comprehensive session summary"""
        summary = self.logger.get_session_summary()

        summary_text = "🧪 TEST SESSION SUMMARY\n"
        summary_text += "=" * 50 + "\n\n"

        summary_text += f"Session Duration: {summary['session_duration']}\n"
        summary_text += f"Total Log Entries: {summary['total_entries']}\n"
        summary_text += f"Errors: {summary['error_count']}\n"
        summary_text += f"Warnings: {summary['warning_count']}\n"
        summary_text += f"Info Messages: {summary['info_count']}\n\n"

        summary_text += "COMPONENT STATUS:\n"
        for component, status in summary['component_status'].items():
            summary_text += f"• {component}: {status['status']}\n"

        summary_text += f"\nLog File: {summary['log_file']}\n"

        # Create summary dialog
        dialog = QDialog(self)
        dialog.setWindowTitle("Session Summary")
        dialog.setModal(True)
        dialog.resize(500, 400)

        layout = QVBoxLayout(dialog)

        text_edit = QTextEdit()
        text_edit.setPlainText(summary_text)
        text_edit.setReadOnly(True)
        layout.addWidget(text_edit)

        close_btn = QPushButton("Close")
        close_btn.clicked.connect(dialog.accept)
        layout.addWidget(close_btn)

        dialog.exec()

    def closeEvent(self, event):
        """Handle window close"""
        self.logger.log("INFO", "Test session ending")

        # Close all test windows
        for window_name, window in self.test_windows.items():
            try:
                if window and hasattr(window, 'close'):
                    window.close()
                    self.logger.log("INFO", f"Closed {window_name} window")
            except Exception as e:
                self.logger.log_error(window_name, e, "Window cleanup")

        # Final summary
        summary = self.logger.get_session_summary()
        self.logger.log("INFO", "Test session completed", summary)

        event.accept()


def main():
    """Main test session function"""
    print("🧪 Starting MFFUHijack GUI Test Session Monitor")
    print("=" * 60)
    print()
    print("This monitor will:")
    print("• Track all GUI component testing")
    print("• Log errors, warnings, and activities")
    print("• Provide real-time monitoring")
    print("• Enable manual testing of all components")
    print("• Generate comprehensive test reports")
    print()
    print("📋 Available Components:")
    print(f"• Live Scan Monitor: {'✅' if LIVE_MONITOR_AVAILABLE else '❌'}")
    print(f"• Testing Mode: {'✅' if TESTING_MODE_AVAILABLE else '❌'}")
    print(f"• Smart Features: {'✅' if SMART_FEATURES_AVAILABLE else '❌'}")
    print(f"• Temporal Dashboard: {'✅' if TEMPORAL_DASHBOARD_AVAILABLE else '❌'}")
    print()
    print("🚀 Ready for comprehensive GUI testing!")
    print("=" * 60)

    app = QApplication(sys.argv)

    monitor = TestSessionMonitor()
    monitor.show()

    sys.exit(app.exec())


if __name__ == "__main__":
    main()
