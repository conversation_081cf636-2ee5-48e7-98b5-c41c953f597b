#!/usr/bin/env python3
"""
Test script for Manual OCR Region Selection
Tests the resizable rectangle interface and integration
"""

import sys
from PyQt6.QtWidgets import *
from PyQt6.QtCore import *
from PyQt6.QtGui import *

from manual_ocr_region_selector import OCRRegionSelectorDialog


class OCRRegionTestWindow(QMainWindow):
    """Test window for OCR region selection"""
    
    def __init__(self):
        super().__init__()
        self.current_region = (10.0, 67.0, 80.0, 25.0)  # Default region
        self.init_ui()
    
    def init_ui(self):
        """Initialize test UI"""
        self.setWindowTitle("🎯 Manual OCR Region Selection Test")
        self.setGeometry(200, 200, 600, 400)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # Header
        header_label = QLabel("🎯 Manual OCR Region Selection Test")
        header_label.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        header_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(header_label)
        
        # Description
        desc_text = """
This test demonstrates the manual OCR region selection feature:

• Resizable rectangle over livestream template
• Drag to move, resize with corner/edge handles
• Precise coordinate controls
• Quick preset buttons
• Real-time percentage display
        """
        
        desc_label = QLabel(desc_text.strip())
        desc_label.setWordWrap(True)
        desc_label.setStyleSheet("padding: 10px; background-color: #f0f0f0; border-radius: 5px;")
        layout.addWidget(desc_label)
        
        # Current region display
        region_group = QGroupBox("📊 Current OCR Region")
        region_layout = QVBoxLayout(region_group)
        
        self.region_display = QLabel()
        self.region_display.setFont(QFont("Consolas", 12))
        self.region_display.setStyleSheet("background-color: #e8f5e8; padding: 10px; border-radius: 5px;")
        self.update_region_display()
        region_layout.addWidget(self.region_display)
        
        layout.addWidget(region_group)
        
        # Test buttons
        button_group = QGroupBox("🧪 Test Controls")
        button_layout = QVBoxLayout(button_group)
        
        # Open selector button
        open_selector_btn = QPushButton("🎯 Open Manual OCR Region Selector")
        open_selector_btn.setMinimumHeight(40)
        open_selector_btn.setStyleSheet("""
            QPushButton {
                font-size: 14px;
                font-weight: bold;
                background-color: #2196F3;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 10px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """)
        open_selector_btn.clicked.connect(self.open_region_selector)
        button_layout.addWidget(open_selector_btn)
        
        # Test with different presets
        preset_layout = QHBoxLayout()
        
        bottom_third_btn = QPushButton("Bottom Third")
        bottom_third_btn.clicked.connect(lambda: self.set_region(0, 67, 100, 33))
        preset_layout.addWidget(bottom_third_btn)
        
        center_btn = QPushButton("Center")
        center_btn.clicked.connect(lambda: self.set_region(25, 25, 50, 50))
        preset_layout.addWidget(center_btn)
        
        full_screen_btn = QPushButton("Full Screen")
        full_screen_btn.clicked.connect(lambda: self.set_region(0, 0, 100, 100))
        preset_layout.addWidget(full_screen_btn)
        
        button_layout.addLayout(preset_layout)
        
        layout.addWidget(button_group)
        
        # Test results
        results_group = QGroupBox("📋 Test Results")
        results_layout = QVBoxLayout(results_group)
        
        self.results_text = QTextEdit()
        self.results_text.setMaximumHeight(150)
        self.results_text.setReadOnly(True)
        results_layout.addWidget(self.results_text)
        
        layout.addWidget(results_group)
        
        # Log initial state
        self.log_result("🚀 OCR Region Test initialized")
        self.log_result(f"📊 Initial region: {self.current_region}")
    
    def open_region_selector(self):
        """Open the manual OCR region selector"""
        try:
            self.log_result("🎯 Opening manual OCR region selector...")
            
            # Create and show the dialog
            dialog = OCRRegionSelectorDialog(self, initial_region=self.current_region)
            
            if dialog.exec() == QDialog.DialogCode.Accepted:
                # Get the selected region
                selected_region = dialog.get_selected_region()
                self.current_region = selected_region
                
                # Update display
                self.update_region_display()
                
                # Log the result
                x, y, w, h = selected_region
                self.log_result(f"✅ Region updated: X:{x:.1f}%, Y:{y:.1f}%, W:{w:.1f}%, H:{h:.1f}%")
                
                # Show success message
                QMessageBox.information(self, "Region Updated", 
                                      f"OCR region has been updated!\n\n"
                                      f"Position: {x:.1f}%, {y:.1f}%\n"
                                      f"Size: {w:.1f}% × {h:.1f}%")
            else:
                self.log_result("❌ Region selection cancelled")
                
        except Exception as e:
            error_msg = f"❌ Error opening region selector: {e}"
            self.log_result(error_msg)
            QMessageBox.critical(self, "Error", error_msg)
    
    def set_region(self, x: float, y: float, w: float, h: float):
        """Set region to specific values"""
        self.current_region = (x, y, w, h)
        self.update_region_display()
        self.log_result(f"📐 Region set to: X:{x}%, Y:{y}%, W:{w}%, H:{h}%")
    
    def update_region_display(self):
        """Update the region display"""
        x, y, w, h = self.current_region
        
        display_text = f"""
Current OCR Region:
  X Position: {x:.1f}%
  Y Position: {y:.1f}%
  Width: {w:.1f}%
  Height: {h:.1f}%

Pixel Coordinates (for 854×480 stream):
  X: {int(854 * x / 100)} pixels
  Y: {int(480 * y / 100)} pixels
  Width: {int(854 * w / 100)} pixels
  Height: {int(480 * h / 100)} pixels
        """.strip()
        
        self.region_display.setText(display_text)
    
    def log_result(self, message: str):
        """Log a test result"""
        timestamp = QTime.currentTime().toString("hh:mm:ss")
        self.results_text.append(f"[{timestamp}] {message}")
        
        # Auto-scroll to bottom
        scrollbar = self.results_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())


def main():
    """Main test function"""
    print("🎯 Starting Manual OCR Region Selection Test")
    print("=" * 50)
    print()
    print("This test will:")
    print("• Open a test window with region controls")
    print("• Allow you to test the manual OCR region selector")
    print("• Show real-time region coordinates")
    print("• Demonstrate preset functionality")
    print("• Log all test results")
    print()
    print("🧪 Test Features:")
    print("• Resizable rectangle over livestream template")
    print("• Drag to move, resize with handles")
    print("• Precise coordinate controls")
    print("• Quick preset buttons")
    print("• Real-time percentage display")
    print()
    print("🚀 Ready to test manual OCR region selection!")
    print("=" * 50)
    
    app = QApplication(sys.argv)
    
    test_window = OCRRegionTestWindow()
    test_window.show()
    
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
