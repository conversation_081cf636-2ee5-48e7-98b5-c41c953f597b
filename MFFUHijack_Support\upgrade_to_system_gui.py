#!/usr/bin/env python3
"""
Upgrade script to switch MFFUHijack to the system default GUI
This script updates the main.py file to use the system interface with proper sizing
"""

import os
import shutil
from datetime import datetime


def backup_current_files():
    """Backup current GUI files"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_dir = f"backup_gui_{timestamp}"
    
    print(f"Creating backup directory: {backup_dir}")
    os.makedirs(backup_dir, exist_ok=True)
    
    # Files to backup
    files_to_backup = [
        "main.py",
        "gui.py", 
        "modern_gui.py",
        "elegant_gui.py"
    ]
    
    for file in files_to_backup:
        if os.path.exists(file):
            shutil.copy2(file, os.path.join(backup_dir, file))
            print(f"Backed up: {file}")
    
    return backup_dir


def update_main_py():
    """Update main.py to use system GUI"""
    main_py_content = '''#!/usr/bin/env python3
"""
MFFUHijack - Main Application Entry Point
Real-Time OCR Livestream Code Detection with System Default GUI
"""

import sys
import os
from PyQt6.QtWidgets import QApplication

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from system_gui import SystemMFFUHijackGUI, message_logger


def main():
    """Main application entry point"""
    # Create QApplication
    app = QApplication(sys.argv)
    
    # Set application properties
    app.setApplicationName("MFFUHijack")
    app.setApplicationVersion("2.0")
    app.setOrganizationName("MFFUHijack")
    
    try:
        # Create and show main window with system default design
        main_window = SystemMFFUHijackGUI()
        main_window.show()
        
        # Log startup message
        message_logger.info("MFFUHijack started with system default interface")
        message_logger.info("All components properly sized and positioned")
        message_logger.info("System ready for operation")
        
        # Run the application
        sys.exit(app.exec())
        
    except Exception as e:
        print(f"Error starting MFFUHijack: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
'''
    
    with open("main.py", "w", encoding="utf-8") as f:
        f.write(main_py_content)
    
    print("Updated main.py to use system default GUI")


def main():
    """Main upgrade function"""
    print("🖥️  MFFUHijack System Default GUI Upgrade")
    print("=" * 45)
    
    # Check if system_gui.py exists
    if not os.path.exists("system_gui.py"):
        print("❌ Error: system_gui.py not found!")
        print("Make sure the system GUI file is in the current directory.")
        return
    
    # Backup current files
    backup_dir = backup_current_files()
    print(f"✅ Backup created: {backup_dir}")
    
    # Update main.py
    update_main_py()
    print("✅ Updated main.py")
    
    print("\n🎉 Upgrade Complete!")
    print("\nThe system default GUI features:")
    print("• 🖥️  Native system styling")
    print("• 📏 Proper sizing and spacing")
    print("• 👁️  Maximum visibility of all elements")
    print("• ⚡ Fast and responsive")
    print("• 🔧 Functional and practical")
    print("• 📐 Correctly positioned components")
    
    print(f"\nTo run MFFUHijack with the system GUI:")
    print("  python main.py")
    print("\nTo test the GUI separately:")
    print("  python test_system_gui.py")
    
    print(f"\nYour original files are backed up in: {backup_dir}")
    
    print("\n📋 System GUI Specifications:")
    print("• Main Window: 1000×750 pixels")
    print("• Proper margins: 10-15px throughout")
    print("• Button heights: 30-35px")
    print("• Input heights: 25px")
    print("• Consistent spacing: 10-15px")
    print("• All elements properly visible")


if __name__ == "__main__":
    main()
