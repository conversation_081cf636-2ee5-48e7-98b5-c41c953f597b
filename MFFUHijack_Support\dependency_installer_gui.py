"""
GUI Dependency Installer for MFFUHijack
Provides a user-friendly interface for installing missing dependencies
"""

import sys
import subprocess
import threading
from typing import List, Dict
from PyQt6.QtWidgets import (
    QApplication, QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
    QPushButton, QTextEdit, QProgressBar, QCheckBox, QGroupBox,
    QScrollArea, QWidget, QMessageBox
)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt6.QtGui import QFont, QPixmap, QIcon


class PackageInstaller(QThread):
    """Thread for installing packages without blocking GUI"""
    
    progress_update = pyqtSignal(str)  # Status message
    package_installed = pyqtSignal(str, bool)  # package_name, success
    installation_complete = pyqtSignal(list)  # list of successfully installed packages
    
    def __init__(self, packages_to_install: List[str]):
        super().__init__()
        self.packages = packages_to_install
        self.installed_packages = []
    
    def run(self):
        """Install packages one by one"""
        for package in self.packages:
            self.progress_update.emit(f"Installing {package}...")

            # Special handling for yt-dlp
            if package == 'yt-dlp':
                try:
                    from yt_dlp_installer import install_yt_dlp_if_needed
                    self.progress_update.emit("📺 Installing yt-dlp with enhanced installer...")

                    if install_yt_dlp_if_needed(auto_download=True):
                        self.progress_update.emit("✅ yt-dlp installed successfully")
                        self.package_installed.emit(package, True)
                        self.installed_packages.append(package)
                    else:
                        self.progress_update.emit("⚠️ yt-dlp installation needs manual completion")
                        self.package_installed.emit(package, False)
                    continue
                except Exception as e:
                    self.progress_update.emit(f"⚠️ yt-dlp installer error: {e}")
                    # Fall back to regular pip installation

            # Regular pip installation for other packages
            try:
                result = subprocess.run([
                    sys.executable, "-m", "pip", "install", package
                ], capture_output=True, text=True, timeout=300)

                if result.returncode == 0:
                    self.progress_update.emit(f"✅ {package} installed successfully")
                    self.package_installed.emit(package, True)
                    self.installed_packages.append(package)
                else:
                    error_msg = result.stderr.strip() if result.stderr else "Unknown error"
                    self.progress_update.emit(f"❌ Failed to install {package}: {error_msg}")
                    self.package_installed.emit(package, False)
            
            except subprocess.TimeoutExpired:
                self.progress_update.emit(f"❌ Timeout installing {package}")
                self.package_installed.emit(package, False)
            except Exception as e:
                self.progress_update.emit(f"❌ Error installing {package}: {str(e)}")
                self.package_installed.emit(package, False)
        
        self.installation_complete.emit(self.installed_packages)


class DependencyInstallerDialog(QDialog):
    """GUI dialog for dependency installation"""
    
    def __init__(self, missing_packages: Dict[str, str], parent=None):
        super().__init__(parent)
        self.missing_packages = missing_packages
        self.selected_packages = []
        self.installer_thread = None
        
        self.setWindowTitle("MFFUHijack - Install Dependencies")
        self.setMinimumSize(600, 500)
        self.setModal(True)
        
        self.init_ui()
    
    def init_ui(self):
        """Initialize the user interface"""
        layout = QVBoxLayout()
        
        # Title
        title = QLabel("🔧 MFFUHijack Dependency Installer")
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title.setFont(title_font)
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)
        
        # Description
        desc = QLabel(
            "Some optional packages are missing. Installing them will enable full functionality.\n"
            "Select the packages you want to install:"
        )
        desc.setWordWrap(True)
        desc.setStyleSheet("color: #666; margin: 10px;")
        layout.addWidget(desc)
        
        # Package selection area
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)
        
        self.package_checkboxes = {}
        
        # Group packages by category
        categories = {
            "Core Functionality": {
                'yt-dlp': "YouTube video processing (required for livestream capture)",
                'easyocr': "High-quality OCR engine (recommended - works reliably on all systems)",
            },
            "Machine Learning": {
                'torch': "PyTorch framework (required for custom model training)",
                'torchvision': "Computer vision utilities (required for custom models)",
            }
        }
        
        for category, packages in categories.items():
            # Create group box for category
            group_box = QGroupBox(category)
            group_layout = QVBoxLayout()
            
            for package, description in packages.items():
                if package in self.missing_packages:
                    checkbox = QCheckBox(f"{package}")
                    checkbox.setChecked(True)  # Default to checked
                    
                    # Add description
                    desc_label = QLabel(f"   {description}")
                    desc_label.setStyleSheet("color: #666; font-size: 11px; margin-left: 20px;")
                    desc_label.setWordWrap(True)
                    
                    group_layout.addWidget(checkbox)
                    group_layout.addWidget(desc_label)
                    
                    self.package_checkboxes[package] = checkbox
            
            if group_layout.count() > 0:  # Only add if has packages
                group_box.setLayout(group_layout)
                scroll_layout.addWidget(group_box)
        
        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        layout.addWidget(scroll_area)
        
        # Progress area
        self.progress_group = QGroupBox("Installation Progress")
        progress_layout = QVBoxLayout()
        
        self.progress_text = QTextEdit()
        self.progress_text.setMaximumHeight(150)
        self.progress_text.setReadOnly(True)
        self.progress_text.setStyleSheet("font-family: 'Courier New', monospace; font-size: 10px;")
        progress_layout.addWidget(self.progress_text)
        
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        progress_layout.addWidget(self.progress_bar)
        
        self.progress_group.setLayout(progress_layout)
        self.progress_group.setVisible(False)
        layout.addWidget(self.progress_group)
        
        # Buttons
        button_layout = QHBoxLayout()
        
        self.select_all_btn = QPushButton("Select All")
        self.select_all_btn.clicked.connect(self.select_all_packages)
        button_layout.addWidget(self.select_all_btn)
        
        self.select_none_btn = QPushButton("Select None")
        self.select_none_btn.clicked.connect(self.select_no_packages)
        button_layout.addWidget(self.select_none_btn)
        
        button_layout.addStretch()
        
        self.install_btn = QPushButton("Install Selected")
        self.install_btn.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; padding: 8px; }")
        self.install_btn.clicked.connect(self.start_installation)
        button_layout.addWidget(self.install_btn)
        
        self.skip_btn = QPushButton("Skip")
        self.skip_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.skip_btn)
        
        layout.addLayout(button_layout)
        
        self.setLayout(layout)
    
    def select_all_packages(self):
        """Select all package checkboxes"""
        for checkbox in self.package_checkboxes.values():
            checkbox.setChecked(True)
    
    def select_no_packages(self):
        """Deselect all package checkboxes"""
        for checkbox in self.package_checkboxes.values():
            checkbox.setChecked(False)
    
    def get_selected_packages(self) -> List[str]:
        """Get list of selected packages"""
        selected = []
        for package, checkbox in self.package_checkboxes.items():
            if checkbox.isChecked():
                selected.append(package)
        return selected
    
    def start_installation(self):
        """Start the installation process"""
        selected_packages = self.get_selected_packages()
        
        if not selected_packages:
            QMessageBox.information(self, "No Packages Selected", 
                                  "Please select at least one package to install.")
            return
        
        # Show progress area
        self.progress_group.setVisible(True)
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # Indeterminate progress
        
        # Disable buttons during installation
        self.install_btn.setEnabled(False)
        self.select_all_btn.setEnabled(False)
        self.select_none_btn.setEnabled(False)
        
        # Update button text
        self.install_btn.setText("Installing...")
        
        # Clear progress text
        self.progress_text.clear()
        self.progress_text.append("🚀 Starting installation...\n")
        
        # Start installation thread
        self.installer_thread = PackageInstaller(selected_packages)
        self.installer_thread.progress_update.connect(self.update_progress)
        self.installer_thread.package_installed.connect(self.on_package_installed)
        self.installer_thread.installation_complete.connect(self.on_installation_complete)
        self.installer_thread.start()
    
    def update_progress(self, message: str):
        """Update progress text"""
        self.progress_text.append(message)
        # Auto-scroll to bottom
        scrollbar = self.progress_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())
    
    def on_package_installed(self, package: str, success: bool):
        """Handle individual package installation result"""
        # Update checkbox to show result
        if package in self.package_checkboxes:
            checkbox = self.package_checkboxes[package]
            if success:
                checkbox.setText(f"✅ {package}")
                checkbox.setStyleSheet("color: green;")
            else:
                checkbox.setText(f"❌ {package}")
                checkbox.setStyleSheet("color: red;")
    
    def on_installation_complete(self, installed_packages: List[str]):
        """Handle installation completion"""
        self.progress_bar.setVisible(False)

        if installed_packages:
            self.progress_text.append(f"\n🎉 Installation complete!")
            self.progress_text.append(f"Successfully installed {len(installed_packages)} packages:")
            for package in installed_packages:
                self.progress_text.append(f"  ✅ {package}")

            # Change button to close
            self.install_btn.setText("Continue")
            self.install_btn.setStyleSheet("QPushButton { background-color: #2196F3; color: white; font-weight: bold; padding: 8px; }")
            self.install_btn.clicked.disconnect()
            self.install_btn.clicked.connect(self.accept)
            self.install_btn.setEnabled(True)

            # Hide skip button
            self.skip_btn.setVisible(False)
        else:
            self.progress_text.append(f"\n❌ No packages were installed successfully.")
            self.progress_text.append("\n💡 Common solutions:")
            self.progress_text.append("• Try the Quick Install: python quick_install.py")
            self.progress_text.append("• Install manually: pip install yt-dlp easyocr")

            self.progress_text.append("• Continue anyway - basic functionality will work")

            # Re-enable buttons
            self.install_btn.setText("Retry")
            self.install_btn.setEnabled(True)
            self.select_all_btn.setEnabled(True)
            self.select_none_btn.setEnabled(True)


def show_dependency_installer(missing_packages: Dict[str, str]) -> bool:
    """Show the dependency installer dialog"""
    app = QApplication.instance()
    if app is None:
        app = QApplication([])
    
    dialog = DependencyInstallerDialog(missing_packages)
    result = dialog.exec()
    
    return result == QDialog.DialogCode.Accepted


if __name__ == "__main__":
    # Test the installer
    test_packages = {
        'yt-dlp': 'YouTube video processing',
        'easyocr': 'OCR engine',
        'torch': 'Machine learning framework'
    }
    
    show_dependency_installer(test_packages)
