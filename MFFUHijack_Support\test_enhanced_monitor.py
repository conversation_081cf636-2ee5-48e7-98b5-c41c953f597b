#!/usr/bin/env python3
"""
Enhanced Test script for the Live Scan Monitor Window
Demonstrates all the new enhanced functionality including themes, notifications, 
interactive features, performance monitoring, and more.
"""

import sys
import time
import threading
import random
import numpy as np
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, 
    QHBoxLayout, QLabel, QSlider, QCheckBox, QComboBox, QGroupBox,
    QGridLayout, QSpinBox
)
from PyQt6.QtCore import QTimer, Qt
from PyQt6.QtGui import QPixmap, QImage

from live_scan_monitor import LiveScanMonitorWindow


class EnhancedTestControlWindow(QMainWindow):
    """Enhanced control window for testing all Live Scan Monitor features"""
    
    def __init__(self):
        super().__init__()
        self.monitor_window = None
        self.simulation_timer = None
        self.frame_simulation_timer = None
        self.init_ui()
    
    def init_ui(self):
        """Initialize the enhanced test control UI"""
        self.setWindowTitle("Live Scan Monitor Enhanced Test Controller")
        self.setGeometry(100, 100, 600, 700)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # Monitor Control Section
        monitor_group = QGroupBox("Monitor Control")
        monitor_layout = QVBoxLayout(monitor_group)
        
        # Basic controls
        basic_controls = QHBoxLayout()
        
        self.create_monitor_btn = QPushButton("🔍 Create Enhanced Monitor")
        self.create_monitor_btn.clicked.connect(self.create_monitor_window)
        basic_controls.addWidget(self.create_monitor_btn)
        
        self.start_session_btn = QPushButton("🚀 Start Session")
        self.start_session_btn.clicked.connect(self.start_scanning_session)
        self.start_session_btn.setEnabled(False)
        basic_controls.addWidget(self.start_session_btn)
        
        self.stop_session_btn = QPushButton("⏹️ Stop Session")
        self.stop_session_btn.clicked.connect(self.stop_scanning_session)
        self.stop_session_btn.setEnabled(False)
        basic_controls.addWidget(self.stop_session_btn)
        
        monitor_layout.addLayout(basic_controls)
        layout.addWidget(monitor_group)
        
        # Simulation Control Section
        sim_group = QGroupBox("Simulation Controls")
        sim_layout = QVBoxLayout(sim_group)
        
        # Auto simulation controls
        auto_sim_layout = QHBoxLayout()
        
        self.auto_sim_checkbox = QCheckBox("Auto-simulate codes")
        self.auto_sim_checkbox.toggled.connect(self.toggle_auto_simulation)
        auto_sim_layout.addWidget(self.auto_sim_checkbox)
        
        auto_sim_layout.addWidget(QLabel("Interval (s):"))
        self.sim_interval_spin = QSpinBox()
        self.sim_interval_spin.setRange(1, 30)
        self.sim_interval_spin.setValue(3)
        auto_sim_layout.addWidget(self.sim_interval_spin)
        
        sim_layout.addLayout(auto_sim_layout)
        
        # Manual simulation buttons
        manual_sim_layout = QHBoxLayout()
        
        self.simulate_valid_btn = QPushButton("✅ Valid Code")
        self.simulate_valid_btn.clicked.connect(lambda: self.simulate_code_detection(force_valid=True))
        self.simulate_valid_btn.setEnabled(False)
        manual_sim_layout.addWidget(self.simulate_valid_btn)
        
        self.simulate_invalid_btn = QPushButton("❌ Invalid Code")
        self.simulate_invalid_btn.clicked.connect(lambda: self.simulate_code_detection(force_valid=False))
        self.simulate_invalid_btn.setEnabled(False)
        manual_sim_layout.addWidget(self.simulate_invalid_btn)
        
        self.simulate_frame_btn = QPushButton("🎥 Simulate Frame")
        self.simulate_frame_btn.clicked.connect(self.simulate_frame_update)
        self.simulate_frame_btn.setEnabled(False)
        manual_sim_layout.addWidget(self.simulate_frame_btn)
        
        sim_layout.addLayout(manual_sim_layout)
        layout.addWidget(sim_group)
        
        # Frame Simulation Section
        frame_group = QGroupBox("Frame Simulation")
        frame_layout = QVBoxLayout(frame_group)
        
        # Frame simulation controls
        frame_controls = QHBoxLayout()
        
        self.auto_frames_checkbox = QCheckBox("Auto-simulate frames")
        self.auto_frames_checkbox.toggled.connect(self.toggle_frame_simulation)
        frame_controls.addWidget(self.auto_frames_checkbox)
        
        frame_controls.addWidget(QLabel("FPS:"))
        self.fps_spin = QSpinBox()
        self.fps_spin.setRange(1, 60)
        self.fps_spin.setValue(10)
        frame_controls.addWidget(self.fps_spin)
        
        frame_layout.addLayout(frame_controls)
        layout.addWidget(frame_group)
        
        # Test Features Section
        features_group = QGroupBox("Test Enhanced Features")
        features_layout = QGridLayout(features_group)
        
        # Theme testing
        theme_btn = QPushButton("🎨 Test Theme Toggle")
        theme_btn.clicked.connect(self.test_theme_toggle)
        features_layout.addWidget(theme_btn, 0, 0)
        
        # Screenshot testing
        screenshot_btn = QPushButton("📷 Test Screenshot")
        screenshot_btn.clicked.connect(self.test_screenshot)
        features_layout.addWidget(screenshot_btn, 0, 1)
        
        # Settings testing
        settings_btn = QPushButton("⚙️ Test Settings")
        settings_btn.clicked.connect(self.test_settings)
        features_layout.addWidget(settings_btn, 1, 0)
        
        # History testing
        history_btn = QPushButton("📚 Test History")
        history_btn.clicked.connect(self.test_history)
        features_layout.addWidget(history_btn, 1, 1)
        
        # Export testing
        export_btn = QPushButton("💾 Test Export")
        export_btn.clicked.connect(self.test_export)
        features_layout.addWidget(export_btn, 2, 0)
        
        # Fullscreen testing
        fullscreen_btn = QPushButton("⛶ Test Fullscreen")
        fullscreen_btn.clicked.connect(self.test_fullscreen)
        features_layout.addWidget(fullscreen_btn, 2, 1)
        
        layout.addWidget(features_group)
        
        # Status Section
        status_group = QGroupBox("Test Status")
        status_layout = QVBoxLayout(status_group)
        
        self.status_label = QLabel("Ready to test enhanced features")
        self.status_label.setWordWrap(True)
        status_layout.addWidget(self.status_label)
        
        layout.addWidget(status_group)
    
    def create_monitor_window(self):
        """Create the Enhanced Live Scan Monitor window"""
        if self.monitor_window is None:
            self.monitor_window = LiveScanMonitorWindow()
            self.monitor_window.window_closed.connect(self.on_monitor_closed)
            self.monitor_window.show()
            
            # Position the monitor window next to this control window
            control_geometry = self.geometry()
            monitor_x = control_geometry.x() + control_geometry.width() + 20
            monitor_y = control_geometry.y()
            self.monitor_window.setGeometry(monitor_x, monitor_y, 1400, 900)
            
            self.create_monitor_btn.setEnabled(False)
            self.start_session_btn.setEnabled(True)
            self.update_status("✅ Enhanced Live Scan Monitor window created with all features")
            print("✅ Enhanced Live Scan Monitor window created")
    
    def start_scanning_session(self):
        """Start an enhanced scanning session"""
        if self.monitor_window:
            test_url = "https://www.youtube.com/@MyFundedFuturesPropFirm"
            self.monitor_window.start_scanning_session(test_url)
            
            self.start_session_btn.setEnabled(False)
            self.stop_session_btn.setEnabled(True)
            self.simulate_valid_btn.setEnabled(True)
            self.simulate_invalid_btn.setEnabled(True)
            self.simulate_frame_btn.setEnabled(True)
            
            self.update_status("🚀 Enhanced scanning session started with full feature testing")
            print("🚀 Enhanced scanning session started")
    
    def stop_scanning_session(self):
        """Stop the enhanced scanning session"""
        if self.monitor_window:
            self.monitor_window.stop_scanning_session()
            
            self.start_session_btn.setEnabled(True)
            self.stop_session_btn.setEnabled(False)
            self.simulate_valid_btn.setEnabled(False)
            self.simulate_invalid_btn.setEnabled(False)
            self.simulate_frame_btn.setEnabled(False)
            
            # Stop all simulations
            self.stop_simulation()
            self.stop_frame_simulation()
            self.auto_sim_checkbox.setChecked(False)
            self.auto_frames_checkbox.setChecked(False)
            
            self.update_status("⏹️ Enhanced scanning session stopped")
            print("⏹️ Enhanced scanning session stopped")
    
    def simulate_code_detection(self, force_valid=None):
        """Simulate enhanced code detection with more variety"""
        if not self.monitor_window:
            return
        
        # Enhanced sample codes with more variety
        valid_codes = [
            {"code": "ABC123", "type": "Starter", "confidence": 0.95},
            {"code": "XYZ789", "type": "Expert", "confidence": 0.87},
            {"code": "RESET5", "type": "Free Reset Code", "confidence": 0.92},
            {"code": "PLUS99", "type": "Starter Plus", "confidence": 0.88},
            {"code": "EXPERT2024", "type": "Expert", "confidence": 0.93},
            {"code": "START50", "type": "Starter", "confidence": 0.89},
            {"code": "RESETFREE", "type": "Reset", "confidence": 0.91},
        ]
        
        invalid_codes = [
            {"code": "INVALID", "type": "Unknown", "confidence": 0.3},
            {"code": "AB", "type": "Starter", "confidence": 0.4},  # Too short
            {"code": "EXPIRED123", "type": "Expert", "confidence": 0.2},  # Low confidence
            {"code": "FAKE", "type": "Invalid", "confidence": 0.1},
        ]
        
        # Choose code based on force_valid parameter
        if force_valid is True:
            code_data = random.choice(valid_codes)
        elif force_valid is False:
            code_data = random.choice(invalid_codes)
        else:
            # Random choice with 70% valid, 30% invalid
            all_codes = valid_codes * 7 + invalid_codes * 3
            code_data = random.choice(all_codes)
        
        code_data['full_text'] = f"FREE 50 {code_data['type'].upper()}: {code_data['code']}"
        
        self.monitor_window.on_code_detected(code_data)
        
        status = "valid" if code_data['confidence'] > 0.5 else "invalid"
        self.update_status(f"🎯 Simulated {status} code: {code_data['code']} ({code_data['type']})")
        print(f"🎯 Simulated {status} code detection: {code_data['code']} ({code_data['type']})")
    
    def simulate_frame_update(self):
        """Simulate frame update with synthetic video data"""
        if not self.monitor_window:
            return
        
        # Create a synthetic frame with some text
        width, height = 854, 480
        frame = np.ones((height, width, 3), dtype=np.uint8) * 40  # Dark background
        
        # Add some random elements to make it look like video
        try:
            import cv2
            
            # Add title
            cv2.putText(frame, "LIVE STREAM SIMULATION", (250, 50), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
            
            # Add timestamp
            timestamp = time.strftime("%H:%M:%S")
            cv2.putText(frame, f"Time: {timestamp}", (50, height - 50), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (200, 200, 200), 1)
            
            # Add random code text occasionally
            if random.random() < 0.3:  # 30% chance
                sample_text = random.choice([
                    "FREE 50 STARTER: ABC123",
                    "EXPERT ACCOUNT: XYZ789", 
                    "USE CODE: RESET5",
                    "STARTER PLUS: PLUS99"
                ])
                cv2.putText(frame, sample_text, (200, 300), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
            
            # Convert to QPixmap
            rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        except ImportError:
            # Fallback if OpenCV not available
            rgb_frame = frame
        
        h, w, ch = rgb_frame.shape
        bytes_per_line = ch * w
        qt_image = QImage(rgb_frame.data, w, h, bytes_per_line, QImage.Format.Format_RGB888)
        pixmap = QPixmap.fromImage(qt_image)
        
        # Update monitor window
        self.monitor_window.update_frame_preview(pixmap)
        
        timestamp = time.strftime("%H:%M:%S")
        self.update_status(f"🎥 Simulated frame update at {timestamp}")
    
    def update_status(self, message):
        """Update status label"""
        self.status_label.setText(f"[{time.strftime('%H:%M:%S')}] {message}")

    def toggle_auto_simulation(self, enabled):
        """Toggle automatic code simulation"""
        if enabled and self.monitor_window and self.monitor_window.is_scanning:
            self.start_simulation()
        else:
            self.stop_simulation()

    def toggle_frame_simulation(self, enabled):
        """Toggle automatic frame simulation"""
        if enabled and self.monitor_window and self.monitor_window.is_scanning:
            self.start_frame_simulation()
        else:
            self.stop_frame_simulation()

    def start_simulation(self):
        """Start automatic code detection simulation"""
        if self.simulation_timer:
            self.simulation_timer.stop()

        self.simulation_timer = QTimer()
        self.simulation_timer.timeout.connect(self.simulate_code_detection)
        interval = self.sim_interval_spin.value() * 1000  # Convert to milliseconds
        self.simulation_timer.start(interval)
        self.update_status(f"🎭 Started auto code simulation (every {self.sim_interval_spin.value()}s)")

    def start_frame_simulation(self):
        """Start automatic frame simulation"""
        if self.frame_simulation_timer:
            self.frame_simulation_timer.stop()

        self.frame_simulation_timer = QTimer()
        self.frame_simulation_timer.timeout.connect(self.simulate_frame_update)
        interval = 1000 // self.fps_spin.value()  # Convert FPS to milliseconds
        self.frame_simulation_timer.start(interval)
        self.update_status(f"🎥 Started auto frame simulation ({self.fps_spin.value()} FPS)")

    def stop_simulation(self):
        """Stop automatic code simulation"""
        if self.simulation_timer:
            self.simulation_timer.stop()
            self.simulation_timer = None

    def stop_frame_simulation(self):
        """Stop automatic frame simulation"""
        if self.frame_simulation_timer:
            self.frame_simulation_timer.stop()
            self.frame_simulation_timer = None

    def test_theme_toggle(self):
        """Test theme toggle functionality"""
        if self.monitor_window:
            self.monitor_window.toggle_theme()
            self.update_status("🎨 Tested theme toggle")
        else:
            self.update_status("❌ No monitor window to test theme toggle")

    def test_screenshot(self):
        """Test screenshot functionality"""
        if self.monitor_window:
            success = self.monitor_window.take_screenshot()
            if success:
                self.update_status("📷 Screenshot test successful")
            else:
                self.update_status("❌ Screenshot test failed")
        else:
            self.update_status("❌ No monitor window to test screenshot")

    def test_settings(self):
        """Test settings dialog"""
        if self.monitor_window:
            self.monitor_window.show_settings_dialog()
            self.update_status("⚙️ Opened settings dialog")
        else:
            self.update_status("❌ No monitor window to test settings")

    def test_history(self):
        """Test session history dialog"""
        if self.monitor_window:
            self.monitor_window.show_session_history()
            self.update_status("📚 Opened session history dialog")
        else:
            self.update_status("❌ No monitor window to test history")

    def test_export(self):
        """Test export functionality"""
        if self.monitor_window:
            self.monitor_window.show_export_menu()
            self.update_status("💾 Opened export menu")
        else:
            self.update_status("❌ No monitor window to test export")

    def test_fullscreen(self):
        """Test fullscreen functionality"""
        if self.monitor_window:
            self.monitor_window.toggle_fullscreen()
            self.update_status("⛶ Toggled fullscreen mode")
        else:
            self.update_status("❌ No monitor window to test fullscreen")

    def on_monitor_closed(self):
        """Handle monitor window being closed"""
        self.monitor_window = None
        self.create_monitor_btn.setEnabled(True)
        self.start_session_btn.setEnabled(False)
        self.stop_session_btn.setEnabled(False)
        self.simulate_valid_btn.setEnabled(False)
        self.simulate_invalid_btn.setEnabled(False)
        self.simulate_frame_btn.setEnabled(False)

        # Stop all simulations
        self.stop_simulation()
        self.stop_frame_simulation()
        self.auto_sim_checkbox.setChecked(False)
        self.auto_frames_checkbox.setChecked(False)

        self.update_status("❌ Monitor window closed")
        print("❌ Monitor window closed")


def main():
    """Main enhanced test function"""
    print("🧪 Starting Enhanced Live Scan Monitor Test")
    print("=" * 60)
    
    app = QApplication(sys.argv)
    
    # Create enhanced test control window
    control_window = EnhancedTestControlWindow()
    control_window.show()
    
    print("📋 Enhanced Test Instructions:")
    print("1. Click 'Create Enhanced Monitor' to open the Live Scan Monitor")
    print("2. Click 'Start Session' to begin monitoring")
    print("3. Use simulation controls to test different features:")
    print("   - Auto-simulate codes with configurable intervals")
    print("   - Manual valid/invalid code simulation")
    print("   - Frame simulation with synthetic video")
    print("4. Test enhanced features:")
    print("   - Theme toggle (Light/Dark)")
    print("   - Screenshot capture")
    print("   - Settings dialog")
    print("   - Session history")
    print("   - Export functionality")
    print("   - Fullscreen mode")
    print("5. Interactive preview features:")
    print("   - Ctrl+Click: Adjust OCR region")
    print("   - Shift+Click: Zoom to point")
    print("   - Right-click: Take screenshot")
    print("   - Ctrl+Wheel: Zoom in/out")
    print("6. Keyboard shortcuts:")
    print("   - Ctrl+S: Screenshot")
    print("   - Ctrl+R: Reset zoom")
    print("   - Space: Pause/Resume")
    print("   - F11: Fullscreen")
    print("   - Ctrl+T: Toggle theme")
    print()
    print("The enhanced monitor includes:")
    print("• Real-time performance monitoring")
    print("• Desktop notifications")
    print("• Session history and export")
    print("• Interactive OCR region adjustment")
    print("• Advanced statistics tracking")
    print("• Customizable themes and settings")
    
    # Run the application
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
