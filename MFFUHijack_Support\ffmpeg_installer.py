#!/usr/bin/env python3
"""
FFmpeg Automatic Installer for MFFUHijack
Handles automatic FFmpeg installation across different platforms
"""

import os
import sys
import platform
import subprocess
import urllib.request
import zipfile
import tarfile
import shutil
from pathlib import Path
import tempfile
import json


class FFmpegInstaller:
    """Handles FFmpeg installation across different platforms"""
    
    def __init__(self):
        self.system = platform.system().lower()
        self.architecture = platform.machine().lower()
        self.ffmpeg_dir = Path.cwd() / "ffmpeg"
        
    def is_ffmpeg_available(self) -> bool:
        """Check if FFmpeg is available in PATH or local directory"""
        # Check system PATH first
        try:
            result = subprocess.run(['ffmpeg', '-version'], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                return True
        except (subprocess.TimeoutExpired, FileNotFoundError):
            pass
        
        # Check local ffmpeg directory
        local_ffmpeg = self.ffmpeg_dir / ("ffmpeg.exe" if self.system == "windows" else "ffmpeg")
        if local_ffmpeg.exists():
            try:
                result = subprocess.run([str(local_ffmpeg), '-version'], 
                                      capture_output=True, text=True, timeout=5)
                if result.returncode == 0:
                    # Add to PATH for this session
                    os.environ['PATH'] = str(self.ffmpeg_dir) + os.pathsep + os.environ['PATH']
                    return True
            except (subprocess.TimeoutExpired, FileNotFoundError):
                pass
        
        return False
    
    def get_download_info(self) -> dict:
        """Get FFmpeg download information for current platform"""
        if self.system == "windows":
            if "64" in self.architecture or "amd64" in self.architecture:
                return {
                    "url": "https://github.com/BtbN/FFmpeg-Builds/releases/download/latest/ffmpeg-master-latest-win64-gpl.zip",
                    "type": "zip",
                    "executable": "ffmpeg.exe"
                }
            else:
                return {
                    "url": "https://github.com/BtbN/FFmpeg-Builds/releases/download/latest/ffmpeg-master-latest-win32-gpl.zip",
                    "type": "zip", 
                    "executable": "ffmpeg.exe"
                }
        
        elif self.system == "darwin":  # macOS
            # For macOS, we'll try to use Homebrew or provide instructions
            return {
                "url": None,
                "type": "homebrew",
                "executable": "ffmpeg"
            }
        
        elif self.system == "linux":
            # For Linux, we'll provide package manager instructions
            return {
                "url": None,
                "type": "package_manager",
                "executable": "ffmpeg"
            }
        
        return None
    
    def install_ffmpeg_windows(self, download_info: dict) -> bool:
        """Install FFmpeg on Windows by downloading and extracting"""
        try:
            print("📥 Downloading FFmpeg for Windows...")
            
            # Create temporary directory
            with tempfile.TemporaryDirectory() as temp_dir:
                zip_path = Path(temp_dir) / "ffmpeg.zip"
                
                # Download FFmpeg
                urllib.request.urlretrieve(download_info["url"], zip_path)
                print("✅ Download complete")
                
                # Extract zip file
                print("📦 Extracting FFmpeg...")
                with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                    zip_ref.extractall(temp_dir)
                
                # Find the extracted directory (usually has a version name)
                extracted_dirs = [d for d in Path(temp_dir).iterdir() if d.is_dir()]
                if not extracted_dirs:
                    print("❌ Could not find extracted FFmpeg directory")
                    return False
                
                ffmpeg_extracted = extracted_dirs[0]
                bin_dir = ffmpeg_extracted / "bin"
                
                if not bin_dir.exists():
                    print("❌ Could not find FFmpeg bin directory")
                    return False
                
                # Create local ffmpeg directory
                self.ffmpeg_dir.mkdir(exist_ok=True)
                
                # Copy FFmpeg executables
                for exe in ["ffmpeg.exe", "ffprobe.exe", "ffplay.exe"]:
                    src = bin_dir / exe
                    if src.exists():
                        dst = self.ffmpeg_dir / exe
                        shutil.copy2(src, dst)
                        print(f"✅ Installed {exe}")
                
                # Add to PATH for current session
                os.environ['PATH'] = str(self.ffmpeg_dir) + os.pathsep + os.environ['PATH']
                
                print("✅ FFmpeg installed successfully!")
                return True
                
        except Exception as e:
            print(f"❌ Failed to install FFmpeg: {e}")
            return False
    
    def install_ffmpeg_macos(self) -> bool:
        """Install FFmpeg on macOS using Homebrew"""
        try:
            # Check if Homebrew is available
            result = subprocess.run(['which', 'brew'], capture_output=True, text=True)
            if result.returncode != 0:
                print("❌ Homebrew not found. Please install Homebrew first:")
                print("   /bin/bash -c \"$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\"")
                print("   Then run: brew install ffmpeg")
                return False
            
            print("📥 Installing FFmpeg via Homebrew...")
            result = subprocess.run(['brew', 'install', 'ffmpeg'], 
                                  capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                print("✅ FFmpeg installed successfully via Homebrew!")
                return True
            else:
                print(f"❌ Homebrew installation failed: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            print("❌ Homebrew installation timed out")
            return False
        except Exception as e:
            print(f"❌ Failed to install FFmpeg via Homebrew: {e}")
            return False
    
    def install_ffmpeg_linux(self) -> bool:
        """Install FFmpeg on Linux using package manager"""
        try:
            # Try different package managers
            package_managers = [
                (['apt', 'update'], ['apt', 'install', '-y', 'ffmpeg']),  # Ubuntu/Debian
                (['yum', 'install', '-y', 'ffmpeg'],),  # CentOS/RHEL
                (['dnf', 'install', '-y', 'ffmpeg'],),  # Fedora
                (['pacman', '-S', '--noconfirm', 'ffmpeg'],),  # Arch
                (['zypper', 'install', '-y', 'ffmpeg'],),  # openSUSE
            ]
            
            for commands in package_managers:
                try:
                    # Check if package manager exists
                    pm_check = subprocess.run(['which', commands[0][0]], 
                                            capture_output=True, text=True)
                    if pm_check.returncode != 0:
                        continue
                    
                    print(f"📥 Installing FFmpeg via {commands[0][0]}...")
                    
                    # Run update command if provided
                    if len(commands) > 1:
                        subprocess.run(commands[0], capture_output=True, text=True, timeout=60)
                        install_cmd = commands[1]
                    else:
                        install_cmd = commands[0]
                    
                    # Run install command
                    result = subprocess.run(install_cmd, capture_output=True, text=True, timeout=300)
                    
                    if result.returncode == 0:
                        print(f"✅ FFmpeg installed successfully via {commands[0][0]}!")
                        return True
                    else:
                        print(f"❌ Installation failed with {commands[0][0]}: {result.stderr}")
                        continue
                        
                except subprocess.TimeoutExpired:
                    print(f"❌ Installation timed out with {commands[0][0]}")
                    continue
                except Exception as e:
                    print(f"❌ Error with {commands[0][0]}: {e}")
                    continue
            
            # If all package managers failed
            print("❌ Could not install FFmpeg automatically.")
            print("Please install FFmpeg manually:")
            print("  Ubuntu/Debian: sudo apt install ffmpeg")
            print("  CentOS/RHEL: sudo yum install ffmpeg")
            print("  Fedora: sudo dnf install ffmpeg")
            print("  Arch: sudo pacman -S ffmpeg")
            return False
            
        except Exception as e:
            print(f"❌ Failed to install FFmpeg on Linux: {e}")
            return False
    
    def install_ffmpeg(self) -> bool:
        """Install FFmpeg for the current platform"""
        if self.is_ffmpeg_available():
            print("✅ FFmpeg is already available")
            return True
        
        print(f"🔧 Installing FFmpeg for {self.system}...")
        
        download_info = self.get_download_info()
        if not download_info:
            print(f"❌ FFmpeg installation not supported for {self.system}")
            return False
        
        if self.system == "windows":
            return self.install_ffmpeg_windows(download_info)
        elif self.system == "darwin":
            return self.install_ffmpeg_macos()
        elif self.system == "linux":
            return self.install_ffmpeg_linux()
        
        return False
    
    def get_installation_instructions(self) -> str:
        """Get manual installation instructions for current platform"""
        if self.system == "windows":
            return """
Windows FFmpeg Installation:
1. Download from: https://ffmpeg.org/download.html#build-windows
2. Extract to a folder (e.g., C:\\ffmpeg)
3. Add C:\\ffmpeg\\bin to your PATH environment variable
4. Restart your command prompt/application
"""
        elif self.system == "darwin":
            return """
macOS FFmpeg Installation:
1. Install Homebrew: /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
2. Run: brew install ffmpeg
"""
        elif self.system == "linux":
            return """
Linux FFmpeg Installation:
- Ubuntu/Debian: sudo apt install ffmpeg
- CentOS/RHEL: sudo yum install ffmpeg
- Fedora: sudo dnf install ffmpeg
- Arch: sudo pacman -S ffmpeg
"""
        return "Platform not supported for automatic installation."


def install_ffmpeg_if_needed() -> bool:
    """Check and install FFmpeg if needed"""
    installer = FFmpegInstaller()
    
    if installer.is_ffmpeg_available():
        return True
    
    print("🎬 FFmpeg is required for video processing but not found.")
    print("Attempting automatic installation...")
    
    return installer.install_ffmpeg()


if __name__ == "__main__":
    # Test the installer
    success = install_ffmpeg_if_needed()
    if success:
        print("✅ FFmpeg is ready!")
    else:
        print("❌ FFmpeg installation failed")
        installer = FFmpegInstaller()
        print(installer.get_installation_instructions())
