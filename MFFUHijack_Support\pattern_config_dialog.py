"""
Pattern Configuration Dialog for MFFUHijack
Allows users to customize OCR text patterns for different account types
"""

import json
import os
from typing import Dict, List
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QCheckBox, QTextEdit, QGroupBox, QScrollArea, QWidget,
    QMessageBox, QTabWidget, QGridLayout, QLineEdit, QFrame
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont


class PatternConfigDialog(QDialog):
    """Dialog for configuring custom OCR patterns"""
    
    patterns_updated = pyqtSignal(dict)  # Emitted when patterns are saved
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Configure Code Detection Patterns")
        self.setMinimumSize(700, 600)
        self.setModal(True)
        
        # Default patterns
        self.default_patterns = {
            "Free Reset Code": {
                "enabled": True,
                "patterns": [
                    "FREE RESETS USE CODE: {code}",
                    "FREE RESET USE CODE: {code}",
                    "RESETS USE CODE: {code}",
                    "RESET CODE: {code}"
                ]
            },
            "Starter": {
                "enabled": True,
                "patterns": [
                    "STARTER ACCOUNTS USE CODE: {code}",
                    "STARTER ACCOUNT USE CODE: {code}",
                    "STARTER USE CODE: {code}",
                    "STARTER: {code}"
                ]
            },
            "Starter Plus": {
                "enabled": True,
                "patterns": [
                    "STARTER+ ACCOUNTS USE CODE: {code}",
                    "STARTER PLUS ACCOUNTS USE CODE: {code}",
                    "STARTER+ USE CODE: {code}",
                    "STARTER PLUS: {code}",
                    "STARTER+: {code}"
                ]
            },
            "Expert": {
                "enabled": True,
                "patterns": [
                    "EXPERT ACCOUNTS USE CODE: {code}",
                    "EXPERT ACCOUNT USE CODE: {code}",
                    "EXPERT USE CODE: {code}",
                    "EXPERT: {code}"
                ]
            }
        }
        
        # Load existing patterns
        self.patterns = self.load_patterns()
        
        self.init_ui()
    
    def init_ui(self):
        """Initialize the user interface"""
        layout = QVBoxLayout()
        
        # Title
        title = QLabel("🎯 Custom Code Detection Patterns")
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title.setFont(title_font)
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)
        
        # Description
        desc = QLabel(
            "Configure which account types to detect and customize the text patterns for each type.\n"
            "Use {code} for the actual code (e.g., RESET3J, HAPPY4TH).\n"
            "Separate multiple patterns with commas."
        )
        desc.setWordWrap(True)
        desc.setStyleSheet("color: #666; margin: 10px; padding: 10px; background-color: #f0f0f0; border-radius: 5px;")
        layout.addWidget(desc)
        
        # Scroll area for account types
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)
        
        self.account_widgets = {}
        
        # Create configuration for each account type
        for account_type, config in self.patterns.items():
            group_widget = self.create_account_group(account_type, config)
            scroll_layout.addWidget(group_widget)
            
        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        layout.addWidget(scroll_area)
        
        # Example section
        example_group = QGroupBox("💡 Pattern Examples")
        example_layout = QVBoxLayout()
        
        examples = [
            "FREE {amount} STARTER: {code} → Matches: 'FREE 50 STARTER: ABC123'",
            "FREE {amount} EXPERT ACCOUNT: {code} → Matches: 'FREE 100 EXPERT ACCOUNT: XYZ789'",
            "{amount} STARTER PLUS: {code} → Matches: '25 STARTER PLUS: DEF456'",
            "GIVEAWAY {amount} STARTER: {code} → Matches: 'GIVEAWAY 75 STARTER: GHI012'"
        ]
        
        for example in examples:
            example_label = QLabel(f"• {example}")
            example_label.setStyleSheet("font-family: 'Courier New', monospace; color: #444;")
            example_layout.addWidget(example_label)
        
        example_group.setLayout(example_layout)
        layout.addWidget(example_group)
        
        # Buttons
        button_layout = QHBoxLayout()
        
        self.reset_btn = QPushButton("Reset to Defaults")
        self.reset_btn.clicked.connect(self.reset_to_defaults)
        button_layout.addWidget(self.reset_btn)
        
        self.test_btn = QPushButton("Test Patterns")
        self.test_btn.clicked.connect(self.test_patterns)
        button_layout.addWidget(self.test_btn)
        
        button_layout.addStretch()
        
        self.save_btn = QPushButton("Save & Apply")
        self.save_btn.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; padding: 8px; }")
        self.save_btn.clicked.connect(self.save_patterns)
        button_layout.addWidget(self.save_btn)
        
        self.cancel_btn = QPushButton("Cancel")
        self.cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_btn)
        
        layout.addLayout(button_layout)
        
        self.setLayout(layout)
    
    def create_account_group(self, account_type: str, config: Dict) -> QGroupBox:
        """Create a configuration group for an account type"""
        group = QGroupBox()
        layout = QVBoxLayout()
        
        # Header with checkbox
        header_layout = QHBoxLayout()
        
        checkbox = QCheckBox(f"🎯 {account_type}")
        checkbox.setChecked(config.get("enabled", True))
        checkbox_font = QFont()
        checkbox_font.setPointSize(12)
        checkbox_font.setBold(True)
        checkbox.setFont(checkbox_font)
        
        header_layout.addWidget(checkbox)
        header_layout.addStretch()
        
        layout.addLayout(header_layout)
        
        # Patterns input
        patterns_label = QLabel("Text Patterns (separate multiple patterns with commas):")
        patterns_label.setStyleSheet("font-weight: bold; margin-top: 10px;")
        layout.addWidget(patterns_label)
        
        patterns_text = QTextEdit()
        patterns_text.setMaximumHeight(100)
        patterns_text.setPlaceholderText(f"Enter patterns for {account_type}, e.g.:\nFREE {{amount}} {account_type.upper()}: {{code}}, {{amount}} {account_type.upper()}: {{code}}")
        
        # Set current patterns
        current_patterns = config.get("patterns", [])
        patterns_text.setPlainText(", ".join(current_patterns))
        
        layout.addWidget(patterns_text)
        
        # Help text
        help_text = QLabel("💡 Use {code} for the actual code (RESET3J, HAPPY4TH, ABC123, etc.)")
        help_text.setStyleSheet("color: #666; font-size: 11px; font-style: italic; margin-bottom: 10px;")
        help_text.setWordWrap(True)
        layout.addWidget(help_text)
        
        # Add separator line
        line = QFrame()
        line.setFrameShape(QFrame.Shape.HLine)
        line.setFrameShadow(QFrame.Shadow.Sunken)
        layout.addWidget(line)
        
        group.setLayout(layout)
        
        # Store widgets for later access
        self.account_widgets[account_type] = {
            "checkbox": checkbox,
            "patterns_text": patterns_text,
            "group": group
        }
        
        return group
    
    def load_patterns(self) -> Dict:
        """Load patterns from configuration file"""
        config_file = "pattern_config.json"
        
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    loaded_patterns = json.load(f)
                    # Merge with defaults to ensure all account types exist
                    for account_type, default_config in self.default_patterns.items():
                        if account_type not in loaded_patterns:
                            loaded_patterns[account_type] = default_config.copy()
                    return loaded_patterns
            except Exception as e:
                print(f"Error loading pattern config: {e}")
        
        return self.default_patterns.copy()
    
    def save_patterns_to_file(self, patterns: Dict):
        """Save patterns to configuration file"""
        config_file = "pattern_config.json"
        try:
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(patterns, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"Error saving pattern config: {e}")
    
    def get_current_patterns(self) -> Dict:
        """Get current patterns from the UI"""
        current_patterns = {}
        
        for account_type, widgets in self.account_widgets.items():
            checkbox = widgets["checkbox"]
            patterns_text = widgets["patterns_text"]
            
            # Parse patterns from text
            patterns_str = patterns_text.toPlainText().strip()
            if patterns_str:
                # Split by comma and clean up
                patterns = [p.strip() for p in patterns_str.split(',') if p.strip()]
            else:
                patterns = []
            
            current_patterns[account_type] = {
                "enabled": checkbox.isChecked(),
                "patterns": patterns
            }
        
        return current_patterns
    
    def reset_to_defaults(self):
        """Reset all patterns to defaults"""
        reply = QMessageBox.question(
            self, "Reset to Defaults",
            "Are you sure you want to reset all patterns to defaults?\nThis will overwrite your current configuration.",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            self.patterns = self.default_patterns.copy()
            self.update_ui_from_patterns()
    
    def update_ui_from_patterns(self):
        """Update UI widgets from current patterns"""
        for account_type, config in self.patterns.items():
            if account_type in self.account_widgets:
                widgets = self.account_widgets[account_type]
                widgets["checkbox"].setChecked(config.get("enabled", True))
                patterns = config.get("patterns", [])
                widgets["patterns_text"].setPlainText(", ".join(patterns))
    
    def test_patterns(self):
        """Test the current patterns with sample text"""
        current_patterns = self.get_current_patterns()
        
        # Sample test texts
        test_texts = [
            "FREE 50 STARTER: ABC123",
            "FREE 100 STARTER PLUS: XYZ789",
            "FREE 25 EXPERT ACCOUNT: DEF456",
            "GIVEAWAY 75 STARTER: GHI012",
            "100 EXPERT: JKL345",
            "This is not a valid code",
            "FREE 200 STARTER+: MNO678"
        ]
        
        # Import the pattern matching function
        try:
            from ocr_utils import create_regex_from_patterns
            
            results = []
            for account_type, config in current_patterns.items():
                if config["enabled"] and config["patterns"]:
                    regex_pattern = create_regex_from_patterns(config["patterns"])
                    results.append(f"=== {account_type} Patterns ===")
                    
                    for text in test_texts:
                        matches = regex_pattern.findall(text)
                        if matches:
                            results.append(f"✅ '{text}' → {matches}")
                        else:
                            results.append(f"❌ '{text}' → No match")
                    results.append("")
            
            # Show results
            result_text = "\n".join(results) if results else "No enabled patterns to test."
            
            msg = QMessageBox(self)
            msg.setWindowTitle("Pattern Test Results")
            msg.setText("Test results for your patterns:")
            msg.setDetailedText(result_text)
            msg.setStandardButtons(QMessageBox.StandardButton.Ok)
            msg.exec()
            
        except Exception as e:
            QMessageBox.warning(self, "Test Error", f"Error testing patterns: {str(e)}")
    
    def save_patterns(self):
        """Save the current patterns and close dialog"""
        current_patterns = self.get_current_patterns()
        
        # Validate patterns
        valid = True
        for account_type, config in current_patterns.items():
            if config["enabled"] and not config["patterns"]:
                QMessageBox.warning(
                    self, "Invalid Configuration",
                    f"Account type '{account_type}' is enabled but has no patterns.\n"
                    f"Please add patterns or disable this account type."
                )
                valid = False
                break
        
        if valid:
            self.patterns = current_patterns
            self.save_patterns_to_file(self.patterns)
            self.patterns_updated.emit(self.patterns)
            self.accept()


def show_pattern_config_dialog(parent=None) -> Dict:
    """Show the pattern configuration dialog and return the patterns"""
    dialog = PatternConfigDialog(parent)
    if dialog.exec() == QDialog.DialogCode.Accepted:
        return dialog.patterns
    return None


if __name__ == "__main__":
    # Test the dialog
    from PyQt6.QtWidgets import QApplication
    import sys
    
    app = QApplication(sys.argv)
    patterns = show_pattern_config_dialog()
    if patterns:
        print("Selected patterns:", patterns)
    else:
        print("Dialog cancelled")
