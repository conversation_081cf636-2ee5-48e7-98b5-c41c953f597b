# 🎯 Custom Pattern Configuration Guide

## 🎉 **New Feature: Customizable Account Type Detection**

Your MFFUHijack application now supports **fully customizable pattern detection** with account type checkboxes and user-defined text patterns!

## ✅ **What's Been Added**

### **1. Account Type Selection**
- ✅ **Starter** - Traditional starter accounts
- ✅ **Starter Plus** - Enhanced starter accounts  
- ✅ **Expert** - Expert level accounts
- ✅ **Enable/Disable** each account type individually

### **2. Custom Text Patterns**
- ✅ **Multiple patterns per account type** (separated by commas)
- ✅ **Flexible placeholders**: `{amount}` for dollar amounts, `{code}` for codes
- ✅ **Case-insensitive matching**
- ✅ **Real-time pattern testing**

### **3. Enhanced GUI**
- ✅ **"Configure Patterns" button** in Livestream Bot tab
- ✅ **Account types display** showing enabled types
- ✅ **Enhanced detection logging** with account type and amount
- ✅ **Pattern configuration dialog** with examples and testing

## 🎮 **How to Use**

### **Step 1: Open Pattern Configuration**
1. Start MFFUHijack: `python main.py`
2. Go to **Tab 1: Livestream Bot**
3. Click the **"Configure Patterns"** button (orange button)

### **Step 2: Configure Account Types**
For each account type (Starter, Starter Plus, Expert):

1. **Check the checkbox** to enable detection for that account type
2. **Enter text patterns** in the text area (separate multiple patterns with commas)
3. **Use placeholders**:
   - `{amount}` - Matches dollar amounts (50, 100, 200, etc.)
   - `{code}` - Matches alphanumeric codes (ABC123, XYZ-789, etc.)

### **Step 3: Example Patterns**

**Starter Account Patterns:**
```
FREE {amount} STARTER: {code}, {amount} STARTER: {code}, GIVEAWAY {amount} STARTER: {code}
```

**Starter Plus Account Patterns:**
```
FREE {amount} STARTER PLUS: {code}, FREE {amount} STARTER+: {code}, {amount} STARTER PLUS: {code}
```

**Expert Account Patterns:**
```
FREE {amount} EXPERT: {code}, FREE {amount} EXPERT ACCOUNT: {code}, {amount} EXPERT: {code}
```

### **Step 4: Test Your Patterns**
1. Click **"Test Patterns"** to see how your patterns match sample text
2. Review the test results to ensure patterns work as expected
3. Adjust patterns if needed

### **Step 5: Save and Apply**
1. Click **"Save & Apply"** to activate your new patterns
2. The main interface will update to show enabled account types
3. Start detecting codes with your custom patterns!

## 📊 **Test Results**

Based on our testing, the new pattern system successfully detects:

### **✅ Working Examples:**
- `FREE 50 STARTER: DEMO1234` → **Starter**, $50, Code: DEMO1234
- `FREE 100 STARTER PLUS: TEST-5678` → **Starter Plus**, $100, Code: TEST-5678  
- `FREE 25 EXPERT: SAMPLE9999` → **Expert**, $25, Code: SAMPLE9999
- `FREE 75 EXPERT ACCOUNT: EXPERT123` → **Expert**, $75, Code: EXPERT123
- `100 STARTER: SIMPLE456` → **Starter**, $100, Code: SIMPLE456
- `free 30 starter plus: lowercase456` → **Starter Plus**, $30, Code: lowercase456

### **Enhanced Detection Display:**
```
🎯 DETECTED: ABC123 | Type: Starter | Amount: $50 | Confidence: 0.92
🎯 DETECTED: XYZ789 | Type: Expert | Amount: $200 | Confidence: 0.95
```

## 🔧 **Advanced Configuration**

### **Pattern Syntax**
- **{amount}** - Matches any number (50, 100, 200, etc.)
- **{code}** - Matches alphanumeric codes with optional hyphens (ABC123, XYZ-789)
- **Case insensitive** - "FREE" matches "free", "Free", "FREE"
- **Flexible spacing** - Handles extra spaces automatically

### **Multiple Patterns**
Separate multiple patterns with commas:
```
FREE {amount} STARTER: {code}, {amount} STARTER: {code}, GIVEAWAY {amount} STARTER: {code}
```

### **Pattern Examples**
```
Pattern: "FREE {amount} STARTER: {code}"
Matches: "FREE 50 STARTER: ABC123"
Extracts: amount=50, code=ABC123

Pattern: "{amount} EXPERT ACCOUNT: {code}"  
Matches: "200 EXPERT ACCOUNT: XYZ789"
Extracts: amount=200, code=XYZ789

Pattern: "GIVEAWAY {amount} STARTER PLUS: {code}"
Matches: "GIVEAWAY 100 STARTER PLUS: DEF456"
Extracts: amount=100, code=DEF456
```

## 💾 **Configuration Storage**

Your patterns are automatically saved to `pattern_config.json`:
```json
{
  "Starter": {
    "enabled": true,
    "patterns": [
      "FREE {amount} STARTER: {code}",
      "{amount} STARTER: {code}"
    ]
  },
  "Starter Plus": {
    "enabled": true,
    "patterns": [
      "FREE {amount} STARTER PLUS: {code}",
      "FREE {amount} STARTER+: {code}"
    ]
  },
  "Expert": {
    "enabled": true,
    "patterns": [
      "FREE {amount} EXPERT: {code}",
      "FREE {amount} EXPERT ACCOUNT: {code}"
    ]
  }
}
```

## 🎯 **Benefits**

### **Flexibility**
- ✅ **Customize for any giveaway format**
- ✅ **Enable only the account types you want**
- ✅ **Add multiple pattern variations**
- ✅ **Easy to modify without code changes**

### **Accuracy**
- ✅ **Precise pattern matching**
- ✅ **Reduced false positives**
- ✅ **Account type identification**
- ✅ **Amount extraction**

### **User Experience**
- ✅ **Visual pattern configuration**
- ✅ **Real-time testing**
- ✅ **Clear detection logging**
- ✅ **Persistent settings**

## 🚀 **Ready to Use**

Your MFFUHijack application now supports:

1. **✅ Custom account type selection** (Starter, Starter Plus, Expert)
2. **✅ User-defined text patterns** with placeholders
3. **✅ Multiple patterns per account type**
4. **✅ Visual configuration interface**
5. **✅ Enhanced detection logging**
6. **✅ Pattern testing and validation**

## 🎮 **Quick Start**

1. **Start the app**: `python main.py`
2. **Click "Configure Patterns"** in the Livestream Bot tab
3. **Enable desired account types** with checkboxes
4. **Enter your custom patterns** using `{amount}` and `{code}` placeholders
5. **Test your patterns** to verify they work
6. **Save & Apply** to start using your custom detection

Your MFFUHijack application is now **fully customizable** for any giveaway format! 🎉
