#!/usr/bin/env python3
"""
Test script for MFFUHijack functionality
Tests core components without requiring GUI
"""

import sys
import os
import cv2
import numpy as np
from datetime import datetime

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import our modules
from ocr_utils import ocr_manager
from submitter import code_submitter


def test_regex_patterns():
    """Test the custom pattern matching"""
    print("=" * 50)
    print("TESTING CUSTOM PATTERNS")
    print("=" * 50)

    # Test texts for different account types
    test_texts = [
        "FREE 50 STARTER: DEMO1234",
        "FREE 100 STARTER PLUS: TEST-5678",
        "FREE 25 EXPERT: SAMPLE9999",
        "FREE 75 EXPERT ACCOUNT: EXPERT123",
        "100 STARTER: SIMPLE456",
        "50 STARTER+: PLUS789",
        "This is not a code",
        "GIVEAWAY 200 STARTER: GIVE012",
        "free 30 starter plus: lowercase456",  # Should match (case insensitive)
    ]

    print("Enabled account types:", ocr_manager.get_enabled_account_types())
    print()

    for text in test_texts:
        # Create a mock OCR result
        mock_result = [{'text': text, 'confidence': 0.95}]
        codes = ocr_manager.find_giveaway_codes(mock_result)

        if codes:
            for code_data in codes:
                print(f"✓ MATCH: '{text}'")
                print(f"    Code: {code_data['code']}")
                print(f"    Type: {code_data['type']}")
                print(f"    Amount: ${code_data.get('amount', 'Unknown')}")
                print(f"    Confidence: {code_data['confidence']:.2f}")
        else:
            print(f"✗ NO MATCH: '{text}'")

    print()


def test_ocr_engines():
    """Test available OCR engines"""
    print("=" * 50)
    print("TESTING OCR ENGINES")
    print("=" * 50)
    
    available_engines = ocr_manager.get_available_engines()
    print(f"Available OCR engines: {available_engines}")
    
    for engine in available_engines:
        success = ocr_manager.set_engine(engine)
        print(f"Setting engine to {engine}: {'✓' if success else '✗'}")
    
    print(f"Current engine: {ocr_manager.current_engine}")
    print()


def create_test_image_with_code():
    """Create a test image with a code for OCR testing"""
    # Create a simple test image with text
    img = np.ones((200, 600, 3), dtype=np.uint8) * 255  # White background
    
    # Add some text using OpenCV
    font = cv2.FONT_HERSHEY_SIMPLEX
    text = "FREE 50 STARTER: TEST1234"
    
    # Calculate text size and position
    text_size = cv2.getTextSize(text, font, 1, 2)[0]
    text_x = (img.shape[1] - text_size[0]) // 2
    text_y = (img.shape[0] + text_size[1]) // 2
    
    # Draw text
    cv2.putText(img, text, (text_x, text_y), font, 1, (0, 0, 0), 2)
    
    return img


def test_ocr_processing():
    """Test OCR processing with a test image"""
    print("=" * 50)
    print("TESTING OCR PROCESSING")
    print("=" * 50)
    
    # Create test image
    test_image = create_test_image_with_code()
    
    # Save test image for inspection
    cv2.imwrite("test_image.jpg", test_image)
    print("Created test image: test_image.jpg")
    
    # Test OCR processing
    available_engines = ocr_manager.get_available_engines()
    
    if not available_engines:
        print("✗ No OCR engines available for testing")
        return
    
    for engine in available_engines:
        print(f"\nTesting with {engine}:")
        ocr_manager.set_engine(engine)
        
        try:
            # Extract text
            text_results = ocr_manager.extract_text_from_image(test_image, "full")
            print(f"  Text results: {len(text_results)} detections")
            
            for result in text_results:
                print(f"    Text: '{result.get('text', '')}' (confidence: {result.get('confidence', 0):.2f})")
            
            # Find codes
            codes = ocr_manager.find_giveaway_codes(text_results)
            print(f"  Codes found: {len(codes)}")
            
            for code_data in codes:
                print(f"    Code: {code_data['code']} (Type: {code_data['type']})")
        
        except Exception as e:
            print(f"  ✗ Error with {engine}: {e}")
    
    print()


def test_code_submission():
    """Test code submission functionality"""
    print("=" * 50)
    print("TESTING CODE SUBMISSION")
    print("=" * 50)
    
    # Test with mock data
    test_codes = [
        {
            'code': 'TEST1234',
            'type': 'STARTER',
            'confidence': 0.95,
            'timestamp': datetime.now().isoformat()
        },
        {
            'code': 'TEST1234',  # Duplicate
            'type': 'STARTER',
            'confidence': 0.90,
            'timestamp': datetime.now().isoformat()
        },
        {
            'code': 'DEMO5678',
            'type': 'RESETS',
            'confidence': 0.88,
            'timestamp': datetime.now().isoformat()
        }
    ]
    
    for code_data in test_codes:
        print(f"Submitting code: {code_data['code']}")
        result = code_submitter.submit_code(code_data)
        
        if result.get('success'):
            print(f"  ✓ Success: {result.get('response', {}).get('message', 'Submitted')}")
        elif result.get('duplicate'):
            print(f"  ⚠ Duplicate: Code already submitted")
        else:
            print(f"  ✗ Failed: {result.get('error', 'Unknown error')}")
    
    # Get submission stats
    stats = code_submitter.get_submission_stats()
    print(f"\nSubmission stats:")
    print(f"  Total submitted: {stats['total_submitted']}")
    print(f"  Submitted codes: {stats['submitted_codes']}")
    
    print()


def test_image_cropping():
    """Test image cropping functionality"""
    print("=" * 50)
    print("TESTING IMAGE CROPPING")
    print("=" * 50)
    
    # Create test image
    test_image = create_test_image_with_code()
    height, width = test_image.shape[:2]
    print(f"Original image size: {width}x{height}")
    
    regions = ["bottom_third", "bottom_center", "full"]
    
    for region in regions:
        cropped = ocr_manager.crop_region_of_interest(test_image, region)
        crop_height, crop_width = cropped.shape[:2]
        print(f"  {region}: {crop_width}x{crop_height}")
        
        # Save cropped image
        filename = f"test_crop_{region}.jpg"
        cv2.imwrite(filename, cropped)
        print(f"    Saved: {filename}")
    
    print()


def main():
    """Run all tests"""
    print("MFFUHijack Functionality Test")
    print("=" * 50)
    
    try:
        test_regex_patterns()
        test_ocr_engines()
        test_image_cropping()
        test_ocr_processing()
        test_code_submission()
        
        print("=" * 50)
        print("ALL TESTS COMPLETED")
        print("=" * 50)
        
        # Cleanup test files
        test_files = [
            "test_image.jpg",
            "test_crop_bottom_third.jpg",
            "test_crop_bottom_center.jpg",
            "test_crop_full.jpg"
        ]
        
        for file in test_files:
            if os.path.exists(file):
                try:
                    os.remove(file)
                    print(f"Cleaned up: {file}")
                except:
                    pass
    
    except KeyboardInterrupt:
        print("\nTest interrupted by user")
    except Exception as e:
        print(f"Test failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
