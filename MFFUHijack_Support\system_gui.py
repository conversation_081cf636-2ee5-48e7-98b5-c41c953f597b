"""
System Default GUI for MFFUHijack
Clean, functional design using system default styling with proper sizing
"""

import logging
from datetime import datetime
from PyQt6.QtWidgets import (
    QMainWindow, QTabWidget, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QLineEdit, QPushButton, QComboBox, QTextEdit,
    QProgressBar, QGroupBox, QGridLayout, QCheckBox, QFrame,
    QMenuBar, QMenu, QMessageBox, QSizePolicy
)
from PyQt6.QtCore import Qt, pyqtSignal, QObject, QTimer
from PyQt6.QtGui import QPixmap, QAction, QFont

# Import our modules
from ocr_utils import ocr_manager

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class MessageLogger(QObject):
    """Simple message logger"""
    
    message_logged = pyqtSignal(str, str)  # message, level
    
    def __init__(self):
        super().__init__()
    
    def log(self, message: str, level: str = "INFO"):
        """Log a message"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {level}: {message}"
        self.message_logged.emit(formatted_message, level)
    
    def info(self, message: str):
        self.log(message, "INFO")
    
    def warning(self, message: str):
        self.log(message, "WARNING")
    
    def error(self, message: str):
        self.log(message, "ERROR")
    
    def success(self, message: str):
        self.log(message, "SUCCESS")


# Global message logger
message_logger = MessageLogger()


class SystemMessageLoggerWindow(QMainWindow):
    """System default message logger window"""

    def __init__(self, logger: MessageLogger):
        super().__init__()
        self.logger = logger
        self.messages = []
        self.init_ui()
        self.connect_signals()

    def init_ui(self):
        """Initialize logger window with proper sizing"""
        self.setWindowTitle("MFFUHijack - Message Logger")
        self.setMinimumSize(800, 600)
        self.resize(800, 600)

        # Central widget
        central = QWidget()
        self.setCentralWidget(central)

        # Main layout with proper margins
        layout = QVBoxLayout(central)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)

        # Header
        header = QLabel("Message Logger")
        header_font = QFont()
        header_font.setPointSize(14)
        header_font.setBold(True)
        header.setFont(header_font)
        layout.addWidget(header)

        # Controls with proper spacing
        controls_layout = QHBoxLayout()
        controls_layout.setSpacing(10)
        
        clear_btn = QPushButton("Clear Messages")
        clear_btn.setMinimumSize(120, 30)
        clear_btn.clicked.connect(self.clear_messages)
        controls_layout.addWidget(clear_btn)

        self.auto_scroll = QCheckBox("Auto-scroll to latest")
        self.auto_scroll.setChecked(True)
        controls_layout.addWidget(self.auto_scroll)

        controls_layout.addStretch()
        layout.addLayout(controls_layout)

        # Message display with proper sizing
        self.display = QTextEdit()
        self.display.setReadOnly(True)
        self.display.setMinimumHeight(400)
        layout.addWidget(self.display)

        # Status bar
        self.status = QLabel("Messages: 0")
        layout.addWidget(self.status)

    def connect_signals(self):
        """Connect signals"""
        self.logger.message_logged.connect(self.add_message)

    def add_message(self, message: str, level: str):
        """Add message to display"""
        self.messages.append((message, level))
        
        # Simple text formatting
        display_message = f"{message}"
        if level == "ERROR":
            display_message = f"ERROR: {message}"
        elif level == "WARNING":
            display_message = f"WARNING: {message}"
        elif level == "SUCCESS":
            display_message = f"SUCCESS: {message}"
        
        self.display.append(display_message)
        
        if self.auto_scroll.isChecked():
            scrollbar = self.display.verticalScrollBar()
            scrollbar.setValue(scrollbar.maximum())
        
        self.status.setText(f"Messages: {len(self.messages)}")

    def clear_messages(self):
        """Clear all messages"""
        self.messages.clear()
        self.display.clear()
        self.status.setText("Messages: 0")


class SystemLivestreamTab(QWidget):
    """System default livestream bot interface"""

    # Signals
    start_bot_signal = pyqtSignal(str, str, float)
    stop_bot_signal = pyqtSignal()

    def __init__(self):
        super().__init__()
        self.ocr_region = (0, 67, 100, 33)
        self.init_ui()

    def init_ui(self):
        """Initialize livestream tab with proper spacing"""
        # Main layout with proper margins
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(15)

        # Configuration section
        config_group = QGroupBox("Bot Configuration")
        config_layout = QGridLayout(config_group)
        config_layout.setHorizontalSpacing(10)
        config_layout.setVerticalSpacing(10)
        config_layout.setContentsMargins(15, 20, 15, 15)

        # Row 0: URL input with proper sizing
        config_layout.addWidget(QLabel("Livestream URL:"), 0, 0)
        self.url_input = QLineEdit()
        self.url_input.setMinimumHeight(25)
        self.url_input.setPlaceholderText("https://www.youtube.com/watch?v=...")
        self.url_input.setText("https://www.youtube.com/@MyFundedFuturesPropFirm/streams")
        config_layout.addWidget(self.url_input, 0, 1, 1, 3)

        # Row 1: OCR settings with proper spacing
        config_layout.addWidget(QLabel("OCR Engine:"), 1, 0)
        self.ocr_combo = QComboBox()
        self.ocr_combo.setMinimumHeight(25)
        self.ocr_combo.addItems(["EasyOCR", "Custom"])
        config_layout.addWidget(self.ocr_combo, 1, 1)

        config_layout.addWidget(QLabel("Interval (seconds):"), 1, 2)
        self.interval_input = QLineEdit("1.0")
        self.interval_input.setMinimumHeight(25)
        self.interval_input.setMaximumWidth(80)
        config_layout.addWidget(self.interval_input, 1, 3)

        # Row 2: Pattern and region buttons with proper sizing
        self.pattern_btn = QPushButton("Configure Patterns")
        self.pattern_btn.setMinimumSize(140, 30)
        self.pattern_btn.clicked.connect(self.configure_patterns)
        config_layout.addWidget(self.pattern_btn, 2, 0)

        self.region_btn = QPushButton("Select OCR Region")
        self.region_btn.setMinimumSize(140, 30)
        self.region_btn.clicked.connect(self.select_region)
        config_layout.addWidget(self.region_btn, 2, 1)

        # Row 3: Status labels with proper sizing
        self.account_types_label = QLabel("Account Types: Loading...")
        self.account_types_label.setWordWrap(True)
        self.account_types_label.setMinimumHeight(25)
        config_layout.addWidget(self.account_types_label, 3, 0, 1, 2)

        self.region_label = QLabel("Region: Bottom third (0%, 67%, 100% × 33%)")
        self.region_label.setMinimumHeight(25)
        config_layout.addWidget(self.region_label, 3, 2, 1, 2)

        layout.addWidget(config_group)

        # Control section with proper spacing
        control_group = QGroupBox("Bot Control")
        control_layout = QHBoxLayout(control_group)
        control_layout.setContentsMargins(15, 20, 15, 15)
        control_layout.setSpacing(15)

        # Start button
        self.start_btn = QPushButton("Start Bot")
        self.start_btn.setMinimumSize(120, 35)
        self.start_btn.clicked.connect(self.start_bot)
        control_layout.addWidget(self.start_btn)

        # Stop button
        self.stop_btn = QPushButton("Stop Bot")
        self.stop_btn.setMinimumSize(120, 35)
        self.stop_btn.setEnabled(False)
        self.stop_btn.clicked.connect(self.stop_bot)
        control_layout.addWidget(self.stop_btn)

        # Status label with proper sizing
        self.status_label = QLabel("Status: Idle")
        self.status_label.setMinimumHeight(35)
        control_layout.addWidget(self.status_label)
        control_layout.addStretch()

        layout.addWidget(control_group)

        # Preview section with proper sizing
        preview_group = QGroupBox("Live Preview")
        preview_layout = QVBoxLayout(preview_group)
        preview_layout.setContentsMargins(15, 20, 15, 15)

        self.frame_preview = QLabel("No frame loaded - Start bot to see preview")
        self.frame_preview.setMinimumSize(500, 300)
        self.frame_preview.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.frame_preview.setScaledContents(True)
        self.frame_preview.setFrameStyle(QFrame.Shape.Box)
        preview_layout.addWidget(self.frame_preview)

        layout.addWidget(preview_group)

        # Activity log with proper sizing
        activity_group = QGroupBox("Activity Log")
        activity_layout = QVBoxLayout(activity_group)
        activity_layout.setContentsMargins(15, 20, 15, 15)

        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(120)
        self.log_text.setMinimumHeight(120)
        self.log_text.setReadOnly(True)
        self.log_text.setPlaceholderText("Activity messages will appear here...")
        activity_layout.addWidget(self.log_text)

        layout.addWidget(activity_group)

        # Update displays
        self.update_account_types_display()

    def update_account_types_display(self):
        """Update account types display"""
        try:
            enabled_types = ocr_manager.get_enabled_account_types()
            if enabled_types:
                types_text = ", ".join(enabled_types)
                self.account_types_label.setText(f"Enabled Account Types: {types_text}")
            else:
                self.account_types_label.setText("No account types configured")
        except Exception:
            self.account_types_label.setText("Error loading account types")

    def configure_patterns(self):
        """Configure detection patterns"""
        try:
            from pattern_config_dialog import show_pattern_config_dialog
            new_patterns = show_pattern_config_dialog(self)
            if new_patterns:
                ocr_manager.update_pattern_config(new_patterns)
                self.update_account_types_display()
                self.log_message("Pattern configuration updated successfully")
        except Exception as e:
            self.log_message(f"Error configuring patterns: {str(e)}")

    def select_region(self):
        """Select OCR region"""
        try:
            from region_selector import RegionSelectorWidget
            from PyQt6.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout
            
            dialog = QDialog(self)
            dialog.setWindowTitle("Select OCR Region")
            dialog.setModal(True)
            dialog.resize(800, 600)
            
            layout = QVBoxLayout(dialog)
            layout.setContentsMargins(10, 10, 10, 10)
            layout.setSpacing(10)
            
            region_selector = RegionSelectorWidget()
            region_selector.set_region_percent(*self.ocr_region)
            
            # Create sample frame
            import numpy as np
            import cv2
            sample_frame = np.ones((720, 1280, 3), dtype=np.uint8) * 240
            sample_frame[:, :160] = 0
            sample_frame[:, 1120:] = 0
            cv2.putText(sample_frame, "SAMPLE LIVESTREAM", (400, 100),
                       cv2.FONT_HERSHEY_SIMPLEX, 1.5, (255, 255, 255), 2)
            cv2.putText(sample_frame, "x5 FREE RESETS USE CODE: RESET3J", (300, 600),
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
            
            region_selector.set_frame(sample_frame)
            layout.addWidget(region_selector)
            
            # Buttons with proper spacing
            button_layout = QHBoxLayout()
            button_layout.setSpacing(10)
            
            apply_btn = QPushButton("Apply Region")
            apply_btn.setMinimumSize(100, 30)
            apply_btn.clicked.connect(lambda: self.apply_region(region_selector, dialog))
            button_layout.addWidget(apply_btn)
            
            cancel_btn = QPushButton("Cancel")
            cancel_btn.setMinimumSize(100, 30)
            cancel_btn.clicked.connect(dialog.reject)
            button_layout.addWidget(cancel_btn)
            
            button_layout.addStretch()
            layout.addLayout(button_layout)
            dialog.exec()
            
        except Exception as e:
            self.log_message(f"Error opening region selector: {str(e)}")

    def apply_region(self, region_selector, dialog):
        """Apply selected region"""
        self.ocr_region = region_selector.get_region_percent()
        x, y, w, h = self.ocr_region
        self.region_label.setText(f"Region: {x:.1f}%, {y:.1f}%, {w:.1f}% × {h:.1f}%")
        self.log_message(f"OCR region updated: {x:.1f}%, {y:.1f}%, {w:.1f}% × {h:.1f}%")
        dialog.accept()

    def start_bot(self):
        """Start the bot"""
        url = self.url_input.text().strip()
        if not url:
            self.log_message("ERROR: Please enter a livestream URL")
            return

        ocr_engine = self.ocr_combo.currentText()
        try:
            interval = float(self.interval_input.text())
        except ValueError:
            interval = 1.0
            self.interval_input.setText("1.0")

        # Update UI
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        self.status_label.setText("Status: Starting...")

        # Emit signal
        self.start_bot_signal.emit(url, ocr_engine, interval)
        self.log_message(f"Starting bot with URL: {url}")

    def stop_bot(self):
        """Stop the bot"""
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.status_label.setText("Status: Stopping...")
        self.stop_bot_signal.emit()
        self.log_message("Stopping bot...")

    def update_status(self, status: str):
        """Update status label"""
        self.status_label.setText(f"Status: {status}")

    def update_frame_preview(self, pixmap: QPixmap):
        """Update frame preview"""
        self.frame_preview.setPixmap(pixmap)

    def log_message(self, message: str):
        """Add message to activity log"""
        self.log_text.append(message)
        scrollbar = self.log_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())

        # Send to centralized logger
        if "ERROR" in message.upper():
            message_logger.error(message)
        elif "WARNING" in message.upper():
            message_logger.warning(message)
        elif "successfully" in message.lower() or "updated" in message.lower():
            message_logger.success(message)
        else:
            message_logger.info(message)


class SystemDatasetTab(QWidget):
    """System default dataset building interface"""

    # Signals
    start_dataset_signal = pyqtSignal(str, int, str)
    start_training_signal = pyqtSignal()

    def __init__(self):
        super().__init__()
        self.init_ui()

    def init_ui(self):
        """Initialize dataset tab with proper spacing"""
        # Main layout with proper margins
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(15)

        # Dataset building section
        dataset_group = QGroupBox("Dataset Building")
        dataset_layout = QGridLayout(dataset_group)
        dataset_layout.setHorizontalSpacing(10)
        dataset_layout.setVerticalSpacing(10)
        dataset_layout.setContentsMargins(15, 20, 15, 15)

        # Row 0: Channel URL with proper sizing
        dataset_layout.addWidget(QLabel("YouTube Channel:"), 0, 0)
        self.channel_input = QLineEdit()
        self.channel_input.setMinimumHeight(25)
        self.channel_input.setPlaceholderText("https://www.youtube.com/@channel")
        dataset_layout.addWidget(self.channel_input, 0, 1, 1, 3)

        # Row 1: Target frames and region with proper spacing
        dataset_layout.addWidget(QLabel("Target Frames:"), 1, 0)
        self.frames_input = QLineEdit("100")
        self.frames_input.setMinimumHeight(25)
        self.frames_input.setMaximumWidth(80)
        dataset_layout.addWidget(self.frames_input, 1, 1)

        dataset_layout.addWidget(QLabel("Region:"), 1, 2)
        self.region_combo = QComboBox()
        self.region_combo.setMinimumHeight(25)
        self.region_combo.addItems(["Full Frame", "Bottom Third", "Custom"])
        self.region_combo.setCurrentText("Bottom Third")
        dataset_layout.addWidget(self.region_combo, 1, 3)

        # Row 2: Start button and progress with proper sizing
        self.start_dataset_btn = QPushButton("Start Dataset Building")
        self.start_dataset_btn.setMinimumSize(160, 30)
        self.start_dataset_btn.clicked.connect(self.start_dataset_building)
        dataset_layout.addWidget(self.start_dataset_btn, 2, 0, 1, 2)

        self.dataset_progress = QProgressBar()
        self.dataset_progress.setMinimumHeight(25)
        self.dataset_progress.setVisible(False)
        dataset_layout.addWidget(self.dataset_progress, 2, 2, 1, 2)

        layout.addWidget(dataset_group)

        # Training section
        training_group = QGroupBox("Model Training")
        training_layout = QGridLayout(training_group)
        training_layout.setHorizontalSpacing(10)
        training_layout.setVerticalSpacing(10)
        training_layout.setContentsMargins(15, 20, 15, 15)

        # Row 0: Training parameters with proper spacing
        training_layout.addWidget(QLabel("Epochs:"), 0, 0)
        self.epochs_input = QLineEdit("10")
        self.epochs_input.setMinimumHeight(25)
        self.epochs_input.setMaximumWidth(60)
        training_layout.addWidget(self.epochs_input, 0, 1)

        training_layout.addWidget(QLabel("Batch Size:"), 0, 2)
        self.batch_input = QLineEdit("32")
        self.batch_input.setMinimumHeight(25)
        self.batch_input.setMaximumWidth(60)
        training_layout.addWidget(self.batch_input, 0, 3)

        # Row 1: Start training button and progress with proper sizing
        self.start_training_btn = QPushButton("Start Training")
        self.start_training_btn.setMinimumSize(120, 30)
        self.start_training_btn.clicked.connect(self.start_training)
        training_layout.addWidget(self.start_training_btn, 1, 0, 1, 2)

        self.training_progress = QProgressBar()
        self.training_progress.setMinimumHeight(25)
        self.training_progress.setVisible(False)
        training_layout.addWidget(self.training_progress, 1, 2, 1, 2)

        layout.addWidget(training_group)

        # Progress log with proper sizing
        log_group = QGroupBox("Progress Log")
        log_layout = QVBoxLayout(log_group)
        log_layout.setContentsMargins(15, 20, 15, 15)

        self.progress_log = QTextEdit()
        self.progress_log.setReadOnly(True)
        self.progress_log.setMinimumHeight(200)
        self.progress_log.setPlaceholderText("Progress messages will appear here...")
        log_layout.addWidget(self.progress_log)

        layout.addWidget(log_group)

    def start_dataset_building(self):
        """Start dataset building"""
        channel_url = self.channel_input.text().strip()
        if not channel_url:
            self.log_progress("ERROR: Please enter a YouTube channel URL")
            return

        try:
            num_frames = int(self.frames_input.text())
        except ValueError:
            num_frames = 100
            self.frames_input.setText("100")

        region = self.region_combo.currentText()

        # Update UI
        self.start_dataset_btn.setEnabled(False)
        self.dataset_progress.setVisible(True)
        self.dataset_progress.setValue(0)

        # Emit signal
        self.start_dataset_signal.emit(channel_url, num_frames, region)
        self.log_progress(f"Starting dataset building for: {channel_url}")
        self.log_progress(f"Target frames: {num_frames}, Region: {region}")

    def start_training(self):
        """Start model training"""
        try:
            epochs = int(self.epochs_input.text())
            batch_size = int(self.batch_input.text())
        except ValueError:
            epochs = 10
            batch_size = 32
            self.epochs_input.setText("10")
            self.batch_input.setText("32")

        # Update UI
        self.start_training_btn.setEnabled(False)
        self.training_progress.setVisible(True)
        self.training_progress.setValue(0)

        # Emit signal
        self.start_training_signal.emit()
        self.log_progress(f"Starting model training...")
        self.log_progress(f"Epochs: {epochs}, Batch size: {batch_size}")

    def reset_ui_state(self):
        """Reset UI to initial state"""
        self.start_dataset_btn.setEnabled(True)
        self.start_training_btn.setEnabled(True)
        self.dataset_progress.setVisible(False)
        self.training_progress.setVisible(False)

    def update_dataset_progress(self, value: int):
        """Update dataset progress"""
        self.dataset_progress.setValue(value)

    def update_training_progress(self, value: int):
        """Update training progress"""
        self.training_progress.setValue(value)

    def log_progress(self, message: str):
        """Add message to progress log"""
        self.progress_log.append(message)
        scrollbar = self.progress_log.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())

        # Send to centralized logger
        if "ERROR" in message.upper():
            message_logger.error(message)
        elif "WARNING" in message.upper():
            message_logger.warning(message)
        elif "complete" in message.lower() or "finished" in message.lower():
            message_logger.success(message)
        else:
            message_logger.info(message)


class SystemMFFUHijackGUI(QMainWindow):
    """System default main application window with proper sizing"""

    def __init__(self):
        super().__init__()
        self.message_logger_window = None
        self.live_scan_monitor = None
        self.init_ui()
        self.connect_signals()

    def init_ui(self):
        """Initialize main window with proper sizing and spacing"""
        self.setWindowTitle("MFFUHijack - Real-Time OCR Livestream Code Detection")
        self.setMinimumSize(1000, 750)
        self.resize(1000, 750)

        # Create menu bar
        self.create_menu_bar()

        # Central widget
        central = QWidget()
        self.setCentralWidget(central)

        # Main layout with proper margins and spacing
        layout = QVBoxLayout(central)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)

        # Header with proper sizing
        header = self.create_header()
        layout.addWidget(header)

        # Tab widget with proper sizing
        self.tab_widget = QTabWidget()
        self.tab_widget.setMinimumHeight(600)

        # Create tabs
        self.livestream_tab = SystemLivestreamTab()
        self.dataset_tab = SystemDatasetTab()

        # Add tabs
        self.tab_widget.addTab(self.livestream_tab, "Livestream Bot")
        self.tab_widget.addTab(self.dataset_tab, "Dataset & Training")

        layout.addWidget(self.tab_widget)

        # Create message logger window
        self.message_logger_window = SystemMessageLoggerWindow(message_logger)
        self.message_logger_window.show()
        self.logger_action.setChecked(True)

        # Position logger window
        self.position_logger_window()

        # Log startup
        message_logger.info("MFFUHijack started with system default interface")

    def create_header(self):
        """Create header with proper sizing"""
        header = QFrame()
        header.setFrameStyle(QFrame.Shape.StyledPanel)
        header.setMaximumHeight(80)
        header.setMinimumHeight(80)

        layout = QHBoxLayout(header)
        layout.setContentsMargins(15, 10, 15, 10)
        layout.setSpacing(15)

        # Title
        title = QLabel("MFFUHijack")
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title.setFont(title_font)
        layout.addWidget(title)

        # Subtitle
        subtitle = QLabel("Real-Time OCR Livestream Code Detection")
        subtitle_font = QFont()
        subtitle_font.setPointSize(10)
        subtitle.setFont(subtitle_font)
        layout.addWidget(subtitle)

        layout.addStretch()

        # Status indicator
        self.status_indicator = QLabel("Status: Ready")
        status_font = QFont()
        status_font.setBold(True)
        self.status_indicator.setFont(status_font)
        self.status_indicator.setMinimumHeight(30)
        layout.addWidget(self.status_indicator)

        return header

    def create_menu_bar(self):
        """Create menu bar"""
        menubar = self.menuBar()

        # View menu
        view_menu = menubar.addMenu("View")

        self.logger_action = QAction("Message Logger", self)
        self.logger_action.setCheckable(True)
        self.logger_action.triggered.connect(self.toggle_logger)
        view_menu.addAction(self.logger_action)

        # Help menu
        help_menu = menubar.addMenu("Help")

        about_action = QAction("About", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

    def position_logger_window(self):
        """Position logger window relative to main window"""
        main_geometry = self.geometry()
        logger_x = main_geometry.x() + main_geometry.width() + 20
        logger_y = main_geometry.y()
        self.message_logger_window.setGeometry(logger_x, logger_y, 800, 600)

    def connect_signals(self):
        """Connect tab signals"""
        # Create Live Scan Monitor window
        try:
            from live_scan_monitor import LiveScanMonitorWindow
            self.live_scan_monitor = LiveScanMonitorWindow()
        except ImportError:
            message_logger.warning("Live Scan Monitor not available")

        # Livestream tab signals
        self.livestream_tab.start_bot_signal.connect(self.start_livestream_bot)
        self.livestream_tab.stop_bot_signal.connect(self.stop_livestream_bot)

        # Dataset tab signals
        self.dataset_tab.start_dataset_signal.connect(self.start_dataset_builder)
        self.dataset_tab.start_training_signal.connect(self.start_model_training)

    def toggle_logger(self):
        """Toggle message logger window"""
        if self.logger_action.isChecked():
            self.message_logger_window.show()
            self.position_logger_window()
        else:
            self.message_logger_window.hide()

    def show_about(self):
        """Show about dialog"""
        QMessageBox.about(self, "About MFFUHijack",
                         "MFFUHijack v2.0\n\n"
                         "Real-Time OCR Livestream Code Detection\n"
                         "System Default Interface")

    # LiveBot integration methods
    def start_livestream_bot(self, url: str, engine: str, interval: float):
        """Start livestream bot with monitor window"""
        try:
            from live_bot import LiveBot
            if not hasattr(self, 'live_bot') or self.live_bot is None:
                self.live_bot = LiveBot()
                if self.live_scan_monitor:
                    self.live_bot.set_monitor_window(self.live_scan_monitor)

            # Start the bot
            success = self.live_bot.start(url, engine, interval)
            if success:
                message_logger.info(f"Started livestream bot: {url}")
                self.status_indicator.setText("Status: Bot Running")
            else:
                message_logger.error("Failed to start livestream bot")
                self.status_indicator.setText("Status: Error")
        except Exception as e:
            message_logger.error(f"Error starting livestream bot: {e}")
            self.status_indicator.setText("Status: Error")

    def stop_livestream_bot(self):
        """Stop livestream bot"""
        try:
            if hasattr(self, 'live_bot') and self.live_bot:
                self.live_bot.stop()
                message_logger.info("Stopped livestream bot")
                self.status_indicator.setText("Status: Ready")
        except Exception as e:
            message_logger.error(f"Error stopping livestream bot: {e}")

    def start_dataset_builder(self, url: str, frames: int, region: str):
        """Start dataset builder (placeholder)"""
        message_logger.info(f"Starting dataset builder: {url}")
        self.status_indicator.setText("Status: Building Dataset")

    def start_model_training(self):
        """Start model training (placeholder)"""
        message_logger.info("Starting model training")
        self.status_indicator.setText("Status: Training Model")
