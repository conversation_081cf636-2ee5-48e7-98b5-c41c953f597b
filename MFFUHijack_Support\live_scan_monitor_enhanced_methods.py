"""
Enhanced methods for the Live Scan Monitor Window
This file contains additional methods that extend the LiveScanMonitorWindow class
"""

from PyQt6.QtWidgets import *
from PyQt6.QtCore import *
from PyQt6.QtGui import *
import os
import json
import csv
from datetime import datetime


class LiveScanMonitorEnhancedMethods:
    """Mixin class with enhanced methods for LiveScanMonitorWindow"""
    
    def create_footer_section(self, parent_layout):
        """Create enhanced footer with session controls and info"""
        footer_frame = QFrame()
        footer_frame.setFrameStyle(QFrame.Shape.StyledPanel)
        footer_layout = QHBoxLayout(footer_frame)
        
        # Session info
        session_info_layout = QVBoxLayout()
        
        # Session duration
        self.session_duration_label = QLabel("Session Duration: 00:00:00")
        self.session_duration_label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        session_info_layout.addWidget(self.session_duration_label)
        
        # Current session ID
        self.session_id_label = QLabel("Session ID: None")
        self.session_id_label.setStyleSheet("color: #666; font-size: 9px;")
        session_info_layout.addWidget(self.session_id_label)
        
        footer_layout.addLayout(session_info_layout)
        
        footer_layout.addStretch()
        
        # Control buttons
        controls_layout = QHBoxLayout()
        
        # Reset statistics button
        reset_btn = QPushButton("🔄 Reset Stats")
        reset_btn.clicked.connect(self.reset_statistics)
        controls_layout.addWidget(reset_btn)
        
        # Export button
        export_btn = QPushButton("💾 Export")
        export_btn.clicked.connect(self.show_export_menu)
        controls_layout.addWidget(export_btn)
        
        # History button
        history_btn = QPushButton("📚 History")
        history_btn.clicked.connect(self.show_session_history)
        controls_layout.addWidget(history_btn)
        
        footer_layout.addLayout(controls_layout)
        
        parent_layout.addWidget(footer_frame)
    
    def apply_current_theme(self):
        """Apply the current theme to the window"""
        stylesheet = self.theme_manager.get_stylesheet()
        self.setStyleSheet(stylesheet)
        
        # Update theme button text
        if hasattr(self, 'theme_btn'):
            if self.theme_manager.current_theme == 'light':
                self.theme_btn.setText("🌙 Dark")
            else:
                self.theme_btn.setText("☀️ Light")
    
    def toggle_theme(self):
        """Toggle between light and dark themes"""
        new_theme = "dark" if self.theme_manager.current_theme == "light" else "light"
        self.change_theme(new_theme)
    
    def change_theme(self, theme_name: str):
        """Change to specified theme"""
        self.theme_manager.set_theme(theme_name)
        self.apply_current_theme()
        self.save_settings()
        self.log_activity(f"🎨 Theme changed to {theme_name}")
    
    def toggle_fullscreen(self):
        """Toggle fullscreen mode"""
        if self.isFullScreen():
            self.showNormal()
            self.log_activity("🖼️ Exited fullscreen mode")
        else:
            self.showFullScreen()
            self.log_activity("⛶ Entered fullscreen mode")
    
    def toggle_preview_fullscreen(self):
        """Toggle preview fullscreen mode"""
        # Create a separate fullscreen window for the preview
        if not hasattr(self, 'fullscreen_preview') or not self.fullscreen_preview:
            self.fullscreen_preview = QMainWindow()
            self.fullscreen_preview.setWindowTitle("Live Preview - Fullscreen")
            
            # Create a copy of the preview widget
            preview_copy = InteractivePreviewWidget()
            if hasattr(self.frame_preview, 'current_pixmap') and self.frame_preview.current_pixmap:
                preview_copy.update_frame(self.frame_preview.current_pixmap)
            
            self.fullscreen_preview.setCentralWidget(preview_copy)
            self.fullscreen_preview.showFullScreen()
            
            # Connect close event
            self.fullscreen_preview.closeEvent = lambda event: self.close_fullscreen_preview(event)
        else:
            self.close_fullscreen_preview()
    
    def close_fullscreen_preview(self, event=None):
        """Close fullscreen preview"""
        if hasattr(self, 'fullscreen_preview') and self.fullscreen_preview:
            self.fullscreen_preview.close()
            self.fullscreen_preview = None
        if event:
            event.accept()
    
    def reset_layout(self):
        """Reset window layout to default"""
        self.resize(1400, 900)
        if hasattr(self, 'main_splitter'):
            self.main_splitter.setSizes([840, 560])
        self.log_activity("🔧 Layout reset to default")
    
    def toggle_pause(self):
        """Toggle pause/resume monitoring"""
        self.is_paused = not self.is_paused
        
        if self.is_paused:
            self.pause_btn.setText("▶️ Resume")
            self.log_activity("⏸️ Monitoring paused")
            self.notification_manager.notify_session_event("pause", "Monitoring paused")
        else:
            self.pause_btn.setText("⏸️ Pause")
            self.log_activity("▶️ Monitoring resumed")
            self.notification_manager.notify_session_event("resume", "Monitoring resumed")
    
    def take_screenshot(self):
        """Take screenshot of current frame"""
        if hasattr(self.frame_preview, 'current_pixmap') and self.frame_preview.current_pixmap:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"screenshot_{timestamp}.png"
            filepath = os.path.join(self.screenshots_dir, filename)
            
            # Save the screenshot
            if self.frame_preview.current_pixmap.save(filepath):
                self.statistics.screenshots_taken += 1
                self.log_activity(f"📷 Screenshot saved: {filename}")
                self.notification_manager.show_notification(
                    "Screenshot Saved", 
                    f"Screenshot saved as {filename}", 
                    "success"
                )
                return True
            else:
                self.log_activity(f"❌ Failed to save screenshot: {filename}")
                return False
        else:
            self.log_activity("❌ No frame available for screenshot")
            return False
    
    def open_screenshots_folder(self):
        """Open screenshots folder in file explorer"""
        try:
            if os.name == 'nt':  # Windows
                os.startfile(self.screenshots_dir)
            elif os.name == 'posix':  # macOS and Linux
                os.system(f'open "{self.screenshots_dir}"')
            self.log_activity(f"📁 Opened screenshots folder: {self.screenshots_dir}")
        except Exception as e:
            self.log_activity(f"❌ Failed to open screenshots folder: {e}")
    
    def reset_zoom(self):
        """Reset preview zoom"""
        if hasattr(self.frame_preview, 'reset_zoom'):
            self.frame_preview.reset_zoom()
            self.log_activity("🔍 Preview zoom reset")
    
    def on_region_changed(self, new_region):
        """Handle OCR region change"""
        self.ocr_region_label.setText(f"{new_region[0]:.1f}%, {new_region[1]:.1f}%, {new_region[2]:.1f}% × {new_region[3]:.1f}%")
        self.log_activity(f"🎯 OCR region updated: {new_region[0]:.1f}%, {new_region[1]:.1f}%, {new_region[2]:.1f}% × {new_region[3]:.1f}%")
        self.region_changed.emit(new_region)
    
    def on_zoom_requested(self, point):
        """Handle zoom request"""
        self.log_activity(f"🔍 Zoom requested at point: {point.x()}, {point.y()}")
    
    def show_manual_code_dialog(self):
        """Show dialog for manual code entry"""
        dialog = QDialog(self)
        dialog.setWindowTitle("Manual Code Entry")
        dialog.setModal(True)
        
        layout = QVBoxLayout(dialog)
        
        # Code input
        layout.addWidget(QLabel("Enter code:"))
        code_input = QLineEdit()
        layout.addWidget(code_input)
        
        # Code type selection
        layout.addWidget(QLabel("Select code type:"))
        type_combo = QComboBox()
        type_combo.addItems(["Reset", "Starter", "Starter Plus", "Expert", "Free Reset Code"])
        layout.addWidget(type_combo)
        
        # Confidence input
        layout.addWidget(QLabel("Confidence (0.0 - 1.0):"))
        confidence_input = QDoubleSpinBox()
        confidence_input.setRange(0.0, 1.0)
        confidence_input.setValue(1.0)
        confidence_input.setSingleStep(0.1)
        layout.addWidget(confidence_input)
        
        # Buttons
        buttons = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel)
        buttons.accepted.connect(dialog.accept)
        buttons.rejected.connect(dialog.reject)
        layout.addWidget(buttons)
        
        if dialog.exec() == QDialog.DialogCode.Accepted:
            # Create manual code detection
            code_data = {
                'code': code_input.text().strip(),
                'type': type_combo.currentText(),
                'confidence': confidence_input.value(),
                'full_text': f"MANUAL ENTRY: {code_input.text().strip()}",
                'manual': True
            }
            
            if code_data['code']:
                self.on_code_detected(code_data)
                self.log_activity(f"✏️ Manual code entry: {code_data['code']} ({code_data['type']})")
    
    def show_export_menu(self):
        """Show export options menu"""
        menu = QMenu(self)
        
        csv_action = menu.addAction("📊 Export to CSV")
        csv_action.triggered.connect(self.export_session_csv)
        
        json_action = menu.addAction("📄 Export to JSON")
        json_action.triggered.connect(self.export_session_json)
        
        log_action = menu.addAction("📝 Export Activity Log")
        log_action.triggered.connect(self.export_activity_log)
        
        # Show menu at cursor position
        menu.exec(QCursor.pos())
    
    def export_session_csv(self):
        """Export current session to CSV"""
        filename, _ = QFileDialog.getSaveFileName(
            self, "Export Session to CSV", 
            f"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
            "CSV Files (*.csv)"
        )
        
        if filename:
            if self.session_history.export_to_csv(filename):
                self.log_activity(f"💾 Session data exported to CSV: {filename}")
                self.notification_manager.show_notification(
                    "Export Successful", 
                    f"Session data exported to {os.path.basename(filename)}", 
                    "success"
                )
            else:
                self.log_activity(f"❌ Failed to export session data to CSV")
    
    def export_session_json(self):
        """Export current session to JSON"""
        filename, _ = QFileDialog.getSaveFileName(
            self, "Export Session to JSON", 
            f"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
            "JSON Files (*.json)"
        )
        
        if filename:
            if self.session_history.export_to_json(filename):
                self.log_activity(f"💾 Session data exported to JSON: {filename}")
                self.notification_manager.show_notification(
                    "Export Successful", 
                    f"Session data exported to {os.path.basename(filename)}", 
                    "success"
                )
            else:
                self.log_activity(f"❌ Failed to export session data to JSON")
    
    def export_activity_log(self):
        """Export activity log to text file"""
        filename, _ = QFileDialog.getSaveFileName(
            self, "Export Activity Log", 
            f"activity_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt",
            "Text Files (*.txt)"
        )
        
        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(self.activity_log.toPlainText())
                self.log_activity(f"💾 Activity log exported: {filename}")
                self.notification_manager.show_notification(
                    "Export Successful", 
                    f"Activity log exported to {os.path.basename(filename)}", 
                    "success"
                )
            except Exception as e:
                self.log_activity(f"❌ Failed to export activity log: {e}")
