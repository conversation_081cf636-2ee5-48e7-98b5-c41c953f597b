#!/usr/bin/env python3
"""
MFFUHijack Launcher
Handles dependency checking and application startup
"""

import sys
import os
import argparse

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))


def main():
    """Main launcher function"""
    parser = argparse.ArgumentParser(description='MFFUHijack Launcher')
    parser.add_argument('--no-gui-installer', action='store_true', 
                       help='Use console-only dependency installer')
    parser.add_argument('--skip-deps', action='store_true',
                       help='Skip dependency check entirely')
    parser.add_argument('--install-only', action='store_true',
                       help='Only run dependency installer, don\'t start app')
    
    args = parser.parse_args()
    
    print("🚀 MFFUHijack Launcher")
    print("=" * 40)
    
    # Skip dependency check if requested
    if not args.skip_deps:
        from startup_checker import check_startup_dependencies
        
        print("🔍 Checking dependencies...")
        use_gui = not args.no_gui_installer
        
        if not check_startup_dependencies(use_gui=use_gui):
            print("\n❌ Cannot start application due to missing critical dependencies.")
            print("Please install required packages and try again.")
            return 1
    
    # If install-only mode, exit after dependency check
    if args.install_only:
        print("\n✅ Dependency check complete!")
        return 0
    
    # Start the main application
    try:
        print("\n🎯 Starting MFFUHijack application...")
        from main import main as app_main
        app_main()
        return 0
    
    except KeyboardInterrupt:
        print("\n\n⚠️  Application interrupted by user.")
        return 0
    
    except Exception as e:
        print(f"\n❌ Application failed to start: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
