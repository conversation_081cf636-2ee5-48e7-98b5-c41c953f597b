"""
Simple GUI for MFFUHijack - Original Clean Design
Maintains all functionality with clean, simple interface
"""

import sys
import os
import logging
from PyQt6.QtWidgets import (
    QMainWindow, QTabWidget, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QLineEdit, QPushButton, QComboBox, QTextEdit,
    QProgressBar, QGroupBox, QGridLayout, QSpacerItem, QSizePolicy
)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal
from PyQt6.QtGui import QPixmap, QFont, QIcon

# Import our modules
from ocr_utils import ocr_manager
from submitter import code_submitter

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Styling constants for consistent appearance
STYLE_CONSTANTS = {
    'group_box_style': """
        QGroupBox {
            font-weight: bold;
            border: 2px solid #cccccc;
            border-radius: 5px;
            margin-top: 10px;
            padding-top: 10px;
        }
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
        }
    """,
    'primary_button_style': """
        QPushButton {
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 5px;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: #45a049;
        }
        QPushButton:pressed {
            background-color: #3d8b40;
        }
        QPushButton:disabled {
            background-color: #cccccc;
            color: #666666;
        }
    """,
    'danger_button_style': """
        QPushButton {
            background-color: #f44336;
            color: white;
            border: none;
            border-radius: 5px;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: #da190b;
        }
        QPushButton:pressed {
            background-color: #c1170a;
        }
        QPushButton:disabled {
            background-color: #cccccc;
            color: #666666;
        }
    """,
    'text_input_style': """
        QLineEdit, QTextEdit {
            border: 2px solid #ddd;
            border-radius: 5px;
            padding: 5px;
            background-color: white;
        }
        QLineEdit:focus, QTextEdit:focus {
            border-color: #4CAF50;
        }
    """
}


class LivestreamTab(QWidget):
    """Tab for livestream bot controls"""
    
    # Signals for communication with main window
    start_bot_signal = pyqtSignal(str, str, float, str)  # url, ocr_engine, interval, speed
    stop_bot_signal = pyqtSignal()
    
    def __init__(self):
        super().__init__()
        self.ocr_region = (0, 67, 100, 33)  # Default: bottom third (x%, y%, w%, h%)
        self.init_ui()
    
    def init_ui(self):
        """Initialize the livestream tab UI"""
        layout = QVBoxLayout()
        layout.setContentsMargins(10, 10, 10, 10)  # Reduced margins for more compact layout
        layout.setSpacing(8)  # Reduced spacing between sections

        # Configuration section
        config_group = QGroupBox("🔧 Configuration")
        config_group.setStyleSheet(STYLE_CONSTANTS['group_box_style'])
        config_layout = QGridLayout(config_group)
        config_layout.setSpacing(6)  # Reduced spacing between grid elements
        config_layout.setContentsMargins(10, 15, 10, 10)  # Reduced margins

        # Auto-detect channel input
        channel_label = QLabel("Auto-Detect Channel:")
        channel_label.setMinimumWidth(120)
        config_layout.addWidget(channel_label, 0, 0)
        self.channel_input = QLineEdit()
        self.channel_input.setPlaceholderText("https://www.youtube.com/@ChannelName")
        self.channel_input.setText("https://www.youtube.com/@MyFundedFuturesPropFirm")
        self.channel_input.setMinimumHeight(25)  # Reduced height
        self.channel_input.setStyleSheet(STYLE_CONSTANTS['text_input_style'])
        config_layout.addWidget(self.channel_input, 0, 1, 1, 2)

        # Auto-detect button
        self.auto_detect_button = QPushButton("🔍 Auto-Detect Latest Stream")
        self.auto_detect_button.setMinimumHeight(25)  # Reduced height
        self.auto_detect_button.setMinimumWidth(180)
        self.auto_detect_button.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:pressed {
                background-color: #1565C0;
            }
        """)
        self.auto_detect_button.clicked.connect(self.auto_detect_livestream)
        config_layout.addWidget(self.auto_detect_button, 0, 3)

        # Livestream URL input
        url_label = QLabel("Livestream URL:")
        url_label.setMinimumWidth(120)
        config_layout.addWidget(url_label, 1, 0)
        self.url_input = QLineEdit()
        self.url_input.setPlaceholderText("https://www.youtube.com/watch?v=... (or 'mock' for testing)")
        self.url_input.setText("")
        self.url_input.setMinimumHeight(30)
        self.url_input.setStyleSheet(STYLE_CONSTANTS['text_input_style'])
        config_layout.addWidget(self.url_input, 1, 1, 1, 3)

        # OCR Model selector
        ocr_label = QLabel("OCR Model:")
        ocr_label.setMinimumWidth(120)
        config_layout.addWidget(ocr_label, 2, 0)
        self.ocr_combo = QComboBox()
        self.ocr_combo.setMinimumHeight(30)
        self.ocr_combo.setMinimumWidth(150)
        self.ocr_combo.setStyleSheet("""
            QComboBox {
                border: 2px solid #ddd;
                border-radius: 5px;
                padding: 5px;
                background-color: white;
            }
            QComboBox:focus {
                border-color: #4CAF50;
            }
            QComboBox::drop-down {
                border: none;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #666;
                margin-right: 5px;
            }
        """)
        self.update_ocr_models()
        config_layout.addWidget(self.ocr_combo, 2, 1)

        # Polling interval
        interval_label = QLabel("Polling Interval (sec):")
        config_layout.addWidget(interval_label, 2, 2)
        self.interval_input = QLineEdit("1.0")
        self.interval_input.setMinimumHeight(25)  # Reduced height
        self.interval_input.setMaximumWidth(80)
        self.interval_input.setStyleSheet(STYLE_CONSTANTS['text_input_style'])
        config_layout.addWidget(self.interval_input, 2, 3)

        # Speed control
        speed_label = QLabel("Playback Speed:")
        config_layout.addWidget(speed_label, 3, 0)
        self.speed_combo = QComboBox()
        self.speed_combo.setMinimumHeight(25)  # Reduced height
        self.speed_combo.setMinimumWidth(120)
        # Generate speed options from 1.0x to 50x in intervals of 2
        speed_options = ["1.0x (Normal)", "1.5x (Latest Frames)"]
        speed_options.extend([f"{i}.0x (Speed)" for i in range(2, 51, 2)])  # 2.0x, 4.0x, 6.0x, ... 50.0x
        self.speed_combo.addItems(speed_options)
        self.speed_combo.setCurrentText("1.5x (Latest Frames)")  # Default to 1.5x
        self.speed_combo.setStyleSheet("""
            QComboBox {
                border: 2px solid #ddd;
                border-radius: 5px;
                padding: 5px;
                background-color: white;
            }
            QComboBox:focus {
                border-color: #4CAF50;
            }
            QComboBox::drop-down {
                border: none;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #666;
                margin-right: 5px;
            }
        """)
        config_layout.addWidget(self.speed_combo, 3, 1)

        # Speed info label
        speed_info = QLabel("⚡ Speed range: 1.0x to 50.0x (intervals of 2x). Higher speeds process latest frames faster.")
        speed_info.setStyleSheet("color: #666; font-size: 10px; font-style: italic;")
        speed_info.setWordWrap(True)
        config_layout.addWidget(speed_info, 3, 2, 1, 2)

        layout.addWidget(config_group)

        # Account Type Selection section
        account_group = QGroupBox("🎯 Account Types to Scan")
        account_group.setStyleSheet(STYLE_CONSTANTS['group_box_style'])
        account_layout = QVBoxLayout(account_group)
        account_layout.setContentsMargins(10, 15, 10, 10)  # Reduced margins
        account_layout.setSpacing(6)  # Reduced spacing

        # Account type checkboxes
        checkbox_layout = QGridLayout()
        checkbox_layout.setSpacing(6)  # Reduced spacing

        # Create checkboxes for each account type
        self.account_checkboxes = {}
        account_types = [
            ("Free Reset Code", "🔄 Free Reset Code"),
            ("Starter", "🚀 Starter"),
            ("Starter Plus", "⭐ Starter Plus"),
            ("Expert", "👑 Expert")
        ]

        for i, (account_type, display_name) in enumerate(account_types):
            checkbox = QCheckBox(display_name)
            checkbox.setChecked(True)  # All enabled by default
            checkbox.setStyleSheet("""
                QCheckBox {
                    font-size: 12px;
                    font-weight: bold;
                    spacing: 8px;
                }
                QCheckBox::indicator {
                    width: 18px;
                    height: 18px;
                }
                QCheckBox::indicator:unchecked {
                    border: 2px solid #ccc;
                    border-radius: 3px;
                    background-color: white;
                }
                QCheckBox::indicator:checked {
                    border: 2px solid #4CAF50;
                    border-radius: 3px;
                    background-color: #4CAF50;
                }
            """)
            self.account_checkboxes[account_type] = checkbox
            checkbox_layout.addWidget(checkbox, i // 2, i % 2)

        account_layout.addLayout(checkbox_layout)

        # Account selection info
        account_info = QLabel("💡 Select which account types to scan for. Only selected types will be detected and processed.")
        account_info.setStyleSheet("color: #666; font-size: 10px; font-style: italic;")
        account_info.setWordWrap(True)
        account_layout.addWidget(account_info)

        layout.addWidget(account_group)

        # Buttons section
        buttons_group = QGroupBox("⚙️ Actions")
        buttons_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        button_layout = QHBoxLayout(buttons_group)
        button_layout.setContentsMargins(15, 20, 15, 15)
        button_layout.setSpacing(10)

        # Configuration buttons
        self.pattern_button = QPushButton("🔧 Configure Patterns")
        self.pattern_button.setMinimumHeight(28)  # Reduced height
        self.pattern_button.setMinimumWidth(150)
        self.pattern_button.clicked.connect(self.configure_patterns)
        button_layout.addWidget(self.pattern_button)

        self.region_button = QPushButton("📐 Select OCR Region")
        self.region_button.setMinimumHeight(28)  # Reduced height
        self.region_button.setMinimumWidth(150)
        self.region_button.clicked.connect(self.open_live_region_selector)
        button_layout.addWidget(self.region_button)

        button_layout.addStretch()

        # Control buttons
        self.start_button = QPushButton("▶️ Start Bot")
        self.start_button.setMinimumHeight(30)  # Reduced height
        self.start_button.setMinimumWidth(120)
        self.start_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
        """)
        self.start_button.clicked.connect(self.start_bot)
        button_layout.addWidget(self.start_button)

        self.stop_button = QPushButton("⏹️ Stop Bot")
        self.stop_button.setMinimumHeight(30)  # Reduced height
        self.stop_button.setMinimumWidth(120)
        self.stop_button.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #da190b;
            }
            QPushButton:pressed {
                background-color: #c1170a;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)
        self.stop_button.clicked.connect(self.stop_bot)
        self.stop_button.setEnabled(False)
        button_layout.addWidget(self.stop_button)

        layout.addWidget(buttons_group)
        
        # Account types display
        status_group = QGroupBox("📊 Status")
        status_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        status_layout = QVBoxLayout(status_group)
        status_layout.setContentsMargins(10, 15, 10, 10)  # Reduced margins
        status_layout.setSpacing(5)  # Reduced spacing

        self.account_types_label = QLabel("Account Types: Loading...")
        self.account_types_label.setStyleSheet("color: #666; font-size: 12px; padding: 5px;")
        self.account_types_label.setWordWrap(True)
        self.update_account_types_display()
        status_layout.addWidget(self.account_types_label)

        # Status
        self.status_label = QLabel("Status: Idle")
        self.status_label.setStyleSheet("font-weight: bold; font-size: 14px; color: #333; padding: 5px;")
        status_layout.addWidget(self.status_label)

        layout.addWidget(status_group)

        # Preview and Log section
        content_group = QGroupBox("📺 Live Monitoring")
        content_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        content_layout = QHBoxLayout(content_group)
        content_layout.setContentsMargins(10, 15, 10, 10)  # Reduced margins
        content_layout.setSpacing(10)  # Reduced spacing

        # Frame preview
        preview_layout = QVBoxLayout()
        preview_label = QLabel("🖼️ Frame Preview:")
        preview_label.setStyleSheet("font-weight: bold; margin-bottom: 5px;")
        preview_layout.addWidget(preview_label)

        self.frame_preview = QLabel()
        self.frame_preview.setMinimumSize(350, 200)  # Reduced size
        self.frame_preview.setMaximumSize(450, 300)  # Reduced size
        self.frame_preview.setStyleSheet("""
            border: 2px solid #ddd;
            background-color: #f8f8f8;
            border-radius: 5px;
        """)
        self.frame_preview.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.frame_preview.setText("No preview available\n\nStart the bot to see live frames")
        self.frame_preview.setScaledContents(True)
        preview_layout.addWidget(self.frame_preview)

        content_layout.addLayout(preview_layout)

        # Detection log
        log_layout = QVBoxLayout()
        log_label = QLabel("📋 Detection Log:")
        log_label.setStyleSheet("font-weight: bold; margin-bottom: 5px;")
        log_layout.addWidget(log_label)

        self.log_text = QTextEdit()
        self.log_text.setMinimumHeight(250)
        self.log_text.setMaximumHeight(350)
        self.log_text.setReadOnly(True)
        self.log_text.setStyleSheet("""
            border: 2px solid #ddd;
            border-radius: 5px;
            background-color: #fafafa;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 11px;
        """)
        self.log_text.setPlaceholderText("Detection results will appear here when the bot is running...")
        log_layout.addWidget(self.log_text)

        content_layout.addLayout(log_layout)

        layout.addWidget(content_group)

        # Payment section (moved from MFFU Credentials)
        payment_group = QGroupBox("💳 Payment Information")
        payment_group.setStyleSheet(STYLE_CONSTANTS['group_box_style'])
        payment_layout = QGridLayout(payment_group)
        payment_layout.setContentsMargins(10, 15, 10, 10)  # Reduced margins
        payment_layout.setSpacing(6)  # Reduced spacing

        # Username field
        username_label = QLabel("Username:")
        username_label.setMinimumWidth(120)
        payment_layout.addWidget(username_label, 0, 0)
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("Enter your MFFU username")
        self.username_input.setMinimumHeight(25)  # Reduced height
        self.username_input.setStyleSheet(STYLE_CONSTANTS['text_input_style'])
        payment_layout.addWidget(self.username_input, 0, 1, 1, 2)

        # Password field
        password_label = QLabel("Password:")
        password_label.setMinimumWidth(120)
        payment_layout.addWidget(password_label, 1, 0)
        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("Enter your MFFU password")
        self.password_input.setMinimumHeight(25)  # Reduced height
        self.password_input.setEchoMode(QLineEdit.EchoMode.Password)  # Hide password text
        self.password_input.setStyleSheet(STYLE_CONSTANTS['text_input_style'])
        payment_layout.addWidget(self.password_input, 1, 1, 1, 2)

        # Card CVV field
        cvv_label = QLabel("Card CVV:")
        cvv_label.setMinimumWidth(120)
        payment_layout.addWidget(cvv_label, 2, 0)
        self.cvv_input = QLineEdit()
        self.cvv_input.setPlaceholderText("Enter your card CVV")
        self.cvv_input.setMinimumHeight(25)  # Reduced height
        self.cvv_input.setMaxLength(4)  # CVV is typically 3-4 digits
        self.cvv_input.setStyleSheet(STYLE_CONSTANTS['text_input_style'])
        payment_layout.addWidget(self.cvv_input, 2, 1, 1, 2)

        # Account to Reset field (for reset account type)
        reset_account_label = QLabel("Account to Reset (Acc#):")
        reset_account_label.setMinimumWidth(120)
        payment_layout.addWidget(reset_account_label, 3, 0)
        self.reset_account_input = QLineEdit()
        self.reset_account_input.setPlaceholderText("Enter account number to reset")
        self.reset_account_input.setMinimumHeight(25)  # Reduced height
        self.reset_account_input.setStyleSheet(STYLE_CONSTANTS['text_input_style'])
        payment_layout.addWidget(self.reset_account_input, 3, 1, 1, 2)

        # Add show/hide password toggle
        self.show_password_button = QPushButton("👁️")
        self.show_password_button.setMaximumWidth(40)
        self.show_password_button.setMinimumHeight(25)  # Reduced height
        self.show_password_button.setToolTip("Show/Hide Password")
        self.show_password_button.setStyleSheet("""
            QPushButton {
                background-color: #f0f0f0;
                border: 2px solid #ddd;
                border-radius: 5px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #e0e0e0;
            }
            QPushButton:pressed {
                background-color: #d0d0d0;
            }
        """)
        self.show_password_button.clicked.connect(self.toggle_password_visibility)
        payment_layout.addWidget(self.show_password_button, 1, 3)

        layout.addWidget(payment_group)

        # Browser Automation section
        browser_group = QGroupBox("🌐 Browser Automation")
        browser_group.setStyleSheet(STYLE_CONSTANTS['group_box_style'])
        browser_layout = QGridLayout(browser_group)
        browser_layout.setContentsMargins(10, 15, 10, 10)  # Reduced margins
        browser_layout.setSpacing(6)  # Reduced spacing

        # Browser controls
        self.login_button = QPushButton("🔐 Login to MFFU")
        self.login_button.setMinimumHeight(28)  # Reduced height
        self.login_button.setStyleSheet(STYLE_CONSTANTS['button_style'])
        self.login_button.clicked.connect(self.login_to_mffu)
        browser_layout.addWidget(self.login_button, 0, 0)

        self.preload_button = QPushButton("🚀 Preload Pages")
        self.preload_button.setMinimumHeight(28)  # Reduced height
        self.preload_button.setStyleSheet(STYLE_CONSTANTS['button_style'])
        self.preload_button.setToolTip("Preload browser windows and links for selected account types only")
        self.preload_button.clicked.connect(self.preload_pages)
        browser_layout.addWidget(self.preload_button, 0, 1)

        self.show_browser_button = QPushButton("👁️ Show Browser")
        self.show_browser_button.setMinimumHeight(28)  # Reduced height
        self.show_browser_button.setStyleSheet(STYLE_CONSTANTS['button_style'])
        self.show_browser_button.clicked.connect(self.toggle_browser_visibility)
        browser_layout.addWidget(self.show_browser_button, 1, 0)

        self.browser_status_label = QLabel("Browser Status: Not initialized")
        self.browser_status_label.setStyleSheet("color: #666; font-size: 12px; padding: 5px;")
        browser_layout.addWidget(self.browser_status_label, 1, 1)

        layout.addWidget(browser_group)

        self.setLayout(layout)

    def auto_detect_livestream(self):
        """Auto-detect the latest livestream from the specified channel"""
        channel_url = self.channel_input.text().strip()

        if not channel_url:
            self.log_message("❌ Please enter a channel URL for auto-detection")
            return

        self.log_message(f"🔍 Auto-detecting latest livestream from: {channel_url}")
        self.auto_detect_button.setEnabled(False)
        self.auto_detect_button.setText("🔍 Detecting...")

        try:
            from livestream_detector import auto_detect_livestream

            # Run detection in a separate thread to avoid blocking GUI
            import threading

            def detect_stream():
                try:
                    stream_url = auto_detect_livestream(channel_url)

                    # Update GUI in main thread
                    if stream_url:
                        self.url_input.setText(stream_url)
                        self.log_message(f"✅ Found latest livestream!")
                        self.log_message(f"   URL: {stream_url}")
                    else:
                        self.log_message("❌ No active livestream found")
                        self.log_message("   The channel may not be currently live")

                except Exception as e:
                    self.log_message(f"❌ Auto-detection failed: {str(e)}")

                finally:
                    # Re-enable button
                    self.auto_detect_button.setEnabled(True)
                    self.auto_detect_button.setText("🔍 Auto-Detect Latest Stream")

            # Start detection in background thread
            thread = threading.Thread(target=detect_stream, daemon=True)
            thread.start()

        except ImportError:
            self.log_message("❌ Livestream detector not available")
            self.auto_detect_button.setEnabled(True)
            self.auto_detect_button.setText("🔍 Auto-Detect Latest Stream")
        except Exception as e:
            self.log_message(f"❌ Auto-detection error: {str(e)}")
            self.auto_detect_button.setEnabled(True)
            self.auto_detect_button.setText("🔍 Auto-Detect Latest Stream")

    def toggle_password_visibility(self):
        """Toggle password field visibility"""
        if self.password_input.echoMode() == QLineEdit.EchoMode.Password:
            # Show password
            self.password_input.setEchoMode(QLineEdit.EchoMode.Normal)
            self.show_password_button.setText("🙈")
            self.show_password_button.setToolTip("Hide Password")
        else:
            # Hide password
            self.password_input.setEchoMode(QLineEdit.EchoMode.Password)
            self.show_password_button.setText("👁️")
            self.show_password_button.setToolTip("Show Password")

    def login_to_mffu(self):
        """Login to My Funded Futures website"""
        username = self.username_input.text().strip()
        password = self.password_input.text().strip()

        if not username or not password:
            self.log_message("❌ Please enter both username and password")
            return

        try:
            from browser_automation import browser_automation

            self.log_message("🌐 Initializing browser automation...")
            self.browser_status_label.setText("Browser Status: Initializing...")

            # Connect signals
            if browser_automation.signals:
                browser_automation.signals.status_update.connect(self.log_message)
                browser_automation.signals.error_occurred.connect(self.log_message)
                browser_automation.signals.login_ready.connect(self.on_login_ready)
                browser_automation.signals.code_submitted.connect(self.on_code_submitted)

            # Start login process
            success = browser_automation.login_to_mff(username, password)
            if success:
                self.browser_status_label.setText("Browser Status: Login in progress...")
                self.login_button.setText("⏳ Logging in...")
                self.login_button.setEnabled(False)
            else:
                self.browser_status_label.setText("Browser Status: Login failed")

        except ImportError:
            self.log_message("❌ Browser automation not available. Please install selenium: pip install selenium")
        except Exception as e:
            self.log_message(f"❌ Login error: {str(e)}")
            self.browser_status_label.setText("Browser Status: Error")

    def preload_pages(self):
        """Preload account type pages to checkout stage for selected account types"""
        try:
            from browser_automation import browser_automation

            # Get selected account types from checkboxes
            selected_types = self.get_selected_account_types()
            if not selected_types:
                self.log_message("❌ No account types selected. Please select at least one account type to preload.")
                return

            # Get CVV for enhanced preloading
            cvv = self.cvv_input.text().strip()

            self.log_message(f"🚀 Preloading pages for selected types: {', '.join(selected_types)}")
            if cvv:
                self.log_message("💳 CVV provided - will pre-fill for faster submission")
            else:
                self.log_message("⚠️ CVV not provided - you'll need to enter it manually during submission")

            self.preload_button.setText("⏳ Preloading...")
            self.preload_button.setEnabled(False)

            # Use open_manual_tabs method which handles the full setup process
            success = browser_automation.open_manual_tabs(selected_types, cvv)
            if success:
                self.browser_status_label.setText("Browser Status: Pages preloaded")
                self.log_message("✅ Selected account pages preloaded to checkout stage")
                if cvv:
                    self.log_message("💳 Coupon textboxes focused and ready for instant code entry")
            else:
                self.browser_status_label.setText("Browser Status: Preload failed")

            self.preload_button.setText("🚀 Preload Pages")
            self.preload_button.setEnabled(True)

        except ImportError:
            self.log_message("❌ Browser automation not available. Please install selenium: pip install selenium")
        except Exception as e:
            self.log_message(f"❌ Preload error: {str(e)}")
            self.preload_button.setText("🚀 Preload Pages")
            self.preload_button.setEnabled(True)

    def toggle_browser_visibility(self):
        """Toggle browser window visibility"""
        try:
            from browser_automation import browser_automation

            current_visibility = browser_automation.is_visible
            new_visibility = not current_visibility

            browser_automation.set_visibility(new_visibility)

            if new_visibility:
                self.show_browser_button.setText("🙈 Hide Browser")
                self.log_message("👁️ Browser window is now visible")
            else:
                self.show_browser_button.setText("👁️ Show Browser")
                self.log_message("🙈 Browser window is now hidden")

        except ImportError:
            self.log_message("❌ Browser automation not available. Please install selenium: pip install selenium")
        except Exception as e:
            self.log_message(f"❌ Browser visibility error: {str(e)}")

    def on_login_ready(self):
        """Called when login is ready for manual completion"""
        self.browser_status_label.setText("Browser Status: Ready for manual login")
        self.login_button.setText("✅ Complete Login Manually")
        self.login_button.setEnabled(True)
        self.log_message("🤖 Please complete CAPTCHA (if present) and click Sign In manually")

    def on_code_submitted(self, success: bool, message: str):
        """Called when code submission is complete"""
        if success:
            self.log_message(f"✅ {message}")
        else:
            self.log_message(f"❌ {message}")

    def update_ocr_models(self):
        """Update the OCR model dropdown with available engines"""
        self.ocr_combo.clear()
        available_engines = ocr_manager.get_available_engines()
        self.ocr_combo.addItems(available_engines)
        
        if available_engines:
            self.ocr_combo.setCurrentText(ocr_manager.current_engine)
    
    def update_account_types_display(self):
        """Update the account types display"""
        try:
            enabled_types = ocr_manager.get_enabled_account_types()
            if enabled_types:
                types_text = ", ".join(enabled_types)
                self.account_types_label.setText(f"Enabled: {types_text}")
                self.account_types_label.setStyleSheet("color: #4CAF50; font-size: 11px;")
            else:
                self.account_types_label.setText("No account types enabled")
                self.account_types_label.setStyleSheet("color: #f44336; font-size: 11px;")
        except Exception as e:
            self.account_types_label.setText("Error loading account types")
            self.account_types_label.setStyleSheet("color: #f44336; font-size: 11px;")
    
    def configure_patterns(self):
        """Open the pattern configuration dialog"""
        try:
            from pattern_config_dialog import show_pattern_config_dialog
            
            new_patterns = show_pattern_config_dialog(self)
            if new_patterns:
                # Update OCR manager with new patterns
                ocr_manager.update_pattern_config(new_patterns)
                self.update_account_types_display()
                self.log_message("✅ Pattern configuration updated successfully")
            
        except Exception as e:
            self.log_message(f"❌ Error configuring patterns: {str(e)}")
            logger.error(f"Pattern configuration error: {e}")

    def get_selected_account_types(self):
        """Get list of selected account types"""
        selected_types = []
        for account_type, checkbox in self.account_checkboxes.items():
            if checkbox.isChecked():
                selected_types.append(account_type)
        return selected_types

    def open_live_region_selector(self):
        """Open region selector for live stream"""
        try:
            from region_selector import RegionSelectorWidget
            from PyQt6.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QPushButton
            
            # Create dialog
            dialog = QDialog(self)
            dialog.setWindowTitle("Select OCR Region for Live Stream")
            dialog.setMinimumSize(800, 700)
            
            layout = QVBoxLayout()
            
            # Info label
            info_label = QLabel(
                "Select the region where codes appear in the livestream.\n"
                "This will be used for real-time OCR scanning during live detection."
            )
            info_label.setStyleSheet("color: #666; margin: 10px; padding: 10px; background-color: #f0f0f0; border-radius: 5px;")
            info_label.setWordWrap(True)
            layout.addWidget(info_label)
            
            # Region selector
            region_selector = RegionSelectorWidget("Live Stream OCR Region")
            
            # Create a sample frame (placeholder)
            import numpy as np
            sample_frame = np.ones((480, 854, 3), dtype=np.uint8) * 240
            import cv2
            cv2.putText(sample_frame, "LIVE STREAM PREVIEW", (250, 200), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (100, 100, 100), 2)
            cv2.putText(sample_frame, "FREE 50 STARTER: ABC123", (250, 350), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 200), 2)
            cv2.putText(sample_frame, "(Sample code location)", (250, 380), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (100, 100, 100), 1)
            
            # Set current region
            x, y, w, h = self.ocr_region
            region_selector.set_image(sample_frame)
            region_selector.set_preset(x, y, w, h)
            
            layout.addWidget(region_selector)
            
            # Buttons
            button_layout = QHBoxLayout()
            
            cancel_btn = QPushButton("Cancel")
            cancel_btn.clicked.connect(dialog.reject)
            button_layout.addWidget(cancel_btn)
            
            button_layout.addStretch()
            
            apply_btn = QPushButton("Apply Region")
            apply_btn.clicked.connect(lambda: self.apply_live_region(region_selector, dialog))
            button_layout.addWidget(apply_btn)
            
            layout.addLayout(button_layout)
            dialog.setLayout(layout)
            
            dialog.exec()
            
        except Exception as e:
            self.log_message(f"❌ Error opening region selector: {str(e)}")
    
    def apply_live_region(self, region_selector, dialog):
        """Apply the selected region for live stream"""
        self.ocr_region = region_selector.get_region_percent()
        x, y, w, h = self.ocr_region
        self.log_message(f"✅ OCR region updated: {x:.1f}%, {y:.1f}%, {w:.1f}% × {h:.1f}%")
        dialog.accept()
    
    def start_bot(self):
        """Start the bot with speed optimization"""
        url = self.url_input.text().strip()
        ocr_engine = self.ocr_combo.currentText()
        speed_setting = self.speed_combo.currentText()

        try:
            interval = float(self.interval_input.text())
        except ValueError:
            interval = 1.0

        if not url:
            self.log_message("❌ Please enter a livestream URL")
            return

        # Check for mock mode
        if url.lower() in ['mock', 'test', 'demo']:
            self.log_message("🎭 MOCK MODE ENABLED - Using synthetic test video")
            try:
                from mock_video_capture import enable_mock_mode
                enable_mock_mode()
                url = "https://mock-test-video.example.com"
                self.log_message("✅ Mock video capture initialized")
            except ImportError:
                self.log_message("❌ Mock mode not available - mock_video_capture.py not found")
                return

        # Log speed setting
        self.log_message(f"⚡ Starting bot with {speed_setting} for latest frame detection")

        self.start_bot_signal.emit(url, ocr_engine, interval, speed_setting)
        self.start_button.setEnabled(False)
        self.stop_button.setEnabled(True)
    
    def stop_bot(self):
        """Stop the bot"""
        self.stop_bot_signal.emit()
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
    
    def update_status(self, status: str):
        """Update the status label"""
        self.status_label.setText(f"Status: {status}")
    
    def update_frame_preview(self, pixmap: QPixmap):
        """Update the frame preview"""
        if pixmap:
            # Scale pixmap to fit preview area while maintaining aspect ratio
            scaled_pixmap = pixmap.scaled(
                self.frame_preview.size(),
                Qt.AspectRatioMode.KeepAspectRatio,
                Qt.TransformationMode.SmoothTransformation
            )
            self.frame_preview.setPixmap(scaled_pixmap)
    
    def log_message(self, message: str):
        """Add a message to the log"""
        self.log_text.append(message)
        # Auto-scroll to bottom
        scrollbar = self.log_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())


class DatasetTab(QWidget):
    """Tab for dataset building and model training"""

    # Signals for communication with main window
    start_dataset_signal = pyqtSignal(str, int, str)  # channel_url, num_frames, region
    start_training_signal = pyqtSignal()

    def __init__(self):
        super().__init__()
        self.dataset_region = (0, 67, 100, 33)  # Default: bottom third
        self.selected_frame = None
        self.init_ui()

    def init_ui(self):
        """Initialize the dataset tab UI"""
        layout = QVBoxLayout()
        layout.setContentsMargins(20, 20, 20, 20)  # Add margins around tab content
        layout.setSpacing(15)  # Add spacing between sections

        # Dataset Builder section
        dataset_group = QGroupBox("📊 Dataset Configuration")
        dataset_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        dataset_layout = QGridLayout(dataset_group)
        dataset_layout.setSpacing(10)  # Add spacing between grid elements
        dataset_layout.setContentsMargins(15, 20, 15, 15)

        # Channel URL input
        url_label = QLabel("YouTube Channel URL:")
        url_label.setMinimumWidth(140)
        dataset_layout.addWidget(url_label, 0, 0)
        self.channel_input = QLineEdit()
        self.channel_input.setPlaceholderText("https://www.youtube.com/@MyFundedFuturesPropFirm")
        self.channel_input.setText("https://www.youtube.com/@MyFundedFuturesPropFirm/streams")
        self.channel_input.setMinimumHeight(30)
        self.channel_input.setStyleSheet(STYLE_CONSTANTS['text_input_style'])
        dataset_layout.addWidget(self.channel_input, 0, 1, 1, 2)

        # Number of frames
        frames_label = QLabel("Training Frames:")
        frames_label.setMinimumWidth(140)
        dataset_layout.addWidget(frames_label, 1, 0)
        self.frames_input = QLineEdit("50")
        self.frames_input.setMinimumHeight(30)
        self.frames_input.setMaximumWidth(100)
        self.frames_input.setStyleSheet(STYLE_CONSTANTS['text_input_style'])
        dataset_layout.addWidget(self.frames_input, 1, 1)

        # Frame & Region Selector button
        self.frame_region_button = QPushButton("🎯 Select Frames & Region")
        self.frame_region_button.setMinimumHeight(35)
        self.frame_region_button.setMinimumWidth(180)
        self.frame_region_button.clicked.connect(self.open_frame_region_selector)
        dataset_layout.addWidget(self.frame_region_button, 1, 2)

        layout.addWidget(dataset_group)

        # Buttons section
        actions_group = QGroupBox("🚀 Actions")
        actions_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        button_layout = QHBoxLayout(actions_group)
        button_layout.setContentsMargins(15, 20, 15, 15)
        button_layout.setSpacing(15)

        # Start Dataset Builder button
        self.start_dataset_button = QPushButton("📊 Start Dataset Builder")
        self.start_dataset_button.setMinimumHeight(40)
        self.start_dataset_button.setMinimumWidth(200)
        self.start_dataset_button.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                border-radius: 5px;
                font-weight: bold;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:pressed {
                background-color: #1565C0;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)
        self.start_dataset_button.clicked.connect(self.start_dataset_builder)
        button_layout.addWidget(self.start_dataset_button)

        button_layout.addStretch()

        # Train Custom OCR Model button
        self.train_button = QPushButton("🧠 Train Custom OCR Model")
        self.train_button.setMinimumHeight(40)
        self.train_button.setMinimumWidth(200)
        self.train_button.setStyleSheet("""
            QPushButton {
                background-color: #FF9800;
                color: white;
                border: none;
                border-radius: 5px;
                font-weight: bold;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #F57C00;
            }
            QPushButton:pressed {
                background-color: #E65100;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)
        self.train_button.clicked.connect(self.start_training)
        button_layout.addWidget(self.train_button)

        layout.addWidget(actions_group)

        # Progress section
        progress_group = QGroupBox("📈 Progress")
        progress_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        progress_layout = QVBoxLayout(progress_group)
        progress_layout.setContentsMargins(15, 20, 15, 15)
        progress_layout.setSpacing(10)

        self.progress_bar = QProgressBar()
        self.progress_bar.setMinimumHeight(25)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid #ddd;
                border-radius: 5px;
                text-align: center;
                font-weight: bold;
            }
            QProgressBar::chunk {
                background-color: #4CAF50;
                border-radius: 3px;
            }
        """)
        self.progress_bar.setVisible(False)
        progress_layout.addWidget(self.progress_bar)

        # Log section
        log_label = QLabel("📋 Progress Log:")
        log_label.setStyleSheet("font-weight: bold; margin-bottom: 5px;")
        progress_layout.addWidget(log_label)

        self.log_text = QTextEdit()
        self.log_text.setMinimumHeight(200)
        self.log_text.setReadOnly(True)
        self.log_text.setStyleSheet("""
            border: 2px solid #ddd;
            border-radius: 5px;
            background-color: #fafafa;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 11px;
        """)
        self.log_text.setPlaceholderText("Progress and status messages will appear here...")
        progress_layout.addWidget(self.log_text)

        layout.addWidget(progress_group)

        self.setLayout(layout)

    def open_frame_region_selector(self):
        """Open frame and region selector for dataset building"""
        try:
            from frame_selector import FrameSelectorWidget
            from PyQt6.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QPushButton

            # Create dialog
            dialog = QDialog(self)
            dialog.setWindowTitle("Select Training Frames & OCR Region")
            dialog.setMinimumSize(900, 800)

            layout = QVBoxLayout()

            # Info label
            info_label = QLabel(
                "1. Load a video from the channel to extract training frames\n"
                "2. Use the slider to browse through frames\n"
                "3. Select the region where codes typically appear\n"
                "4. This configuration will be used for dataset building and model training"
            )
            info_label.setStyleSheet("color: #666; margin: 10px; padding: 10px; background-color: #f0f0f0; border-radius: 5px;")
            info_label.setWordWrap(True)
            layout.addWidget(info_label)

            # Frame selector
            frame_selector = FrameSelectorWidget("Training Data Frame & Region Selector")

            # Set default URL if available
            if self.channel_input.text().strip():
                frame_selector.url_input.setText(self.channel_input.text().strip())

            # Connect signals
            frame_selector.region_changed.connect(lambda region: setattr(self, 'dataset_region', region))
            frame_selector.frame_selected.connect(lambda frame: setattr(self, 'selected_frame', frame))

            layout.addWidget(frame_selector)

            # Buttons
            button_layout = QHBoxLayout()

            cancel_btn = QPushButton("Cancel")
            cancel_btn.clicked.connect(dialog.reject)
            button_layout.addWidget(cancel_btn)

            button_layout.addStretch()

            apply_btn = QPushButton("Apply Configuration")
            apply_btn.clicked.connect(lambda: self.apply_frame_region_config(frame_selector, dialog))
            button_layout.addWidget(apply_btn)

            layout.addLayout(button_layout)
            dialog.setLayout(layout)

            dialog.exec()

        except Exception as e:
            self.log_progress(f"❌ Error opening frame/region selector: {str(e)}")

    def apply_frame_region_config(self, frame_selector, dialog):
        """Apply the selected frame and region configuration"""
        self.dataset_region = frame_selector.get_region_percent()
        self.selected_frame = frame_selector.get_current_frame()

        x, y, w, h = self.dataset_region
        self.log_progress(f"✅ Dataset configuration updated:")
        self.log_progress(f"   OCR region: {x:.1f}%, {y:.1f}%, {w:.1f}% × {h:.1f}%")
        if self.selected_frame is not None:
            self.log_progress(f"   Reference frame selected for training")

        dialog.accept()

    def start_dataset_builder(self):
        """Start dataset building"""
        channel_url = self.channel_input.text().strip()

        try:
            num_frames = int(self.frames_input.text())
        except ValueError:
            num_frames = 50

        if not channel_url:
            self.log_progress("❌ Please enter a YouTube channel URL")
            return

        self.start_dataset_signal.emit(channel_url, num_frames, str(self.dataset_region))
        self.start_dataset_button.setEnabled(False)
        self.train_button.setEnabled(False)
        self.progress_bar.setVisible(True)

    def start_training(self):
        """Start model training"""
        self.start_training_signal.emit()
        self.start_dataset_button.setEnabled(False)
        self.train_button.setEnabled(False)
        self.progress_bar.setVisible(True)

    def update_progress(self, value: int):
        """Update progress bar"""
        self.progress_bar.setValue(value)

    def log_progress(self, message: str):
        """Add a message to the progress log"""
        self.log_text.append(message)
        # Auto-scroll to bottom
        scrollbar = self.log_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())

    def reset_ui_state(self):
        """Reset UI to idle state"""
        self.start_dataset_button.setEnabled(True)
        self.train_button.setEnabled(True)
        self.progress_bar.setVisible(False)


class MFFUHijackGUI(QMainWindow):
    """Main application window with simple, clean design"""

    def __init__(self):
        super().__init__()
        self.live_bot = None
        self.dataset_builder = None
        self.model_trainer = None
        self.live_scan_monitor = None
        self.init_ui()
        self.setup_connections()

    def init_ui(self):
        """Initialize the main UI"""
        self.setWindowTitle("MFFUHijack - Real-Time OCR Livestream Code Detection")
        self.setMinimumSize(1200, 800)
        self.resize(1400, 900)  # Set a good default size

        # Create central widget and tab widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Main layout with proper margins
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(15, 15, 15, 15)  # Add margins around the entire content
        layout.setSpacing(10)  # Add spacing between elements

        # Create tab widget with styling
        self.tab_widget = QTabWidget()
        self.tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #c0c0c0;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #f0f0f0;
                border: 1px solid #c0c0c0;
                padding: 8px 16px;
                margin-right: 2px;
            }
            QTabBar::tab:selected {
                background-color: white;
                border-bottom-color: white;
            }
            QTabBar::tab:hover {
                background-color: #e0e0e0;
            }
        """)

        # Create tabs
        self.livestream_tab = LivestreamTab()
        self.dataset_tab = DatasetTab()

        # Add tabs
        self.tab_widget.addTab(self.livestream_tab, "🎯 Livestream Bot")
        self.tab_widget.addTab(self.dataset_tab, "📊 Dataset + Training")

        layout.addWidget(self.tab_widget)

    def setup_connections(self):
        """Setup signal connections between components"""
        # Import live bot and other components
        try:
            from live_bot import LiveBot
            from dataset_builder import DatasetBuilder
            from model_trainer import ModelTrainer

            self.live_bot = LiveBot()
            self.dataset_builder = DatasetBuilder()
            self.model_trainer = ModelTrainer()

            # Create Live Scan Monitor window
            from live_scan_monitor import LiveScanMonitorWindow
            self.live_scan_monitor = LiveScanMonitorWindow()
            self.live_bot.set_monitor_window(self.live_scan_monitor)

            # Connect livestream tab signals
            self.livestream_tab.start_bot_signal.connect(self.start_livestream_bot)
            self.livestream_tab.stop_bot_signal.connect(self.stop_livestream_bot)

            # Connect dataset tab signals
            self.dataset_tab.start_dataset_signal.connect(self.start_dataset_builder)
            self.dataset_tab.start_training_signal.connect(self.start_model_training)

            # Connect live bot signals
            self.live_bot.frame_captured.connect(self.on_frame_captured)
            self.live_bot.code_detected.connect(self.on_code_detected)
            self.live_bot.status_changed.connect(self.on_status_changed)
            self.live_bot.error_occurred.connect(self.on_error_occurred)

            # Connect dataset builder signals
            self.dataset_builder.progress_update.connect(self.dataset_tab.update_progress)
            self.dataset_builder.status_update.connect(self.dataset_tab.log_progress)
            self.dataset_builder.finished.connect(self.on_dataset_finished)

            # Connect model trainer signals
            self.model_trainer.progress_update.connect(self.dataset_tab.update_progress)
            self.model_trainer.status_update.connect(self.dataset_tab.log_progress)
            self.model_trainer.finished.connect(self.on_training_finished)

        except Exception as e:
            logger.error(f"Error setting up connections: {e}")

    def start_livestream_bot(self, url: str, ocr_engine: str, interval: float, speed: str):
        """Start the livestream bot with speed optimization"""
        # Parse speed multiplier from speed setting
        speed_multiplier = 1.0
        if "1.5x" in speed:
            speed_multiplier = 1.5
        elif "2.0x" in speed:
            speed_multiplier = 2.0

        self.livestream_tab.log_message(f"⚡ Starting bot with {speed} playback for latest frames")

        # Pass the custom region and speed to the bot
        success = self.live_bot.start(url, ocr_engine, interval, region=self.livestream_tab.ocr_region, speed_multiplier=speed_multiplier)
        if not success:
            self.livestream_tab.log_message("ERROR: Failed to start bot")
            self.livestream_tab.update_status("Error")
            # Reset UI state
            self.livestream_tab.start_button.setEnabled(True)
            self.livestream_tab.stop_button.setEnabled(False)

    def stop_livestream_bot(self):
        """Stop the livestream bot"""
        self.live_bot.stop()
        self.livestream_tab.update_status("Stopped")

    def start_dataset_builder(self, channel_url: str, num_frames: int, region: str):
        """Start dataset building"""
        try:
            # Convert region string back to tuple
            region_tuple = eval(region) if region else (0, 67, 100, 33)
            self.dataset_builder.start(channel_url, num_frames, region_tuple)
        except Exception as e:
            self.dataset_tab.log_progress(f"ERROR: Failed to start dataset builder: {e}")
            self.dataset_tab.reset_ui_state()

    def start_model_training(self):
        """Start model training"""
        try:
            self.model_trainer.start()
        except Exception as e:
            self.dataset_tab.log_progress(f"ERROR: Failed to start training: {e}")
            self.dataset_tab.reset_ui_state()

    def on_frame_captured(self, pixmap):
        """Handle frame capture"""
        self.livestream_tab.update_frame_preview(pixmap)

    def on_code_detected(self, code_data):
        """Handle code detection and automatic submission"""
        code = code_data.get('code', 'Unknown')
        account_type = code_data.get('type', 'Unknown')
        amount = code_data.get('amount', 'Unknown')
        confidence = code_data.get('confidence', 0.0)

        message = f"🎯 DETECTED: {code} | Type: {account_type} | Amount: ${amount} | Confidence: {confidence:.2f}"
        self.livestream_tab.log_message(message)

        # Attempt automatic browser submission
        self.submit_code_automatically(code, account_type)

    def submit_code_automatically(self, code: str, account_type: str):
        """Automatically submit code using browser automation"""
        try:
            from browser_automation import browser_automation

            # Get payment information from GUI
            cvv = self.livestream_tab.cvv_input.text().strip()
            reset_account = self.livestream_tab.reset_account_input.text().strip()

            if not cvv:
                self.livestream_tab.log_message("❌ CVV not provided - cannot submit code automatically")
                return

            if account_type.lower() == "reset" and not reset_account:
                self.livestream_tab.log_message("❌ Reset account number not provided - cannot submit reset code")
                return

            self.livestream_tab.log_message(f"🚀 Attempting automatic submission of code: {code}")

            # Submit code in a separate thread to avoid blocking GUI
            import threading
            submission_thread = threading.Thread(
                target=self._submit_code_thread,
                args=(code, account_type, cvv, reset_account),
                daemon=True
            )
            submission_thread.start()

        except ImportError:
            self.livestream_tab.log_message("❌ Browser automation not available - code not submitted automatically")
        except Exception as e:
            self.livestream_tab.log_message(f"❌ Automatic submission error: {str(e)}")

    def _submit_code_thread(self, code: str, account_type: str, cvv: str, reset_account: str):
        """Submit code in separate thread"""
        try:
            from browser_automation import browser_automation

            success = browser_automation.submit_code(code, account_type, cvv, reset_account)

            if success:
                self.livestream_tab.log_message(f"✅ Code {code} submitted successfully!")
            else:
                self.livestream_tab.log_message(f"❌ Failed to submit code {code}")

        except Exception as e:
            self.livestream_tab.log_message(f"❌ Code submission thread error: {str(e)}")

    def on_status_changed(self, status):
        """Handle status change"""
        self.livestream_tab.update_status(status)

    def on_error_occurred(self, error):
        """Handle error"""
        self.livestream_tab.log_message(f"ERROR: {error}")
        self.livestream_tab.update_status("Error")
        # Reset UI state
        self.livestream_tab.start_button.setEnabled(True)
        self.livestream_tab.stop_button.setEnabled(False)

    def on_dataset_finished(self):
        """Handle dataset building completion"""
        self.dataset_tab.log_progress("✅ Dataset building completed")
        self.dataset_tab.reset_ui_state()

    def on_training_finished(self):
        """Handle training completion"""
        self.dataset_tab.log_progress("✅ Model training completed")
        self.dataset_tab.reset_ui_state()


if __name__ == "__main__":
    from PyQt6.QtWidgets import QApplication

    app = QApplication(sys.argv)

    # Set application properties
    app.setApplicationName("MFFUHijack")
    app.setApplicationVersion("1.0")
    app.setOrganizationName("MFFUHijack")

    # Create and show main window
    window = MFFUHijackGUI()
    window.show()

    sys.exit(app.exec())
