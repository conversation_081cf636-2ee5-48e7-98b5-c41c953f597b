"""
Dialog classes for the Enhanced Live Scan Monitor
Contains settings dialog, session history dialog, and other UI dialogs
"""

from PyQt6.QtWidgets import *
from PyQt6.QtCore import *
from PyQt6.QtGui import *
import json
from datetime import datetime


class SettingsDialog(QDialog):
    """Settings dialog for Live Scan Monitor"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent_window = parent
        self.setWindowTitle("Live Scan Monitor Settings")
        self.setModal(True)
        self.resize(500, 400)
        
        self.init_ui()
        self.load_current_settings()
    
    def init_ui(self):
        """Initialize settings UI"""
        layout = QVBoxLayout(self)
        
        # Create tabs for different setting categories
        tab_widget = QTabWidget()
        
        # General settings tab
        general_tab = self.create_general_tab()
        tab_widget.addTab(general_tab, "General")
        
        # Notifications tab
        notifications_tab = self.create_notifications_tab()
        tab_widget.addTab(notifications_tab, "Notifications")
        
        # Performance tab
        performance_tab = self.create_performance_tab()
        tab_widget.addTab(performance_tab, "Performance")
        
        # Appearance tab
        appearance_tab = self.create_appearance_tab()
        tab_widget.addTab(appearance_tab, "Appearance")
        
        layout.addWidget(tab_widget)
        
        # Buttons
        buttons = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | 
            QDialogButtonBox.StandardButton.Cancel | 
            QDialogButtonBox.StandardButton.Apply
        )
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        buttons.button(QDialogButtonBox.StandardButton.Apply).clicked.connect(self.apply_settings)
        layout.addWidget(buttons)
    
    def create_general_tab(self):
        """Create general settings tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Auto-start monitoring
        self.auto_start_checkbox = QCheckBox("Automatically start monitoring when bot starts")
        layout.addWidget(self.auto_start_checkbox)
        
        # Auto-save screenshots
        self.auto_screenshot_checkbox = QCheckBox("Automatically take screenshots of detected codes")
        layout.addWidget(self.auto_screenshot_checkbox)
        
        # Screenshot directory
        screenshot_group = QGroupBox("Screenshot Settings")
        screenshot_layout = QVBoxLayout(screenshot_group)
        
        dir_layout = QHBoxLayout()
        dir_layout.addWidget(QLabel("Screenshots Directory:"))
        self.screenshot_dir_edit = QLineEdit()
        dir_layout.addWidget(self.screenshot_dir_edit)
        
        browse_btn = QPushButton("Browse...")
        browse_btn.clicked.connect(self.browse_screenshot_dir)
        dir_layout.addWidget(browse_btn)
        
        screenshot_layout.addLayout(dir_layout)
        layout.addWidget(screenshot_group)
        
        # Session settings
        session_group = QGroupBox("Session Settings")
        session_layout = QVBoxLayout(session_group)
        
        # Max session history
        history_layout = QHBoxLayout()
        history_layout.addWidget(QLabel("Maximum session history:"))
        self.max_history_spin = QSpinBox()
        self.max_history_spin.setRange(10, 1000)
        self.max_history_spin.setValue(100)
        history_layout.addWidget(self.max_history_spin)
        history_layout.addStretch()
        
        session_layout.addLayout(history_layout)
        layout.addWidget(session_group)
        
        layout.addStretch()
        return widget
    
    def create_notifications_tab(self):
        """Create notifications settings tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Enable notifications
        self.notifications_enabled_checkbox = QCheckBox("Enable desktop notifications")
        layout.addWidget(self.notifications_enabled_checkbox)
        
        # Notification types
        types_group = QGroupBox("Notification Types")
        types_layout = QVBoxLayout(types_group)
        
        self.notify_valid_codes_checkbox = QCheckBox("Valid code detections")
        types_layout.addWidget(self.notify_valid_codes_checkbox)
        
        self.notify_invalid_codes_checkbox = QCheckBox("Invalid code detections")
        types_layout.addWidget(self.notify_invalid_codes_checkbox)
        
        self.notify_session_events_checkbox = QCheckBox("Session start/stop events")
        types_layout.addWidget(self.notify_session_events_checkbox)
        
        self.notify_milestones_checkbox = QCheckBox("Milestone achievements")
        types_layout.addWidget(self.notify_milestones_checkbox)
        
        layout.addWidget(types_group)
        
        # Sound settings
        sound_group = QGroupBox("Sound Settings")
        sound_layout = QVBoxLayout(sound_group)
        
        self.sound_enabled_checkbox = QCheckBox("Enable notification sounds")
        sound_layout.addWidget(self.sound_enabled_checkbox)
        
        layout.addWidget(sound_group)
        
        layout.addStretch()
        return widget
    
    def create_performance_tab(self):
        """Create performance settings tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Update intervals
        intervals_group = QGroupBox("Update Intervals")
        intervals_layout = QGridLayout(intervals_group)
        
        intervals_layout.addWidget(QLabel("UI Update Interval (ms):"), 0, 0)
        self.ui_update_spin = QSpinBox()
        self.ui_update_spin.setRange(100, 5000)
        self.ui_update_spin.setValue(1000)
        intervals_layout.addWidget(self.ui_update_spin, 0, 1)
        
        intervals_layout.addWidget(QLabel("Performance Update Interval (ms):"), 1, 0)
        self.perf_update_spin = QSpinBox()
        self.perf_update_spin.setRange(500, 10000)
        self.perf_update_spin.setValue(1000)
        intervals_layout.addWidget(self.perf_update_spin, 1, 1)
        
        layout.addWidget(intervals_group)
        
        # Memory management
        memory_group = QGroupBox("Memory Management")
        memory_layout = QVBoxLayout(memory_group)
        
        # Max log entries
        log_layout = QHBoxLayout()
        log_layout.addWidget(QLabel("Maximum log entries:"))
        self.max_log_entries_spin = QSpinBox()
        self.max_log_entries_spin.setRange(100, 10000)
        self.max_log_entries_spin.setValue(1000)
        log_layout.addWidget(self.max_log_entries_spin)
        log_layout.addStretch()
        
        memory_layout.addLayout(log_layout)
        layout.addWidget(memory_group)
        
        layout.addStretch()
        return widget
    
    def create_appearance_tab(self):
        """Create appearance settings tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Theme selection
        theme_group = QGroupBox("Theme")
        theme_layout = QVBoxLayout(theme_group)
        
        self.theme_light_radio = QRadioButton("Light Theme")
        self.theme_dark_radio = QRadioButton("Dark Theme")
        
        theme_layout.addWidget(self.theme_light_radio)
        theme_layout.addWidget(self.theme_dark_radio)
        
        layout.addWidget(theme_group)
        
        # Font settings
        font_group = QGroupBox("Font Settings")
        font_layout = QGridLayout(font_group)
        
        font_layout.addWidget(QLabel("Font Size Scale:"), 0, 0)
        self.font_scale_slider = QSlider(Qt.Orientation.Horizontal)
        self.font_scale_slider.setRange(80, 150)
        self.font_scale_slider.setValue(100)
        font_layout.addWidget(self.font_scale_slider, 0, 1)
        
        self.font_scale_label = QLabel("100%")
        font_layout.addWidget(self.font_scale_label, 0, 2)
        
        self.font_scale_slider.valueChanged.connect(
            lambda v: self.font_scale_label.setText(f"{v}%")
        )
        
        layout.addWidget(font_group)
        
        # Window settings
        window_group = QGroupBox("Window Settings")
        window_layout = QVBoxLayout(window_group)
        
        self.always_on_top_checkbox = QCheckBox("Always keep window on top")
        window_layout.addWidget(self.always_on_top_checkbox)
        
        self.remember_position_checkbox = QCheckBox("Remember window position and size")
        window_layout.addWidget(self.remember_position_checkbox)
        
        layout.addWidget(window_group)
        
        layout.addStretch()
        return widget
    
    def browse_screenshot_dir(self):
        """Browse for screenshot directory"""
        directory = QFileDialog.getExistingDirectory(
            self, "Select Screenshots Directory", 
            self.screenshot_dir_edit.text()
        )
        if directory:
            self.screenshot_dir_edit.setText(directory)
    
    def load_current_settings(self):
        """Load current settings from parent window"""
        if not self.parent_window:
            return
        
        # Load settings from parent window
        settings = self.parent_window.settings
        
        # General settings
        self.auto_start_checkbox.setChecked(settings.value("auto_start", True, type=bool))
        self.auto_screenshot_checkbox.setChecked(settings.value("auto_screenshot", False, type=bool))
        self.screenshot_dir_edit.setText(settings.value("screenshot_dir", "screenshots"))
        self.max_history_spin.setValue(settings.value("max_history", 100, type=int))
        
        # Notifications
        self.notifications_enabled_checkbox.setChecked(
            self.parent_window.notification_manager.notifications_enabled
        )
        self.notify_valid_codes_checkbox.setChecked(settings.value("notify_valid_codes", True, type=bool))
        self.notify_invalid_codes_checkbox.setChecked(settings.value("notify_invalid_codes", False, type=bool))
        self.notify_session_events_checkbox.setChecked(settings.value("notify_session_events", True, type=bool))
        self.notify_milestones_checkbox.setChecked(settings.value("notify_milestones", True, type=bool))
        self.sound_enabled_checkbox.setChecked(
            self.parent_window.notification_manager.sound_enabled
        )
        
        # Performance
        self.ui_update_spin.setValue(settings.value("ui_update_interval", 1000, type=int))
        self.perf_update_spin.setValue(settings.value("perf_update_interval", 1000, type=int))
        self.max_log_entries_spin.setValue(settings.value("max_log_entries", 1000, type=int))
        
        # Appearance
        current_theme = self.parent_window.theme_manager.current_theme
        if current_theme == "light":
            self.theme_light_radio.setChecked(True)
        else:
            self.theme_dark_radio.setChecked(True)
        
        self.font_scale_slider.setValue(settings.value("font_scale", 100, type=int))
        self.always_on_top_checkbox.setChecked(settings.value("always_on_top", False, type=bool))
        self.remember_position_checkbox.setChecked(settings.value("remember_position", True, type=bool))
    
    def apply_settings(self):
        """Apply settings without closing dialog"""
        self.save_settings()
    
    def save_settings(self):
        """Save settings to parent window"""
        if not self.parent_window:
            return
        
        settings = self.parent_window.settings
        
        # General settings
        settings.setValue("auto_start", self.auto_start_checkbox.isChecked())
        settings.setValue("auto_screenshot", self.auto_screenshot_checkbox.isChecked())
        settings.setValue("screenshot_dir", self.screenshot_dir_edit.text())
        settings.setValue("max_history", self.max_history_spin.value())
        
        # Notifications
        self.parent_window.notification_manager.set_notifications_enabled(
            self.notifications_enabled_checkbox.isChecked()
        )
        settings.setValue("notify_valid_codes", self.notify_valid_codes_checkbox.isChecked())
        settings.setValue("notify_invalid_codes", self.notify_invalid_codes_checkbox.isChecked())
        settings.setValue("notify_session_events", self.notify_session_events_checkbox.isChecked())
        settings.setValue("notify_milestones", self.notify_milestones_checkbox.isChecked())
        self.parent_window.notification_manager.set_sound_enabled(
            self.sound_enabled_checkbox.isChecked()
        )
        
        # Performance
        settings.setValue("ui_update_interval", self.ui_update_spin.value())
        settings.setValue("perf_update_interval", self.perf_update_spin.value())
        settings.setValue("max_log_entries", self.max_log_entries_spin.value())
        
        # Update timers
        self.parent_window.update_timer.setInterval(self.ui_update_spin.value())
        self.parent_window.performance_timer.setInterval(self.perf_update_spin.value())
        
        # Appearance
        new_theme = "light" if self.theme_light_radio.isChecked() else "dark"
        if new_theme != self.parent_window.theme_manager.current_theme:
            self.parent_window.change_theme(new_theme)
        
        settings.setValue("font_scale", self.font_scale_slider.value())
        settings.setValue("always_on_top", self.always_on_top_checkbox.isChecked())
        settings.setValue("remember_position", self.remember_position_checkbox.isChecked())
        
        # Apply window flags
        if self.always_on_top_checkbox.isChecked():
            self.parent_window.setWindowFlags(
                self.parent_window.windowFlags() | Qt.WindowType.WindowStaysOnTopHint
            )
        else:
            self.parent_window.setWindowFlags(
                self.parent_window.windowFlags() & ~Qt.WindowType.WindowStaysOnTopHint
            )
        self.parent_window.show()
        
        # Update screenshot directory
        self.parent_window.screenshots_dir = self.screenshot_dir_edit.text()
        self.parent_window.ensure_screenshots_dir()
    
    def accept(self):
        """Accept and save settings"""
        self.save_settings()
        super().accept()


class SessionHistoryDialog(QDialog):
    """Dialog to view session history"""
    
    def __init__(self, session_history, parent=None):
        super().__init__(parent)
        self.session_history = session_history
        self.setWindowTitle("Session History")
        self.setModal(True)
        self.resize(800, 600)
        
        self.init_ui()
        self.load_sessions()
    
    def init_ui(self):
        """Initialize history UI"""
        layout = QVBoxLayout(self)
        
        # Sessions tree
        self.sessions_tree = QTreeWidget()
        self.sessions_tree.setHeaderLabels([
            "Session ID", "Stream URL", "Start Time", "Duration", 
            "Total Codes", "Valid Codes", "Success Rate"
        ])
        self.sessions_tree.header().setSectionResizeMode(QHeaderView.ResizeMode.ResizeToContents)
        layout.addWidget(self.sessions_tree)
        
        # Buttons
        buttons_layout = QHBoxLayout()
        
        export_btn = QPushButton("Export Selected")
        export_btn.clicked.connect(self.export_selected)
        buttons_layout.addWidget(export_btn)
        
        delete_btn = QPushButton("Delete Selected")
        delete_btn.clicked.connect(self.delete_selected)
        buttons_layout.addWidget(delete_btn)
        
        buttons_layout.addStretch()
        
        close_btn = QPushButton("Close")
        close_btn.clicked.connect(self.accept)
        buttons_layout.addWidget(close_btn)
        
        layout.addLayout(buttons_layout)
    
    def load_sessions(self):
        """Load sessions into tree"""
        self.sessions_tree.clear()
        
        for session in reversed(self.session_history.sessions):  # Most recent first
            duration = "N/A"
            if session.get('end_time'):
                start = datetime.fromisoformat(session['start_time'])
                end = datetime.fromisoformat(session['end_time'])
                duration = str(end - start).split('.')[0]  # Remove microseconds
            
            success_rate = 0
            if session['total_codes'] > 0:
                success_rate = (session['valid_codes'] / session['total_codes']) * 100
            
            item = QTreeWidgetItem([
                session['id'],
                session['stream_url'][:50] + "..." if len(session['stream_url']) > 50 else session['stream_url'],
                session['start_time'][:19].replace('T', ' '),  # Format datetime
                duration,
                str(session['total_codes']),
                str(session['valid_codes']),
                f"{success_rate:.1f}%"
            ])
            
            # Store full session data
            item.setData(0, Qt.ItemDataRole.UserRole, session)
            self.sessions_tree.addTopLevelItem(item)
    
    def export_selected(self):
        """Export selected session"""
        current_item = self.sessions_tree.currentItem()
        if not current_item:
            QMessageBox.information(self, "No Selection", "Please select a session to export.")
            return
        
        session_data = current_item.data(0, Qt.ItemDataRole.UserRole)
        
        filename, _ = QFileDialog.getSaveFileName(
            self, "Export Session", 
            f"{session_data['id']}.json",
            "JSON Files (*.json)"
        )
        
        if filename:
            try:
                with open(filename, 'w') as f:
                    json.dump(session_data, f, indent=2)
                QMessageBox.information(self, "Export Successful", f"Session exported to {filename}")
            except Exception as e:
                QMessageBox.critical(self, "Export Failed", f"Failed to export session: {e}")
    
    def delete_selected(self):
        """Delete selected session"""
        current_item = self.sessions_tree.currentItem()
        if not current_item:
            QMessageBox.information(self, "No Selection", "Please select a session to delete.")
            return
        
        session_data = current_item.data(0, Qt.ItemDataRole.UserRole)
        
        reply = QMessageBox.question(
            self, "Confirm Delete", 
            f"Are you sure you want to delete session '{session_data['id']}'?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            # Remove from history
            self.session_history.sessions = [
                s for s in self.session_history.sessions 
                if s['id'] != session_data['id']
            ]
            self.session_history.save_history()
            
            # Remove from tree
            index = self.sessions_tree.indexOfTopLevelItem(current_item)
            self.sessions_tree.takeTopLevelItem(index)
            
            QMessageBox.information(self, "Deleted", "Session deleted successfully.")
