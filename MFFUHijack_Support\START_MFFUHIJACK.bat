@echo off
title MFFUHijack - Real-Time OCR Livestream Code Sniping Bot

echo.
echo ========================================
echo  MFFUHijack - Auto-Start
echo ========================================
echo.
echo 🚀 Starting MFFUHijack with automatic setup...
echo.
echo First-time users: All dependencies will be
echo installed automatically. This may take a
echo few minutes depending on your internet speed.
echo.

REM Try to find Python
python --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Python found
    echo.
    echo 🎯 Launching MFFUHijack...
    python main.py
) else (
    echo ❌ Python not found in PATH
    echo.
    echo Please install Python 3.8 or higher from:
    echo https://www.python.org/downloads/
    echo.
    echo Make sure to check "Add Python to PATH" during installation.
    echo.
    pause
)

echo.
echo 👋 MFFUHijack has closed.
pause
