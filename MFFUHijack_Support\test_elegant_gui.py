#!/usr/bin/env python3
"""
Test script for the elegant MFFUHijack GUI
Demonstrates the new clean, optimized design with proper sizing
"""

import sys
import os
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QTimer

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from elegant_gui import ElegantMFFUHijackGUI, message_logger
except ImportError as e:
    print(f"Import error: {e}")
    print("Make sure all required modules are available")
    sys.exit(1)


def main():
    """Main function to run the elegant GUI"""
    # Create QApplication
    app = QApplication(sys.argv)
    
    # Set application properties
    app.setApplicationName("MFFUHijack")
    app.setApplicationVersion("2.0")
    app.setOrganizationName("MFFUHijack")
    
    try:
        # Create and show main window
        main_window = ElegantMFFUHijackGUI()
        main_window.show()
        
        # Log some demo messages to show the logger
        def demo_messages():
            message_logger.info("Elegant GUI initialized successfully")
            message_logger.success("All components loaded properly")
            message_logger.warning("This is a demo warning message")
            message_logger.info("GUI is ready for use")
        
        # Schedule demo messages after a short delay
        QTimer.singleShot(1000, demo_messages)
        
        # Run the application
        sys.exit(app.exec())
        
    except Exception as e:
        print(f"Error running application: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
