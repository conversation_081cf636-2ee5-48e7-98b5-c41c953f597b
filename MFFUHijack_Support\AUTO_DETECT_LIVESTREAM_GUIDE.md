# Auto-Detect Latest Livestream Feature Guide

## 🎯 Overview

MFFUHijack now includes an **Auto-Detect Latest Livestream** feature that automatically finds and opens the most recent active livestream from any YouTube channel. This eliminates the need to manually find and copy livestream URLs.

## 🚀 How to Use

### **Step 1: Enter Channel URL**
1. **Open MFFUHijack**
2. **In the "Auto-Detect Channel" field**, enter the YouTube channel URL
3. **Default channel**: `https://www.youtube.com/@MyFundedFuturesPropFirm`

### **Step 2: Auto-Detect Livestream**
1. **Click the "🔍 Auto-Detect Latest Stream" button**
2. **Wait for detection** (button will show "🔍 Detecting...")
3. **If found**: The livestream URL will automatically populate the "Livestream URL" field
4. **If not found**: You'll see a message that no active livestream was detected

### **Step 3: Start Bot**
1. **Verify the detected URL** in the "Livestream URL" field
2. **Configure OCR settings** as needed
3. **Click "▶️ Start Bot"** to begin monitoring

## 📺 Supported Channel URL Formats

The auto-detect feature supports all standard YouTube channel URL formats:

```
✅ @username format:
https://www.youtube.com/@MyFundedFuturesPropFirm
https://www.youtube.com/@NASA
https://www.youtube.com/@SpaceX

✅ /c/ format:
https://www.youtube.com/c/ChannelName

✅ /channel/ format:
https://www.youtube.com/channel/UC1234567890abcdef

✅ /user/ format:
https://www.youtube.com/user/Username
```

## 🔧 Technical Implementation

### **Detection Methods**

The system uses **two detection methods** for maximum reliability:

#### **Method 1: Direct Channel Scan**
- Scans the channel's recent videos
- Looks for videos with `live_status` indicating active streams
- Checks for "LIVE" keywords in video titles
- Examines video metadata for livestream indicators

#### **Method 2: Search-Based Detection**
- If Method 1 fails, performs a YouTube search
- Searches for: `[channel_name] live`
- Filters results for active livestreams
- Provides fallback when direct scanning doesn't work

### **Detection Criteria**

A video is considered an **active livestream** if:
- ✅ `live_status` is "is_live" or "live"
- ✅ `is_live` metadata is "True" or "1"
- ✅ Title contains "LIVE" (case-insensitive)
- ✅ Title contains "live" or "streaming"

### **Console Logging**

The detection process provides detailed logging:

```
🔍 Auto-detecting latest livestream from: https://www.youtube.com/@MyFundedFuturesPropFirm
📋 Getting channel information for: https://www.youtube.com/@MyFundedFuturesPropFirm
📺 Channel: My Funded Futures
🚀 Auto-detecting latest livestream from channel...
🔍 Searching for active livestreams from: https://www.youtube.com/@MyFundedFuturesPropFirm
🛠️  Running command: python -m yt_dlp --flat-playlist --print %(id)s|%(title)s|%(live_status)s|%(url)s|%(is_live)s --playlist-end 10 https://www.youtube.com/@MyFundedFuturesPropFirm
📤 yt-dlp return code: 0
   📺 Found: Friday: Live Futures Trading & Market Talk (Status: NA, is_live: NA) [DETECTED AS LIVESTREAM]
✅ Found latest livestream (Method 1):
   Title: Friday: Live Futures Trading & Market Talk
   URL: https://www.youtube.com/watch?v=abc123def456
```

## 🎭 GUI Integration

### **New GUI Elements**

#### **Auto-Detect Channel Field**
- **Location**: Top row of Configuration section
- **Purpose**: Enter the YouTube channel URL for auto-detection
- **Default**: `https://www.youtube.com/@MyFundedFuturesPropFirm`

#### **Auto-Detect Button**
- **Text**: "🔍 Auto-Detect Latest Stream"
- **Color**: Blue (#2196F3)
- **Behavior**: 
  - Changes to "🔍 Detecting..." while running
  - Runs in background thread (non-blocking)
  - Re-enables when complete

#### **Enhanced Layout**
```
Row 0: [Auto-Detect Channel: ________________] [🔍 Auto-Detect Latest Stream]
Row 1: [Livestream URL:     ________________] [                            ]
Row 2: [OCR Model: ____] [Polling Interval: __]
```

### **Background Processing**

The auto-detection runs in a **background thread** to prevent GUI freezing:

```python
def auto_detect_livestream(self):
    # Disable button and show progress
    self.auto_detect_button.setEnabled(False)
    self.auto_detect_button.setText("🔍 Detecting...")
    
    # Run detection in background thread
    def detect_stream():
        stream_url = auto_detect_livestream(channel_url)
        if stream_url:
            self.url_input.setText(stream_url)  # Populate URL field
    
    threading.Thread(target=detect_stream, daemon=True).start()
```

## 🛠️ Error Handling

### **Common Scenarios**

#### **No Active Livestream**
```
❌ No active livestreams found
   The channel may not be currently live
```
**Solution**: Try again later when the channel goes live

#### **Invalid Channel URL**
```
❌ Invalid channel URL: https://invalid-url.com
```
**Solution**: Use a valid YouTube channel URL format

#### **Network/Timeout Issues**
```
❌ yt-dlp command timed out (30 seconds)
```
**Solution**: Check internet connection and try again

#### **yt-dlp Not Available**
```
❌ Livestream detector not available
```
**Solution**: Ensure yt-dlp is properly installed

### **Fallback Behavior**

If auto-detection fails:
1. **Error message** is displayed in the log
2. **Button is re-enabled** for retry
3. **Manual URL entry** remains available
4. **User can try again** or enter URL manually

## 📋 Usage Examples

### **Example 1: MyFundedFuturesPropFirm (Default)**
1. **Channel URL**: `https://www.youtube.com/@MyFundedFuturesPropFirm`
2. **Click**: "🔍 Auto-Detect Latest Stream"
3. **Result**: Finds their daily trading livestreams
4. **URL**: `https://www.youtube.com/watch?v=abc123def456`

### **Example 2: Custom Channel**
1. **Enter**: `https://www.youtube.com/@NASA`
2. **Click**: "🔍 Auto-Detect Latest Stream"
3. **Result**: Finds NASA's live space coverage
4. **URL**: `https://www.youtube.com/watch?v=xyz789uvw012`

### **Example 3: No Active Stream**
1. **Enter**: Any channel URL
2. **Click**: "🔍 Auto-Detect Latest Stream"
3. **Result**: "❌ No active livestreams found"
4. **Action**: Try again later or enter URL manually

## 🎉 Benefits

### **For Users**
- ✅ **No manual URL hunting** - automatically finds latest streams
- ✅ **Always up-to-date** - gets the most recent livestream
- ✅ **Works with any channel** - not limited to specific channels
- ✅ **Fallback options** - multiple detection methods
- ✅ **Non-blocking** - doesn't freeze the GUI

### **For Monitoring**
- ✅ **Ensures live content** - only detects active streams
- ✅ **Reduces setup time** - one-click stream detection
- ✅ **Prevents stale URLs** - always gets current streams
- ✅ **Channel flexibility** - easily switch between channels

### **For Development**
- ✅ **Robust detection** - multiple fallback methods
- ✅ **Comprehensive logging** - detailed debug information
- ✅ **Error handling** - graceful failure recovery
- ✅ **Extensible design** - easy to add new detection methods

## 🔄 Workflow Integration

### **Typical Usage Flow**
1. **Start MFFUHijack**
2. **Default channel** is pre-filled: `@MyFundedFuturesPropFirm`
3. **Click "Auto-Detect"** to find latest stream
4. **Configure OCR settings** (model, region, interval)
5. **Start bot** to begin monitoring
6. **Bot automatically** processes the live stream

### **Multi-Channel Monitoring**
1. **Monitor Channel A**: Auto-detect and start bot
2. **Switch to Channel B**: Change channel URL, auto-detect
3. **Quick switching**: Save frequently used channel URLs
4. **Flexible monitoring**: Easy to adapt to different streams

The Auto-Detect Latest Livestream feature makes MFFUHijack much more user-friendly by eliminating the manual process of finding and copying livestream URLs!
