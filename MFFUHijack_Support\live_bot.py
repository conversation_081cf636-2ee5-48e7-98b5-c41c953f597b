"""
Livestream Bot Core Logic for MFFUHijack
Handles real-time frame capture, OCR processing, and code detection
"""

import cv2
import numpy as np
import time
import threading
from datetime import datetime
from typing import Optional, Callable
import logging
import subprocess
import os

from PyQt6.QtCore import QThread, pyqtSignal, QTimer, QObject
from PyQt6.QtGui import QPixmap, QImage

# Import our modules
from ocr_utils import ocr_manager
from submitter import code_submitter

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class LivestreamCapture:
    """Handles video capture from YouTube livestreams using yt-dlp and OpenCV"""
    
    def __init__(self):
        self.stream_url = None
        self.video_url = None
        self.cap = None
        self.is_active = False
    
    def get_stream_url(self, youtube_url: str) -> Optional[str]:
        """Extract direct video stream URL from YouTube URL using yt-dlp"""
        try:
            print(f"🔗 Extracting stream URL from: {youtube_url}")

            # Use yt-dlp to get the direct stream URL
            cmd = [
                'python', '-m', 'yt_dlp',
                '--get-url',
                '--format', 'best[height<=720]',  # Get best quality up to 720p
                youtube_url
            ]

            print(f"🛠️  Running command: {' '.join(cmd)}")

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)

            print(f"📤 yt-dlp return code: {result.returncode}")
            if result.stdout:
                print(f"📤 yt-dlp stdout: {result.stdout[:200]}...")
            if result.stderr:
                print(f"📤 yt-dlp stderr: {result.stderr}")

            if result.returncode == 0 and result.stdout.strip():
                stream_url = result.stdout.strip()
                print(f"✅ Successfully extracted stream URL: {stream_url[:100]}...")
                logger.info(f"Got stream URL: {stream_url[:100]}...")
                return stream_url
            else:
                print(f"❌ yt-dlp failed with return code {result.returncode}")
                print(f"   Error: {result.stderr}")
                logger.error(f"yt-dlp failed: {result.stderr}")
                return None

        except subprocess.TimeoutExpired:
            print("❌ yt-dlp command timed out (30 seconds)")
            logger.error("yt-dlp timeout")
            return None
        except Exception as e:
            print(f"❌ Exception during yt-dlp execution: {e}")
            logger.error(f"Failed to get stream URL: {e}")
            return None
    
    def start_capture(self, youtube_url: str) -> bool:
        """Start capturing from YouTube livestream"""
        print(f"🚀 Starting video capture for: {youtube_url}")
        self.stream_url = youtube_url

        # Get direct stream URL
        print("📡 Getting direct stream URL from yt-dlp...")
        self.video_url = self.get_stream_url(youtube_url)
        if not self.video_url:
            print("❌ Failed to get video stream URL")
            logger.error("Failed to get video stream URL")
            return False

        print(f"✅ Got stream URL: {self.video_url[:100]}...")

        # Initialize OpenCV capture with speed optimization
        try:
            print("🎥 Initializing OpenCV video capture with 1.5x speed...")
            self.cap = cv2.VideoCapture(self.video_url)
            if not self.cap.isOpened():
                print("❌ Failed to open video stream with OpenCV")
                logger.error("Failed to open video stream")
                return False

            # Set buffer size to reduce latency (critical for live streams)
            self.cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)

            # Get original video properties
            original_fps = self.cap.get(cv2.CAP_PROP_FPS)
            width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))

            # Calculate 1.5x speed FPS
            target_fps = original_fps * 1.5 if original_fps > 0 else 45  # Default to 45 if FPS unknown

            # Set playback speed to 1.5x by adjusting FPS
            try:
                self.cap.set(cv2.CAP_PROP_FPS, target_fps)
                actual_fps = self.cap.get(cv2.CAP_PROP_FPS)
                print(f"⚡ Speed optimization applied:")
                print(f"   Original FPS: {original_fps}")
                print(f"   Target FPS (1.5x): {target_fps}")
                print(f"   Actual FPS: {actual_fps}")
            except Exception as e:
                print(f"⚠️ Could not set FPS directly: {e}")
                print("   Will use frame skipping for speed control")

            print(f"📺 Video stream properties:")
            print(f"   Resolution: {width}x{height}")
            print(f"   Playback Speed: 1.5x (for latest frames)")
            print(f"   Buffer size: 1 (ultra-low latency)")
            print(f"   Speed mode: ENABLED for real-time code detection")

            self.is_active = True
            print("✅ Video capture started with 1.5x speed optimization!")
            logger.info("Video capture started with 1.5x speed")
            return True

        except Exception as e:
            error_msg = f"Failed to start video capture: {e}"
            print(f"❌ {error_msg}")
            logger.error(error_msg)
            return False
    
    def get_frame(self) -> Optional[np.ndarray]:
        """Get the latest frame from the stream with 1.5x speed optimization"""
        if not self.cap or not self.is_active:
            return None

        try:
            # For 1.5x speed: Clear buffer to get the most recent frame
            # This ensures we're not processing old frames from the buffer
            latest_frame = None
            frames_read = 0
            max_buffer_clear = 3  # Read up to 3 frames to clear buffer

            while frames_read < max_buffer_clear:
                ret, frame = self.cap.read()
                if ret:
                    latest_frame = frame
                    frames_read += 1
                else:
                    break

            if latest_frame is not None:
                # Successfully got the latest frame
                return latest_frame
            else:
                # Fallback to single frame read
                ret, frame = self.cap.read()
                if ret:
                    return frame
                else:
                    logger.warning("Failed to read frame")
                    return None

        except Exception as e:
            logger.error(f"Error reading frame: {e}")
            return None
    
    def stop_capture(self):
        """Stop video capture"""
        self.is_active = False
        if self.cap:
            self.cap.release()
            self.cap = None
        logger.info("Video capture stopped")


class LiveBotWorker(QThread):
    """Worker thread for the livestream bot"""
    
    # Signals
    frame_ready = pyqtSignal(np.ndarray)  # New frame available
    code_detected = pyqtSignal(dict)      # Code detected
    status_update = pyqtSignal(str)       # Status message
    error_occurred = pyqtSignal(str)      # Error message
    
    def __init__(self):
        super().__init__()
        self.capture = LivestreamCapture()
        self.is_running = False
        self.youtube_url = ""
        self.ocr_engine = "EasyOCR"
        self.polling_interval = 1.0
        self.region = (0, 67, 100, 33)  # Default: bottom third as percentages
        
    def configure(self, youtube_url: str, ocr_engine: str, polling_interval: float, region=None, speed_multiplier: float = 1.5):
        """Configure the bot parameters with speed optimization"""
        self.youtube_url = youtube_url
        self.ocr_engine = ocr_engine
        self.polling_interval = polling_interval
        self.speed_multiplier = speed_multiplier
        if region is not None:
            self.region = region

        # Set OCR engine
        if not ocr_manager.set_engine(ocr_engine):
            self.error_occurred.emit(f"Failed to set OCR engine: {ocr_engine}")
    
    def run(self):
        """Main bot loop"""
        self.is_running = True
        
        try:
            # Start video capture
            self.status_update.emit("Connecting to stream...")
            if not self.capture.start_capture(self.youtube_url):
                self.error_occurred.emit("Failed to connect to livestream")
                return
            
            self.status_update.emit("Connected - Processing frames...")
            
            frame_count = 0
            last_process_time = 0
            
            # Speed optimization variables
            skip_frame_counter = 0
            speed_multiplier = getattr(self, 'speed_multiplier', 1.5)  # Use configured speed or default to 1.5x

            while self.is_running:
                current_time = time.time()

                # Get frame with 1.5x speed optimization
                frame = self.capture.get_frame()
                if frame is None:
                    if frame_count % 50 == 0:  # Log every 50 failed attempts
                        print(f"⚠️  Frame capture failed (attempt {frame_count})")
                    time.sleep(0.1)
                    continue

                frame_count += 1
                skip_frame_counter += 1

                # SPEED OPTIMIZATION: Skip frames based on speed multiplier
                if speed_multiplier > 1.0:
                    skip_ratio = int(speed_multiplier * 2)  # 1.5x = skip every 3rd, 2.0x = skip every 4th
                    if skip_frame_counter % skip_ratio != 0:
                        # Skip this frame to maintain target speed
                        continue

                    # Reset skip counter
                    skip_frame_counter = 0

                # Log frame capture progress with speed info
                if frame_count % 30 == 0:  # Every 30 frames
                    print(f"📹 Captured {frame_count} frames ({speed_multiplier}x speed) | Resolution: {frame.shape[1]}x{frame.shape[0]} | Latest frame mode: ACTIVE")

                # Emit frame for preview (every few processed frames)
                if frame_count % 15 == 0:  # Reduced frequency due to speed
                    print(f"🖼️  Sending latest frame {frame_count} to preview ({speed_multiplier}x speed)")
                    self.frame_ready.emit(frame.copy())

                # Process frame for codes at specified interval
                if current_time - last_process_time >= self.polling_interval:
                    print(f"🔍 Processing LATEST frame {frame_count} for codes ({speed_multiplier}x speed, interval: {self.polling_interval}s)")
                    self.process_frame_for_codes(frame)
                    last_process_time = current_time

                # Reduced delay for 1.5x speed processing
                time.sleep(0.03)  # Reduced from 0.05 for faster processing
        
        except Exception as e:
            self.error_occurred.emit(f"Bot error: {str(e)}")
        
        finally:
            self.capture.stop_capture()
            self.status_update.emit("Stopped")
    
    def process_frame_for_codes(self, frame: np.ndarray):
        """Process a frame to detect and submit codes"""
        try:
            print(f"🔍 Starting OCR processing on frame...")
            print(f"   Frame shape: {frame.shape}")
            print(f"   OCR region: {self.region}")
            print(f"   OCR engine: {self.ocr_engine}")

            # Use OCR to find codes
            codes = ocr_manager.process_frame_for_codes(frame, self.region)

            print(f"📝 OCR processing complete. Found {len(codes) if codes else 0} codes")

            if codes:
                print(f"🎯 CODE DETECTION SUCCESS! Found {len(codes)} code(s):")
                self.status_update.emit(f"Found {len(codes)} code(s)")

                for i, code_data in enumerate(codes):
                    print(f"   Code {i+1}: {code_data}")

                    # Add timestamp
                    code_data['timestamp'] = datetime.now().isoformat()

                    # Emit code detected signal
                    self.code_detected.emit(code_data)

                    # Submit code
                    print(f"📤 Submitting code: {code_data.get('code', 'Unknown')}")
                    result = code_submitter.submit_code(code_data)
                    print(f"📥 Submission result: {result}")

                    if result.get('success'):
                        print(f"✅ Successfully submitted: {code_data['code']}")
                        self.status_update.emit(f"Submitted: {code_data['code']}")
                    elif result.get('duplicate'):
                        print(f"🔄 Duplicate code: {code_data['code']}")
                        self.status_update.emit(f"Duplicate: {code_data['code']}")
                    else:
                        error_msg = result.get('error', 'Unknown error')
                        print(f"❌ Submission failed: {code_data['code']} - {error_msg}")
                        self.status_update.emit(f"Failed: {code_data['code']} - {error_msg}")

            else:
                print("🔍 No codes detected in this frame")
                # Update status occasionally when no codes found
                if time.time() % 10 < 1:  # Every ~10 seconds
                    self.status_update.emit("Scanning for codes...")

        except Exception as e:
            error_msg = f"Error processing frame: {e}"
            print(f"❌ {error_msg}")
            logger.error(error_msg)
            self.error_occurred.emit(f"Frame processing error: {str(e)}")
    
    def stop(self):
        """Stop the bot"""
        self.is_running = False
        self.wait()  # Wait for thread to finish


class LiveBot(QObject):
    """Main livestream bot controller"""

    # Define signals that match what the GUI expects
    frame_captured = pyqtSignal(QPixmap)
    code_detected = pyqtSignal(dict)
    status_changed = pyqtSignal(str)
    error_occurred = pyqtSignal(str)

    def __init__(self):
        super().__init__()
        self.worker = None
        self.frame_callback = None
        self.code_callback = None
        self.status_callback = None
        self.error_callback = None
        self.monitor_window = None
    
    def set_callbacks(self, frame_cb=None, code_cb=None, status_cb=None, error_cb=None):
        """Set callback functions for bot events"""
        self.frame_callback = frame_cb
        self.code_callback = code_cb
        self.status_callback = status_cb
        self.error_callback = error_cb

    def set_monitor_window(self, monitor_window):
        """Set the live scan monitor window"""
        self.monitor_window = monitor_window

        # Connect signals to monitor window
        if monitor_window:
            self.frame_captured.connect(monitor_window.update_frame_preview)
            self.code_detected.connect(monitor_window.on_code_detected)
            self.status_changed.connect(monitor_window.log_activity)
            self.error_occurred.connect(lambda msg: monitor_window.log_activity(f"❌ Error: {msg}"))

    def open_monitor_window(self, stream_url: str):
        """Open and configure the monitor window for scanning"""
        if self.monitor_window:
            self.monitor_window.start_scanning_session(stream_url)
            self.monitor_window.show()
            self.monitor_window.raise_()
            self.monitor_window.activateWindow()

    def close_monitor_window(self):
        """Close the monitor window"""
        if self.monitor_window:
            self.monitor_window.stop_scanning_session()
            # Don't force close - let user decide
    
    def start(self, youtube_url: str, ocr_engine: str = "EasyOCR", polling_interval: float = 1.0, region=None, speed_multiplier: float = 1.5):
        """Start the livestream bot with speed optimization"""
        if self.worker and self.worker.isRunning():
            logger.warning("Bot is already running")
            return False

        # Create new worker
        self.worker = LiveBotWorker()
        self.worker.configure(youtube_url, ocr_engine, polling_interval, region, speed_multiplier)
        
        # Connect worker signals to LiveBot signals
        self.worker.frame_ready.connect(self._on_frame_ready)
        self.worker.code_detected.connect(self.code_detected.emit)
        self.worker.status_update.connect(self.status_changed.emit)
        self.worker.error_occurred.connect(self.error_occurred.emit)

        # Open monitor window if available
        self.open_monitor_window(youtube_url)

        # Connect signals to callbacks (for backward compatibility)
        if self.frame_callback:
            self.worker.frame_ready.connect(self.frame_callback)
        if self.code_callback:
            self.worker.code_detected.connect(self.code_callback)
        if self.status_callback:
            self.worker.status_update.connect(self.status_callback)
        if self.error_callback:
            self.worker.error_occurred.connect(self.error_callback)
        
        # Start worker
        self.worker.start()
        logger.info(f"Started livestream bot for: {youtube_url}")
        return True

    def _on_frame_ready(self, frame: np.ndarray):
        """Convert numpy frame to QPixmap and emit frame_captured signal"""
        try:
            pixmap = numpy_to_qpixmap(frame)
            self.frame_captured.emit(pixmap)
        except Exception as e:
            logger.error(f"Error converting frame to pixmap: {e}")
    
    def stop(self):
        """Stop the livestream bot"""
        if self.worker:
            self.worker.stop()
            self.worker = None
            logger.info("Stopped livestream bot")

        # Close monitor window
        self.close_monitor_window()
    
    def is_running(self) -> bool:
        """Check if bot is currently running"""
        return self.worker is not None and self.worker.isRunning()


def numpy_to_qpixmap(frame: np.ndarray) -> QPixmap:
    """Convert numpy array (OpenCV frame) to QPixmap for display"""
    try:
        # Convert BGR to RGB
        rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        
        # Get dimensions
        height, width, channel = rgb_frame.shape
        bytes_per_line = 3 * width
        
        # Create QImage
        q_image = QImage(rgb_frame.data, width, height, bytes_per_line, QImage.Format.Format_RGB888)
        
        # Convert to QPixmap
        pixmap = QPixmap.fromImage(q_image)
        
        # Scale to reasonable size for preview
        if pixmap.width() > 400:
            pixmap = pixmap.scaledToWidth(400)
        
        return pixmap
    
    except Exception as e:
        logger.error(f"Error converting frame to pixmap: {e}")
        return QPixmap()
