#!/usr/bin/env python3
"""
Test script for video capture and logging functionality
"""

import sys
import time
from pathlib import Path

# Add current directory to path
sys.path.append(str(Path(__file__).parent))

from live_bot import LiveBot, LivestreamCapture
from PyQt6.QtCore import QCoreApplication


def test_yt_dlp_url_extraction():
    """Test yt-dlp URL extraction with logging"""
    print("🧪 Testing yt-dlp URL extraction...")
    print("=" * 50)
    
    capture = LivestreamCapture()
    
    # Test with a known working YouTube URL (Rick Roll - always available)
    test_url = "https://www.youtube.com/watch?v=dQw4w9WgXcQ"
    
    print(f"Testing with URL: {test_url}")
    
    stream_url = capture.get_stream_url(test_url)
    
    if stream_url:
        print(f"✅ URL extraction successful!")
        print(f"Stream URL length: {len(stream_url)} characters")
        return True
    else:
        print(f"❌ URL extraction failed")
        return False


def test_video_capture_init():
    """Test video capture initialization"""
    print("\n🧪 Testing video capture initialization...")
    print("=" * 50)
    
    capture = LivestreamCapture()
    
    # Test with a known working YouTube URL
    test_url = "https://www.youtube.com/watch?v=dQw4w9WgXcQ"
    
    print(f"Testing video capture with: {test_url}")
    
    success = capture.start_capture(test_url)
    
    if success:
        print("✅ Video capture initialization successful!")
        
        # Try to get a few frames
        print("📹 Testing frame capture...")
        for i in range(5):
            frame = capture.get_frame()
            if frame is not None:
                print(f"   Frame {i+1}: {frame.shape}")
            else:
                print(f"   Frame {i+1}: Failed to capture")
            time.sleep(0.5)
        
        capture.stop_capture()
        return True
    else:
        print("❌ Video capture initialization failed")
        return False


def test_live_bot_signals():
    """Test LiveBot signal connections"""
    print("\n🧪 Testing LiveBot signal connections...")
    print("=" * 50)
    
    # Create QApplication for signal testing
    app = QCoreApplication(sys.argv)
    
    bot = LiveBot()
    
    # Test signal connections
    def on_frame(pixmap):
        print(f"📺 Received frame signal: {pixmap.size()}")
    
    def on_code(code_data):
        print(f"🎯 Received code signal: {code_data}")
    
    def on_status(status):
        print(f"📊 Status update: {status}")
    
    def on_error(error):
        print(f"❌ Error: {error}")
    
    # Connect signals
    bot.frame_captured.connect(on_frame)
    bot.code_detected.connect(on_code)
    bot.status_changed.connect(on_status)
    bot.error_occurred.connect(on_error)
    
    print("✅ Signal connections established")
    
    # Test starting the bot (this will fail but we can see the logging)
    print("\n🚀 Testing bot start (expect failure with detailed logging)...")
    test_url = "https://www.youtube.com/watch?v=dQw4w9WgXcQ"
    
    success = bot.start(test_url, "EasyOCR", 2.0, region=(0, 67, 100, 33))
    
    if success:
        print("✅ Bot started successfully!")
        print("⏳ Running for 10 seconds to test logging...")
        
        # Process events for 10 seconds
        start_time = time.time()
        while time.time() - start_time < 10:
            app.processEvents()
            time.sleep(0.1)
        
        bot.stop()
        print("⏹️  Bot stopped")
    else:
        print("❌ Bot failed to start (expected - check logs above)")
    
    return True


def main():
    """Run all tests"""
    print("🚀 Video Capture and Logging Test Suite")
    print("=" * 60)
    
    tests_passed = 0
    total_tests = 3
    
    # Test 1: yt-dlp URL extraction
    try:
        if test_yt_dlp_url_extraction():
            tests_passed += 1
            print("✅ Test 1 passed: yt-dlp URL extraction")
        else:
            print("❌ Test 1 failed: yt-dlp URL extraction")
    except Exception as e:
        print(f"❌ Test 1 error: {e}")
    
    # Test 2: Video capture initialization
    try:
        if test_video_capture_init():
            tests_passed += 1
            print("✅ Test 2 passed: Video capture initialization")
        else:
            print("❌ Test 2 failed: Video capture initialization")
    except Exception as e:
        print(f"❌ Test 2 error: {e}")
    
    # Test 3: LiveBot signals
    try:
        if test_live_bot_signals():
            tests_passed += 1
            print("✅ Test 3 passed: LiveBot signals")
        else:
            print("❌ Test 3 failed: LiveBot signals")
    except Exception as e:
        print(f"❌ Test 3 error: {e}")
    
    # Summary
    print("\n📊 Test Results")
    print("=" * 30)
    print(f"Tests passed: {tests_passed}/{total_tests}")
    
    if tests_passed >= 2:
        print("🎉 Most tests passed! Video capture system is working")
        return True
    else:
        print("⚠️  Multiple tests failed. Check the detailed logs above")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
