#!/usr/bin/env python3
"""
Test the new startup system for MFFUHijack
Tests dependency checking and GUI installer
"""

import sys
import os
import json
import tempfile
import shutil

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))


def test_startup_checker():
    """Test the startup checker functionality"""
    print("=" * 60)
    print("TESTING STARTUP CHECKER")
    print("=" * 60)
    
    try:
        from startup_checker import StartupChecker
        
        # Create a temporary config for testing
        original_config = "startup_config.json"
        backup_config = None
        
        # Backup existing config if it exists
        if os.path.exists(original_config):
            backup_config = original_config + ".backup"
            shutil.copy(original_config, backup_config)
        
        try:
            # Test with fresh config (first run)
            if os.path.exists(original_config):
                os.remove(original_config)
            
            checker = StartupChecker()
            print("✅ StartupChecker created successfully")
            
            # Test config loading
            config = checker.load_config()
            print(f"✅ Config loaded: first_run={config.get('first_run', 'unknown')}")
            
            # Test dependency checking
            missing_required, missing_optional, missing_external = checker.get_missing_dependencies()
            print(f"✅ Dependency check completed:")
            print(f"   Required missing: {len(missing_required)}")
            print(f"   Optional missing: {len(missing_optional)}")
            print(f"   External missing: {len(missing_external)}")
            
            if missing_optional:
                print(f"   Missing optional packages: {missing_optional}")
            
            return True
            
        finally:
            # Restore backup config
            if backup_config and os.path.exists(backup_config):
                shutil.move(backup_config, original_config)
            elif os.path.exists(original_config):
                os.remove(original_config)
    
    except Exception as e:
        print(f"❌ StartupChecker test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_gui_installer_import():
    """Test that GUI installer can be imported"""
    print("\n" + "=" * 60)
    print("TESTING GUI INSTALLER IMPORT")
    print("=" * 60)
    
    try:
        from dependency_installer_gui import DependencyInstallerDialog, show_dependency_installer
        print("✅ GUI installer imported successfully")
        
        # Test dialog creation (without showing)
        test_packages = {
            'test-package': 'Test package description'
        }
        
        # This would normally show the dialog, but we'll just test creation
        print("✅ GUI installer functions available")
        return True
        
    except ImportError as e:
        print(f"❌ GUI installer import failed: {e}")
        print("   This might be due to missing PyQt6")
        return False
    except Exception as e:
        print(f"❌ GUI installer test failed: {e}")
        return False


def test_launcher_import():
    """Test that launcher can be imported"""
    print("\n" + "=" * 60)
    print("TESTING LAUNCHER IMPORT")
    print("=" * 60)
    
    try:
        import launch
        print("✅ Launcher imported successfully")
        
        # Test that main function exists
        if hasattr(launch, 'main'):
            print("✅ Launcher main function available")
        else:
            print("❌ Launcher main function not found")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Launcher test failed: {e}")
        return False


def test_main_app_integration():
    """Test that main app integrates with startup checker"""
    print("\n" + "=" * 60)
    print("TESTING MAIN APP INTEGRATION")
    print("=" * 60)
    
    try:
        # Test that main.py imports startup checker
        with open('main.py', 'r', encoding='utf-8') as f:
            content = f.read()
            
        if 'startup_checker' in content:
            print("✅ Main app imports startup checker")
        else:
            print("❌ Main app doesn't import startup checker")
            return False
        
        if 'check_startup_dependencies' in content:
            print("✅ Main app calls dependency check")
        else:
            print("❌ Main app doesn't call dependency check")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Main app integration test failed: {e}")
        return False


def test_file_structure():
    """Test that all required files exist"""
    print("\n" + "=" * 60)
    print("TESTING FILE STRUCTURE")
    print("=" * 60)
    
    required_files = [
        'startup_checker.py',
        'dependency_installer_gui.py',
        'launch.py',
        'main.py',
        'run.bat',
        'run.sh'
    ]
    
    missing_files = []
    
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file} - MISSING")
            missing_files.append(file)
    
    return len(missing_files) == 0


def main():
    """Run all startup system tests"""
    print("MFFUHijack Startup System Test")
    print("=" * 60)
    
    tests = [
        ("File Structure", test_file_structure),
        ("Startup Checker", test_startup_checker),
        ("GUI Installer Import", test_gui_installer_import),
        ("Launcher Import", test_launcher_import),
        ("Main App Integration", test_main_app_integration),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"\n✅ {test_name}: PASSED")
            else:
                print(f"\n❌ {test_name}: FAILED")
        except Exception as e:
            print(f"\n❌ {test_name}: ERROR - {e}")
    
    print("\n" + "=" * 60)
    print(f"STARTUP SYSTEM TEST RESULTS: {passed}/{total} passed")
    print("=" * 60)
    
    if passed == total:
        print("🎉 All startup system tests passed!")
        print("\nYou can now use:")
        print("  python launch.py        - Full startup with dependency check")
        print("  python main.py          - Direct start (skip dependency check)")
        print("  run.bat / run.sh        - Platform-specific launchers")
    else:
        print("⚠️  Some startup system tests failed.")
        print("The application may still work, but startup features might be limited.")
    
    print("=" * 60)
    
    return passed == total


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\nTest interrupted by user")
    except Exception as e:
        print(f"\nTest failed with error: {e}")
        import traceback
        traceback.print_exc()
