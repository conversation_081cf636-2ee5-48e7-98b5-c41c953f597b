# MFFUHijack Browser Automation Guide

## 🎯 Overview

MFFUHijack now includes **automatic browser automation** for submitting codes directly to the My Funded Futures website. When a code is detected from the livestream, the system automatically navigates to the appropriate account page and submits the code with your payment information.

## 🚀 Key Features

### ✅ **Automatic Code Submission**
- Detects codes from livestream OCR
- Automatically submits to My Funded Futures website
- Supports all account types: Starter, Starter Plus, Expert, Reset
- Pre-loads checkout pages for fastest submission speed

### ✅ **Smart Account Type Handling**
- **Standard Types** (Starter, Starter Plus, Expert): Navigate through checkout process
- **Reset Type**: Special workflow using stats page and account selection
- **Multi-Account Support**: Handle multiple account types simultaneously

### ✅ **Browser Control**
- Show/hide browser window
- Manual login with CAPTCHA support
- Real-time status updates and logging
- Error handling and retry logic

## 🛠️ Installation

### **Automatic Installation**
The browser automation dependencies are automatically installed when you run MFFUHijack for the first time.

### **Manual Installation**
If needed, install the browser automation dependencies manually:

```bash
pip install selenium>=4.15.0
```

### **Chrome Browser Required**
- **Chrome** must be installed on your system
- The system uses Chrome WebDriver for automation
- Chrome will be automatically detected and used

## 🎮 How to Use

### **1. Setup Payment Information**
In the main GUI, fill out the **Payment Information** section:

```
💳 Payment Information
┌─────────────────────────────────────────────────────┐
│ Username: [your_mffu_username]                      │
│ Password: [••••••••••••••••••••] [👁️]              │
│ Card CVV: [123]                                     │
│ Account to Reset (Acc#): [ACC12345] (for resets)   │
└─────────────────────────────────────────────────────┘
```

### **2. Login to My Funded Futures**
1. Click **"🔐 Login to MFFU"** button
2. Browser opens to login page with credentials filled
3. **Complete CAPTCHA manually** (if present)
4. **Click "Sign In" manually**
5. System detects successful login

### **3. Preload Account Pages**
1. Click **"🚀 Preload Pages"** button
2. System navigates to all enabled account types
3. Pages are preloaded to final checkout stage
4. Ready for instant code submission

### **4. Start Livestream Bot**
1. Configure your livestream URL and OCR settings
2. Click **"▶️ Start Bot"**
3. When codes are detected, they're **automatically submitted**
4. Real-time logging shows submission status

### **5. Browser Visibility**
- Click **"👁️ Show Browser"** to make browser visible
- Click **"🙈 Hide Browser"** to hide browser window
- Useful for monitoring submission process

## 🔧 Account Type Workflows

### **Standard Account Types** (Starter, Starter Plus, Expert)

1. **Navigate** to account-specific URL:
   - Starter Plus: `https://myfundedfutures.com/challenge?id=39&platform=Tradovate`
   - Starter: `https://myfundedfutures.com/challenge?id=40&platform=Tradovate`
   - Expert: `https://myfundedfutures.com/challenge?id=43&platform=Tradovate`

2. **Checkout Process**:
   - Click "Next" button
   - Accept all terms (check all checkboxes)
   - Click "Next" again
   - Reach final checkout page

3. **Code Submission**:
   - Click "Add coupon code"
   - Enter detected code
   - Click "APPLY"
   - Enter CVV
   - Click "Submit"

### **Reset Account Type**

1. **Navigate** to stats page: `https://myfundedfutures.com/stats`
2. **Account Selection**:
   - Click account dropdown (span element)
   - Select specific account number
3. **Reset Process**:
   - Click "Reset Now" button
   - Click "Add coupon code"
   - Enter detected code
   - Click "APPLY"
   - Enter CVV
   - Click "Submit Payment"

## 📊 Status Monitoring

### **Browser Status Indicators**
- **"Not initialized"**: Browser not started
- **"Initializing..."**: Browser starting up
- **"Login in progress..."**: Credentials entered, waiting for manual completion
- **"Ready for manual login"**: Complete CAPTCHA and click Sign In
- **"Pages preloaded"**: All account pages ready for submission
- **"Login failed"** / **"Preload failed"**: Error occurred

### **Real-Time Logging**
All browser actions are logged in real-time:
```
🌐 Initializing browser automation...
✅ Browser initialized successfully
🔐 Navigating to login page...
📝 Filling login credentials...
✅ Username entered: your_username
✅ Password entered
🤖 CAPTCHA detected - Please complete manually and click Sign In
🚀 Preloading Starter Plus page...
✅ Starter Plus page preloaded to checkout
🎯 DETECTED: ABC123 | Type: Starter Plus | Confidence: 0.95
🚀 Attempting automatic submission of code: ABC123
✅ Code ABC123 submitted successfully!
```

## ⚡ Speed Optimization

### **Pre-loading Strategy**
- All enabled account pages are pre-loaded to the final checkout stage
- When a code is detected, submission happens **instantly**
- No need to navigate through checkout process during submission
- Maximizes chances of being first to submit

### **Multi-Tab Management**
- Each account type opens in a separate browser tab
- System switches between tabs for submissions
- Parallel processing for multiple account types

## 🛡️ Error Handling

### **Common Errors and Solutions**

**❌ "Selenium not installed"**
- Solution: `pip install selenium`

**❌ "Chrome not found"**
- Solution: Install Google Chrome browser

**❌ "Login timeout"**
- Solution: Complete CAPTCHA and click Sign In within 60 seconds

**❌ "Coupon code error"**
- Solution: Code may be invalid or already used
- System logs the specific error message

**❌ "CVV not provided"**
- Solution: Fill in Card CVV field in Payment section

**❌ "Reset account number not provided"**
- Solution: Fill in Account to Reset field for reset codes

## 🔒 Security Notes

### **Credential Handling**
- Credentials are only stored in GUI fields during session
- No persistent storage of sensitive information
- Password field is masked by default

### **Browser Security**
- Uses standard Chrome browser with normal security settings
- No modification of browser security features
- CAPTCHA must be completed manually for security

## 🧪 Testing

### **Test Browser Automation**
Run the test script to verify functionality:

```bash
python test_browser_automation.py
```

### **Test Components**
- ✅ Browser initialization
- ✅ Login page navigation
- ✅ Element detection
- ✅ Visibility controls
- ✅ URL mapping
- ✅ Code submission logic

## 📞 Troubleshooting

### **Browser Won't Start**
1. Ensure Chrome is installed
2. Check internet connection
3. Try running as administrator
4. Restart MFFUHijack

### **Login Issues**
1. Verify username/password are correct
2. Complete CAPTCHA if present
3. Check for website changes
4. Try manual login first

### **Submission Failures**
1. Verify CVV is entered
2. Check account type configuration
3. Ensure pages are preloaded
4. Monitor browser window for errors

### **Performance Issues**
1. Close unnecessary browser tabs
2. Restart browser automation
3. Check system resources
4. Use hidden browser mode

## 🎉 Success Tips

1. **Always preload pages** before starting the bot
2. **Keep browser visible** initially to monitor process
3. **Test with mock codes** first
4. **Monitor real-time logs** for status updates
5. **Have backup manual submission** ready

The browser automation system is designed to give you the **fastest possible code submission** while maintaining reliability and security!
