"""
Smart Features for Enhanced Live Scan Monitor
Includes Time-based Analysis, Smart Retry System, and Code History
"""

import json
import os
import re
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Set
from collections import defaultdict, Counter
import sqlite3
from PyQt6.QtWidgets import *
from PyQt6.QtCore import *
from PyQt6.QtGui import *


class TimeBasedAnalyzer:
    """Analyzes code detection patterns over time"""
    
    def __init__(self):
        self.detection_timeline = []
        self.hourly_stats = defaultdict(int)
        self.daily_stats = defaultdict(int)
        self.detection_intervals = []
        self.peak_hours = []
        self.quiet_periods = []
        self.analysis_window = timedelta(hours=24)  # Default 24-hour analysis window
    
    def add_detection(self, timestamp: datetime, code_data: Dict):
        """Add a code detection to the timeline"""
        detection_event = {
            'timestamp': timestamp,
            'code': code_data.get('code', ''),
            'type': code_data.get('type', ''),
            'confidence': code_data.get('confidence', 0),
            'valid': code_data.get('valid', False)
        }
        
        self.detection_timeline.append(detection_event)
        
        # Update hourly and daily stats
        hour_key = timestamp.strftime('%H')
        day_key = timestamp.strftime('%Y-%m-%d')
        
        self.hourly_stats[hour_key] += 1
        self.daily_stats[day_key] += 1
        
        # Calculate intervals between detections
        if len(self.detection_timeline) > 1:
            prev_timestamp = self.detection_timeline[-2]['timestamp']
            interval = (timestamp - prev_timestamp).total_seconds()
            self.detection_intervals.append(interval)
        
        # Keep only recent data within analysis window
        self.cleanup_old_data()
        
        # Update analysis
        self.update_analysis()
    
    def cleanup_old_data(self):
        """Remove data older than analysis window"""
        cutoff_time = datetime.now() - self.analysis_window
        
        # Filter timeline
        self.detection_timeline = [
            event for event in self.detection_timeline 
            if event['timestamp'] > cutoff_time
        ]
        
        # Rebuild stats from filtered data
        self.hourly_stats.clear()
        self.daily_stats.clear()
        
        for event in self.detection_timeline:
            hour_key = event['timestamp'].strftime('%H')
            day_key = event['timestamp'].strftime('%Y-%m-%d')
            self.hourly_stats[hour_key] += 1
            self.daily_stats[day_key] += 1
    
    def update_analysis(self):
        """Update peak hours and quiet periods analysis"""
        if not self.hourly_stats:
            return
        
        # Find peak hours (top 25% of hours by detection count)
        sorted_hours = sorted(self.hourly_stats.items(), key=lambda x: x[1], reverse=True)
        peak_count = max(1, len(sorted_hours) // 4)
        self.peak_hours = [hour for hour, count in sorted_hours[:peak_count]]
        
        # Find quiet periods (bottom 25% of hours)
        quiet_count = max(1, len(sorted_hours) // 4)
        self.quiet_periods = [hour for hour, count in sorted_hours[-quiet_count:]]
    
    def get_detection_frequency(self) -> float:
        """Get average detection frequency (detections per hour)"""
        if not self.detection_timeline:
            return 0.0
        
        if len(self.detection_timeline) < 2:
            return 0.0
        
        time_span = (self.detection_timeline[-1]['timestamp'] - 
                    self.detection_timeline[0]['timestamp']).total_seconds() / 3600
        
        if time_span == 0:
            return 0.0
        
        return len(self.detection_timeline) / time_span
    
    def get_peak_detection_times(self) -> List[str]:
        """Get hours with highest detection activity"""
        return self.peak_hours
    
    def get_quiet_periods(self) -> List[str]:
        """Get hours with lowest detection activity"""
        return self.quiet_periods
    
    def get_average_interval(self) -> float:
        """Get average time between detections in seconds"""
        if not self.detection_intervals:
            return 0.0
        return sum(self.detection_intervals) / len(self.detection_intervals)
    
    def get_hourly_distribution(self) -> Dict[str, int]:
        """Get detection count by hour of day"""
        return dict(self.hourly_stats)
    
    def get_daily_distribution(self) -> Dict[str, int]:
        """Get detection count by day"""
        return dict(self.daily_stats)
    
    def get_detection_patterns(self) -> Dict:
        """Get comprehensive detection pattern analysis"""
        return {
            'total_detections': len(self.detection_timeline),
            'detection_frequency': self.get_detection_frequency(),
            'average_interval': self.get_average_interval(),
            'peak_hours': self.peak_hours,
            'quiet_periods': self.quiet_periods,
            'hourly_distribution': self.get_hourly_distribution(),
            'daily_distribution': self.get_daily_distribution(),
            'analysis_window_hours': self.analysis_window.total_seconds() / 3600
        }


class CharacterConfusionMatrix:
    """Handles OCR character confusion and smart substitutions"""
    
    def __init__(self):
        # Common OCR character confusions
        self.confusion_map = {
            # Number/Letter confusions
            '0': ['O', 'Q', 'D'],
            'O': ['0', 'Q', 'D'],
            'Q': ['O', '0', 'G'],
            '1': ['I', 'l', '|', 'L'],
            'I': ['1', 'l', '|', 'L'],
            'l': ['1', 'I', '|', 'L'],
            'L': ['1', 'I', 'l', '|'],
            '5': ['S', 's'],
            'S': ['5', 's'],
            's': ['5', 'S'],
            '6': ['G', 'b'],
            'G': ['6', 'C', 'Q'],
            '8': ['B', '3'],
            'B': ['8', '3', 'R'],
            '2': ['Z', 'z'],
            'Z': ['2', 'z'],
            'z': ['2', 'Z'],
            '9': ['g', 'q'],
            'g': ['9', 'q'],
            'q': ['9', 'g'],
            # Additional common confusions
            'C': ['G', 'O', '0'],
            'E': ['F', '3'],
            'F': ['E', 'P'],
            'P': ['F', 'R'],
            'R': ['P', 'B'],
            'T': ['7', '1'],
            '7': ['T', '1'],
            'U': ['V', 'Y'],
            'V': ['U', 'Y'],
            'Y': ['V', 'U'],
            'W': ['VV', 'M'],
            'M': ['W', 'N'],
            'N': ['M', 'H'],
            'H': ['N', 'K'],
            'K': ['H', 'R'],
        }
        
        # Character similarity scores (0-1, higher = more similar)
        self.similarity_scores = {
            ('0', 'O'): 0.9,
            ('1', 'I'): 0.9,
            ('5', 'S'): 0.8,
            ('6', 'G'): 0.7,
            ('8', 'B'): 0.8,
            ('2', 'Z'): 0.7,
            ('9', 'g'): 0.6,
            ('C', 'G'): 0.7,
            ('E', 'F'): 0.6,
            ('P', 'R'): 0.6,
            ('T', '7'): 0.7,
            ('U', 'V'): 0.6,
            ('W', 'M'): 0.5,
            ('N', 'H'): 0.5,
        }
    
    def get_possible_substitutions(self, character: str) -> List[str]:
        """Get possible character substitutions for OCR errors"""
        return self.confusion_map.get(character.upper(), [])
    
    def get_similarity_score(self, char1: str, char2: str) -> float:
        """Get similarity score between two characters"""
        key = tuple(sorted([char1.upper(), char2.upper()]))
        return self.similarity_scores.get(key, 0.0)

    def analyze_s5_context(self, text: str, position: int) -> str:
        """
        Analyze context around S/5 character to determine most likely character
        Returns 'S' or '5' based on context analysis
        """
        if position < 0 or position >= len(text):
            return text[position] if position < len(text) else ''

        char = text[position].upper()
        if char not in ['S', '5']:
            return char

        # Get surrounding context
        before = text[max(0, position-2):position].upper()
        after = text[position+1:min(len(text), position+3)].upper()

        # Context clues for S
        s_indicators = [
            # Word patterns
            before.endswith('RE') and after.startswith('ET'),  # RESET
            before.endswith('PLU') and not after,             # PLUS
            before == '' and after.startswith('TART'),        # START
            before == '' and after.startswith('AVE'),         # SAVE
            before == '' and after.startswith('PECIAL'),      # SPECIAL
            before.endswith('EXPERT') and not after,          # EXPERTS
            before.endswith('CODE') and not after,            # CODES
            # Letter context
            before and before[-1].isalpha() and after and after[0].isalpha(),
        ]

        # Context clues for 5
        five_indicators = [
            # Numeric patterns
            before and before[-1].isdigit(),                  # Number before
            after and after[0].isdigit(),                     # Number after
            before and before[-1].isdigit() and after and after[0].isdigit(),  # Between numbers
            # End of numeric sequence
            before and all(c.isdigit() for c in before) and not after,
        ]

        s_score = sum(s_indicators)
        five_score = sum(five_indicators)

        if s_score > five_score:
            return 'S'
        elif five_score > s_score:
            return '5'
        else:
            # Default to original character if no clear context
            return char
    
    def generate_code_variants(self, code: str, max_substitutions: int = 2) -> List[Tuple[str, float]]:
        """Generate possible code variants with confidence scores"""
        variants = []
        code_upper = code.upper()
        
        # Original code has highest confidence
        variants.append((code_upper, 1.0))
        
        # Single character substitutions
        for i, char in enumerate(code_upper):
            substitutions = self.get_possible_substitutions(char)
            for sub_char in substitutions:
                variant = code_upper[:i] + sub_char + code_upper[i+1:]
                confidence = self.get_similarity_score(char, sub_char)
                variants.append((variant, confidence))
        
        # Double character substitutions (if enabled)
        if max_substitutions >= 2 and len(code_upper) >= 2:
            for i in range(len(code_upper)):
                for j in range(i+1, len(code_upper)):
                    char1, char2 = code_upper[i], code_upper[j]
                    subs1 = self.get_possible_substitutions(char1)
                    subs2 = self.get_possible_substitutions(char2)
                    
                    for sub1 in subs1:
                        for sub2 in subs2:
                            variant = list(code_upper)
                            variant[i] = sub1
                            variant[j] = sub2
                            variant_str = ''.join(variant)
                            
                            # Combined confidence score
                            conf1 = self.get_similarity_score(char1, sub1)
                            conf2 = self.get_similarity_score(char2, sub2)
                            confidence = (conf1 + conf2) / 2
                            
                            variants.append((variant_str, confidence))
        
        # Remove duplicates and sort by confidence
        unique_variants = {}
        for variant, confidence in variants:
            if variant not in unique_variants or confidence > unique_variants[variant]:
                unique_variants[variant] = confidence
        
        return sorted(unique_variants.items(), key=lambda x: x[1], reverse=True)


class CodeHistoryManager:
    """Manages code history and duplicate detection"""
    
    def __init__(self, db_path: str = "code_history.db"):
        self.db_path = db_path
        self.init_database()
        self.session_codes = set()  # Codes seen in current session
        self.recent_codes = []  # Recent codes with timestamps
        self.code_patterns = defaultdict(int)  # Pattern frequency tracking
    
    def init_database(self):
        """Initialize SQLite database for persistent code storage"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Create codes table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS codes (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    code TEXT NOT NULL,
                    code_type TEXT,
                    confidence REAL,
                    timestamp TEXT,
                    session_id TEXT,
                    valid BOOLEAN,
                    full_text TEXT,
                    source TEXT DEFAULT 'detection'
                )
            ''')
            
            # Create patterns table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS patterns (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    pattern TEXT UNIQUE,
                    frequency INTEGER DEFAULT 1,
                    last_seen TEXT
                )
            ''')
            
            # Create indexes for performance
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_code ON codes(code)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_timestamp ON codes(timestamp)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_session ON codes(session_id)')
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"Error initializing code history database: {e}")
    
    def add_code(self, code_data: Dict, session_id: str = None) -> bool:
        """Add code to history, returns False if duplicate"""
        code = code_data.get('code', '').upper()
        
        # Check for session duplicates
        if code in self.session_codes:
            return False
        
        # Add to session codes
        self.session_codes.add(code)
        
        # Add to recent codes
        timestamp = datetime.now()
        self.recent_codes.append({
            'code': code,
            'timestamp': timestamp,
            'type': code_data.get('type', ''),
            'confidence': code_data.get('confidence', 0),
            'valid': code_data.get('valid', False)
        })
        
        # Keep only last 100 recent codes
        if len(self.recent_codes) > 100:
            self.recent_codes = self.recent_codes[-100:]
        
        # Update pattern tracking
        pattern = self.extract_pattern(code)
        self.code_patterns[pattern] += 1
        
        # Store in database
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO codes (code, code_type, confidence, timestamp, session_id, valid, full_text)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                code,
                code_data.get('type', ''),
                code_data.get('confidence', 0),
                timestamp.isoformat(),
                session_id,
                code_data.get('valid', False),
                code_data.get('full_text', '')
            ))
            
            # Update pattern frequency
            cursor.execute('''
                INSERT OR REPLACE INTO patterns (pattern, frequency, last_seen)
                VALUES (?, ?, ?)
            ''', (pattern, self.code_patterns[pattern], timestamp.isoformat()))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"Error storing code in database: {e}")
        
        return True
    
    def is_duplicate(self, code: str) -> bool:
        """Check if code is a duplicate in current session"""
        return code.upper() in self.session_codes
    
    def is_recent_duplicate(self, code: str, time_window: timedelta = timedelta(minutes=5)) -> bool:
        """Check if code was seen recently within time window"""
        code_upper = code.upper()
        cutoff_time = datetime.now() - time_window
        
        for recent_code in self.recent_codes:
            if (recent_code['code'] == code_upper and 
                recent_code['timestamp'] > cutoff_time):
                return True
        
        return False
    
    def extract_pattern(self, code: str) -> str:
        """Extract pattern from code (e.g., ABC123 -> LLL### )"""
        pattern = ""
        for char in code.upper():
            if char.isalpha():
                pattern += "L"  # Letter
            elif char.isdigit():
                pattern += "#"  # Number
            else:
                pattern += char  # Special character
        return pattern
    
    def get_similar_codes(self, code: str, limit: int = 10) -> List[Dict]:
        """Get similar codes from history"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Get codes with similar patterns
            pattern = self.extract_pattern(code)
            cursor.execute('''
                SELECT code, code_type, confidence, timestamp, valid
                FROM codes 
                WHERE code LIKE ? OR code LIKE ?
                ORDER BY timestamp DESC
                LIMIT ?
            ''', (f"%{code[:3]}%", f"%{code[-3:]}%", limit))
            
            results = []
            for row in cursor.fetchall():
                results.append({
                    'code': row[0],
                    'type': row[1],
                    'confidence': row[2],
                    'timestamp': row[3],
                    'valid': row[4]
                })
            
            conn.close()
            return results
            
        except Exception as e:
            print(f"Error querying similar codes: {e}")
            return []
    
    def get_code_statistics(self) -> Dict:
        """Get comprehensive code statistics"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Total codes
            cursor.execute('SELECT COUNT(*) FROM codes')
            total_codes = cursor.fetchone()[0]
            
            # Valid codes
            cursor.execute('SELECT COUNT(*) FROM codes WHERE valid = 1')
            valid_codes = cursor.fetchone()[0]
            
            # Codes by type
            cursor.execute('SELECT code_type, COUNT(*) FROM codes GROUP BY code_type')
            codes_by_type = dict(cursor.fetchall())
            
            # Most common patterns
            cursor.execute('SELECT pattern, frequency FROM patterns ORDER BY frequency DESC LIMIT 10')
            common_patterns = cursor.fetchall()
            
            conn.close()
            
            return {
                'total_codes': total_codes,
                'valid_codes': valid_codes,
                'invalid_codes': total_codes - valid_codes,
                'success_rate': (valid_codes / total_codes * 100) if total_codes > 0 else 0,
                'codes_by_type': codes_by_type,
                'common_patterns': common_patterns,
                'session_codes': len(self.session_codes),
                'recent_codes': len(self.recent_codes)
            }
            
        except Exception as e:
            print(f"Error getting code statistics: {e}")
            return {}
    
    def clear_session_codes(self):
        """Clear session-specific code tracking"""
        self.session_codes.clear()
        self.recent_codes.clear()
    
    def search_codes(self, query: str, limit: int = 50) -> List[Dict]:
        """Search codes by query"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT code, code_type, confidence, timestamp, valid, full_text
                FROM codes 
                WHERE code LIKE ? OR full_text LIKE ?
                ORDER BY timestamp DESC
                LIMIT ?
            ''', (f"%{query}%", f"%{query}%", limit))
            
            results = []
            for row in cursor.fetchall():
                results.append({
                    'code': row[0],
                    'type': row[1],
                    'confidence': row[2],
                    'timestamp': row[3],
                    'valid': row[4],
                    'full_text': row[5]
                })
            
            conn.close()
            return results
            
        except Exception as e:
            print(f"Error searching codes: {e}")
            return []


class SmartRetrySystem:
    """Intelligent code retry system with character confusion handling"""

    def __init__(self, code_history: CodeHistoryManager, confusion_matrix: CharacterConfusionMatrix):
        self.code_history = code_history
        self.confusion_matrix = confusion_matrix
        self.retry_attempts = {}  # Track retry attempts per code
        self.max_retries = 3
        self.retry_delay = 1.0  # seconds between retries
        self.validation_patterns = {
            'Starter': [
                r'^[A-Z0-9]{5,8}$',  # 5-8 alphanumeric characters
                r'^START[A-Z0-9]{2,4}$',  # START prefix
                r'^[A-Z]{3}[0-9]{2,4}$'  # 3 letters + 2-4 numbers
            ],
            'Expert': [
                r'^[A-Z0-9]{6,10}$',  # 6-10 alphanumeric characters
                r'^EXPERT[A-Z0-9]{1,4}$',  # EXPERT prefix
                r'^[A-Z]{4,6}[0-9]{2,4}$'  # 4-6 letters + 2-4 numbers
            ],
            'Reset': [
                r'^RESET[A-Z0-9]{1,4}$',  # RESET prefix
                r'^[A-Z0-9]{5,8}$',  # 5-8 alphanumeric characters
                r'^[A-Z]{3,5}[0-9]{1,3}$'  # 3-5 letters + 1-3 numbers
            ],
            'Starter Plus': [
                r'^PLUS[A-Z0-9]{2,4}$',  # PLUS prefix
                r'^[A-Z0-9]{6,9}$',  # 6-9 alphanumeric characters
                r'^[A-Z]{3,4}[0-9]{2,4}$'  # 3-4 letters + 2-4 numbers
            ],
            'Free Reset Code': [
                r'^FREE[A-Z0-9]{2,5}$',  # FREE prefix
                r'^[A-Z0-9]{5,10}$',  # 5-10 alphanumeric characters
                r'^[A-Z]{4,6}[0-9]{1,4}$'  # 4-6 letters + 1-4 numbers
            ]
        }

        # Known valid code patterns from successful submissions
        self.known_patterns = set()
        self.load_known_patterns()

    def load_known_patterns(self):
        """Load known valid patterns from code history"""
        try:
            stats = self.code_history.get_code_statistics()
            common_patterns = stats.get('common_patterns', [])

            for pattern, frequency in common_patterns:
                if frequency >= 2:  # Pattern seen at least twice
                    self.known_patterns.add(pattern)

        except Exception as e:
            print(f"Error loading known patterns: {e}")

    def validate_code_format(self, code: str, code_type: str) -> bool:
        """Validate code format against known patterns"""
        if code_type not in self.validation_patterns:
            return True  # Unknown type, assume valid

        patterns = self.validation_patterns[code_type]
        for pattern in patterns:
            if re.match(pattern, code.upper()):
                return True

        return False

    def calculate_code_confidence(self, code: str, code_type: str, ocr_confidence: float) -> float:
        """Calculate overall confidence score for a code"""
        confidence_factors = []

        # OCR confidence (weight: 0.4)
        confidence_factors.append(('ocr', ocr_confidence, 0.4))

        # Format validation (weight: 0.3)
        format_valid = self.validate_code_format(code, code_type)
        format_score = 1.0 if format_valid else 0.3
        confidence_factors.append(('format', format_score, 0.3))

        # Pattern recognition (weight: 0.2)
        pattern = self.code_history.extract_pattern(code)
        pattern_score = 1.0 if pattern in self.known_patterns else 0.5
        confidence_factors.append(('pattern', pattern_score, 0.2))

        # Duplicate check (weight: 0.1)
        is_duplicate = self.code_history.is_duplicate(code)
        duplicate_score = 0.0 if is_duplicate else 1.0
        confidence_factors.append(('duplicate', duplicate_score, 0.1))

        # Calculate weighted average
        total_score = sum(score * weight for _, score, weight in confidence_factors)

        return min(1.0, max(0.0, total_score))

    def should_retry_code(self, code: str, confidence: float) -> bool:
        """Determine if a code should be retried"""
        # Don't retry if confidence is already high
        if confidence >= 0.8:
            return False

        # Don't retry if we've already tried too many times
        retry_count = self.retry_attempts.get(code, 0)
        if retry_count >= self.max_retries:
            return False

        # Don't retry obvious duplicates
        if self.code_history.is_duplicate(code):
            return False

        # Retry if confidence is low or format is invalid
        return confidence < 0.6

    def generate_retry_variants(self, code: str, code_type: str) -> List[Tuple[str, float]]:
        """Generate code variants for retry attempts"""
        variants = self.confusion_matrix.generate_code_variants(code, max_substitutions=2)

        # Filter and score variants
        valid_variants = []
        for variant_code, substitution_confidence in variants:
            # Skip original code
            if variant_code == code.upper():
                continue

            # Skip if already seen
            if self.code_history.is_duplicate(variant_code):
                continue

            # Calculate overall confidence for variant
            overall_confidence = self.calculate_code_confidence(
                variant_code, code_type, substitution_confidence
            )

            # Only include variants with reasonable confidence
            if overall_confidence >= 0.4:
                valid_variants.append((variant_code, overall_confidence))

        # Sort by confidence and return top candidates
        valid_variants.sort(key=lambda x: x[1], reverse=True)
        return valid_variants[:5]  # Top 5 candidates

    def process_code_detection(self, code_data: Dict) -> Dict:
        """Process code detection with smart retry logic"""
        original_code = code_data.get('code', '').upper()
        code_type = code_data.get('type', '')
        ocr_confidence = code_data.get('confidence', 0)

        # Calculate overall confidence
        overall_confidence = self.calculate_code_confidence(
            original_code, code_type, ocr_confidence
        )

        # Create enhanced code data
        enhanced_data = code_data.copy()
        enhanced_data.update({
            'original_code': original_code,
            'overall_confidence': overall_confidence,
            'retry_candidates': [],
            'validation_passed': self.validate_code_format(original_code, code_type),
            'is_duplicate': self.code_history.is_duplicate(original_code),
            'should_retry': False
        })

        # Check if we should retry
        if self.should_retry_code(original_code, overall_confidence):
            enhanced_data['should_retry'] = True

            # Generate retry variants
            retry_variants = self.generate_retry_variants(original_code, code_type)
            enhanced_data['retry_candidates'] = retry_variants

            # Track retry attempt
            self.retry_attempts[original_code] = self.retry_attempts.get(original_code, 0) + 1

            # If we have good retry candidates, suggest the best one
            if retry_variants:
                best_variant, best_confidence = retry_variants[0]
                if best_confidence > overall_confidence:
                    enhanced_data.update({
                        'suggested_code': best_variant,
                        'suggested_confidence': best_confidence,
                        'retry_reason': f"Character confusion correction: {original_code} → {best_variant}"
                    })

        return enhanced_data

    def get_retry_statistics(self) -> Dict:
        """Get retry system statistics"""
        total_attempts = sum(self.retry_attempts.values())
        codes_retried = len(self.retry_attempts)

        return {
            'total_retry_attempts': total_attempts,
            'codes_retried': codes_retried,
            'average_retries_per_code': total_attempts / codes_retried if codes_retried > 0 else 0,
            'known_patterns': len(self.known_patterns),
            'max_retries': self.max_retries
        }

    def reset_retry_tracking(self):
        """Reset retry tracking for new session"""
        self.retry_attempts.clear()
        self.load_known_patterns()  # Reload patterns from database


class SmartFeaturesManager:
    """Main manager for all smart features"""

    def __init__(self):
        self.time_analyzer = TimeBasedAnalyzer()
        self.confusion_matrix = CharacterConfusionMatrix()
        self.code_history = CodeHistoryManager()
        self.retry_system = SmartRetrySystem(self.code_history, self.confusion_matrix)
        self.current_session_id = None

    def start_session(self, session_id: str):
        """Start a new smart features session"""
        self.current_session_id = session_id
        self.code_history.clear_session_codes()
        self.retry_system.reset_retry_tracking()

    def process_code_detection(self, code_data: Dict) -> Dict:
        """Process code detection through all smart features"""
        timestamp = datetime.now()

        # Process through smart retry system
        enhanced_data = self.retry_system.process_code_detection(code_data)

        # Add to time-based analysis
        self.time_analyzer.add_detection(timestamp, enhanced_data)

        # Add to code history (if not duplicate)
        if not enhanced_data.get('is_duplicate', False):
            self.code_history.add_code(enhanced_data, self.current_session_id)

        # Add timestamp and smart features info
        enhanced_data.update({
            'detection_timestamp': timestamp,
            'session_id': self.current_session_id,
            'smart_features_processed': True
        })

        return enhanced_data

    def get_comprehensive_analytics(self) -> Dict:
        """Get comprehensive analytics from all smart features"""
        return {
            'time_analysis': self.time_analyzer.get_detection_patterns(),
            'code_history': self.code_history.get_code_statistics(),
            'retry_system': self.retry_system.get_retry_statistics(),
            'session_id': self.current_session_id
        }
