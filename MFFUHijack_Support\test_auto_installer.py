#!/usr/bin/env python3
"""
Test script for the automatic installer
"""

import sys
import os

# Add current directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))


def test_installation_status():
    """Test checking installation status"""
    print("🧪 Testing Installation Status Check")
    print("=" * 50)
    
    try:
        from auto_installer import check_installation_status
        
        status = check_installation_status()
        
        print("📊 Current Installation Status:")
        print("-" * 30)
        
        required_packages = ['PyQt6', 'opencv-python', 'numpy', 'requests', 'Pillow', 'yt-dlp']
        optional_packages = ['easyocr', 'torch', 'torchvision', 'tqdm']
        external_tools = ['ffmpeg']
        
        print("\n🔧 Required Packages:")
        for package in required_packages:
            if package in status:
                icon = "✅" if status[package] else "❌"
                print(f"  {icon} {package}")
        
        print("\n🎯 Optional Packages:")
        for package in optional_packages:
            if package in status:
                icon = "✅" if status[package] else "❌"
                print(f"  {icon} {package}")
        
        print("\n🛠️  External Tools:")
        for tool in external_tools:
            if tool in status:
                icon = "✅" if status[tool] else "❌"
                print(f"  {icon} {tool}")
        
        # Count missing packages
        missing_required = [pkg for pkg in required_packages if not status.get(pkg, False)]
        missing_optional = [pkg for pkg in optional_packages if not status.get(pkg, False)]
        missing_external = [tool for tool in external_tools if not status.get(tool, False)]
        
        print(f"\n📈 Summary:")
        print(f"  Required packages missing: {len(missing_required)}")
        print(f"  Optional packages missing: {len(missing_optional)}")
        print(f"  External tools missing: {len(missing_external)}")
        
        if missing_required:
            print(f"\n⚠️  Missing required packages: {', '.join(missing_required)}")
        
        if missing_optional:
            print(f"\n💡 Missing optional packages: {', '.join(missing_optional)}")
        
        if missing_external:
            print(f"\n🔧 Missing external tools: {', '.join(missing_external)}")
        
        return len(missing_required) == 0
        
    except Exception as e:
        print(f"❌ Error checking installation status: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_ffmpeg_installer():
    """Test FFmpeg installer"""
    print("\n🧪 Testing FFmpeg Installer")
    print("=" * 50)
    
    try:
        from ffmpeg_installer import FFmpegInstaller
        
        installer = FFmpegInstaller()
        
        print(f"Platform: {installer.system}")
        print(f"Architecture: {installer.architecture}")
        
        # Check if FFmpeg is available
        is_available = installer.is_ffmpeg_available()
        print(f"FFmpeg available: {'✅ Yes' if is_available else '❌ No'}")
        
        if not is_available:
            print("\n📋 Installation instructions:")
            print(installer.get_installation_instructions())
        
        return is_available
        
    except Exception as e:
        print(f"❌ Error testing FFmpeg installer: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_startup_check():
    """Test the startup dependency check"""
    print("\n🧪 Testing Startup Dependency Check")
    print("=" * 50)
    
    try:
        from startup_checker import check_startup_dependencies
        
        print("Running startup check (no auto-install)...")
        result = check_startup_dependencies(auto_install=False, use_gui=False)
        
        print(f"Startup check result: {'✅ Success' if result else '❌ Failed'}")
        
        return result
        
    except Exception as e:
        print(f"❌ Error testing startup check: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run all tests"""
    print("🚀 MFFUHijack Auto-Installer Test Suite")
    print("=" * 60)
    
    tests = [
        ("Installation Status Check", test_installation_status),
        ("FFmpeg Installer", test_ffmpeg_installer),
        ("Startup Dependency Check", test_startup_check),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        print(f"Running: {test_name}")
        print(f"{'='*60}")
        
        try:
            result = test_func()
            results.append((test_name, result))
            print(f"\n{test_name}: {'✅ PASSED' if result else '⚠️  ISSUES FOUND'}")
        except Exception as e:
            print(f"\n{test_name}: ❌ FAILED - {e}")
            results.append((test_name, False))
    
    # Summary
    print(f"\n{'='*60}")
    print("TEST SUMMARY")
    print(f"{'='*60}")
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All tests passed! MFFUHijack should work correctly.")
    else:
        print("⚠️  Some tests failed. You may need to install missing dependencies.")
        print("\nTo install missing dependencies automatically, run:")
        print("  python auto_installer.py")
        print("  or")
        print("  python main.py  # (will auto-install on first run)")


if __name__ == "__main__":
    main()
