#!/usr/bin/env python3
"""
Enhanced yt-dlp Installer for MFFUHijack
Handles automatic installation of yt-dlp with multiple fallback methods
"""

import sys
import subprocess
import importlib
import os
import platform
import requests
import json
from pathlib import Path
from typing import Optional, Tuple, Dict
import tempfile
import shutil


class YtDlpInstaller:
    """Comprehensive yt-dlp installer with multiple installation methods"""
    
    def __init__(self):
        self.system = platform.system().lower()
        self.architecture = platform.machine().lower()
        self.project_root = Path(__file__).parent
        self.bin_dir = self.project_root / "bin"
        self.bin_dir.mkdir(exist_ok=True)
        
    def check_yt_dlp_module(self) -> bool:
        """Check if yt-dlp is available as a Python module"""
        try:
            import yt_dlp
            return True
        except ImportError:
            return False
    
    def check_yt_dlp_command(self) -> bool:
        """Check if yt-dlp command works (either module or executable)"""
        try:
            # Try python -m yt_dlp first (preferred method)
            result = subprocess.run(
                ['python', '-m', 'yt_dlp', '--version'],
                capture_output=True, text=True, timeout=10
            )
            if result.returncode == 0:
                return True
        except:
            pass
        
        try:
            # Try direct yt-dlp command
            result = subprocess.run(
                ['yt-dlp', '--version'],
                capture_output=True, text=True, timeout=10
            )
            if result.returncode == 0:
                return True
        except:
            pass
        
        try:
            # Try local executable
            local_exe = self.bin_dir / "yt-dlp.exe"
            if local_exe.exists():
                result = subprocess.run(
                    [str(local_exe), '--version'],
                    capture_output=True, text=True, timeout=10
                )
                if result.returncode == 0:
                    return True
        except:
            pass
        
        return False
    
    def install_via_pip(self) -> bool:
        """Install yt-dlp using pip"""
        try:
            print("📦 Installing yt-dlp via pip...")
            
            # Try to install yt-dlp
            result = subprocess.run([
                sys.executable, "-m", "pip", "install", "yt-dlp", "--upgrade"
            ], capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                print("✅ yt-dlp installed successfully via pip")
                return True
            else:
                print(f"❌ pip installation failed: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            print("❌ pip installation timed out")
            return False
        except Exception as e:
            print(f"❌ pip installation error: {e}")
            return False
    
    def get_latest_release_info(self) -> Optional[Dict]:
        """Get latest yt-dlp release information from GitHub"""
        try:
            print("🔍 Checking latest yt-dlp release...")
            
            response = requests.get(
                "https://api.github.com/repos/yt-dlp/yt-dlp/releases/latest",
                timeout=10
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                print(f"❌ Failed to get release info: HTTP {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ Error getting release info: {e}")
            return None
    
    def download_executable(self) -> bool:
        """Download yt-dlp executable as fallback"""
        try:
            release_info = self.get_latest_release_info()
            if not release_info:
                return False
            
            # Find the appropriate executable for the platform
            download_url = None
            filename = None
            
            for asset in release_info.get('assets', []):
                asset_name = asset['name'].lower()
                
                if self.system == 'windows':
                    if asset_name == 'yt-dlp.exe':
                        download_url = asset['browser_download_url']
                        filename = 'yt-dlp.exe'
                        break
                elif self.system in ['linux', 'darwin']:  # macOS is darwin
                    if asset_name == 'yt-dlp':
                        download_url = asset['browser_download_url']
                        filename = 'yt-dlp'
                        break
            
            if not download_url:
                print(f"❌ No suitable executable found for {self.system}")
                return False
            
            print(f"📥 Downloading yt-dlp executable...")
            print(f"   URL: {download_url}")
            
            # Download the file
            response = requests.get(download_url, timeout=60)
            response.raise_for_status()
            
            # Save to bin directory
            exe_path = self.bin_dir / filename
            with open(exe_path, 'wb') as f:
                f.write(response.content)
            
            # Make executable on Unix systems
            if self.system in ['linux', 'darwin']:
                os.chmod(exe_path, 0o755)
            
            print(f"✅ yt-dlp executable downloaded to: {exe_path}")
            return True
            
        except Exception as e:
            print(f"❌ Failed to download executable: {e}")
            return False
    
    def open_manual_download_page(self):
        """Open the yt-dlp releases page for manual download"""
        try:
            import webbrowser
            url = "https://github.com/yt-dlp/yt-dlp/releases"
            print(f"🌐 Opening yt-dlp releases page: {url}")
            webbrowser.open(url)
            
            print("\n📋 Manual Installation Instructions:")
            print("=" * 50)
            
            if self.system == 'windows':
                print("1. Download 'yt-dlp.exe' from the releases page")
                print(f"2. Save it to: {self.bin_dir}")
                print("3. Restart MFFUHijack")
            else:
                print("1. Download 'yt-dlp' (without .exe) from the releases page")
                print(f"2. Save it to: {self.bin_dir}")
                print("3. Make it executable: chmod +x yt-dlp")
                print("4. Restart MFFUHijack")
            
            print(f"\nAlternatively, you can try:")
            print(f"   pip install yt-dlp")
            
        except Exception as e:
            print(f"❌ Could not open browser: {e}")
            print(f"Please manually visit: https://github.com/yt-dlp/yt-dlp/releases")
    
    def install_yt_dlp(self, auto_download: bool = True) -> bool:
        """
        Comprehensive yt-dlp installation with multiple methods
        
        Args:
            auto_download: If True, automatically try to download executable
        
        Returns:
            bool: True if yt-dlp is available after installation
        """
        print("🚀 Installing yt-dlp for MFFUHijack...")
        print("=" * 50)
        
        # Check if already available
        if self.check_yt_dlp_command():
            print("✅ yt-dlp is already available!")
            return True
        
        # Method 1: Try pip installation (preferred)
        print("\n📦 Method 1: Installing via pip...")
        if self.install_via_pip():
            if self.check_yt_dlp_command():
                print("✅ yt-dlp successfully installed via pip!")
                return True
            else:
                print("⚠️  pip installation completed but yt-dlp still not working")
        
        # Method 2: Try downloading executable (if auto_download enabled)
        if auto_download:
            print("\n📥 Method 2: Downloading executable...")
            if self.download_executable():
                if self.check_yt_dlp_command():
                    print("✅ yt-dlp executable successfully downloaded!")
                    return True
                else:
                    print("⚠️  Executable downloaded but still not working")
        
        # Method 3: Manual download guidance
        print("\n🔧 Method 3: Manual installation required")
        print("Automatic installation failed. Opening manual download page...")
        self.open_manual_download_page()
        
        return False
    
    def get_installation_status(self) -> Dict[str, any]:
        """Get detailed installation status"""
        status = {
            'module_available': self.check_yt_dlp_module(),
            'command_working': self.check_yt_dlp_command(),
            'local_executable': (self.bin_dir / "yt-dlp.exe").exists() or (self.bin_dir / "yt-dlp").exists(),
            'bin_directory': str(self.bin_dir),
            'system': self.system,
            'architecture': self.architecture
        }
        
        return status


def install_yt_dlp_if_needed(auto_download: bool = True) -> bool:
    """
    Main function to install yt-dlp if needed
    
    Args:
        auto_download: If True, automatically try to download executable
    
    Returns:
        bool: True if yt-dlp is available
    """
    installer = YtDlpInstaller()
    
    # Check if already available
    if installer.check_yt_dlp_command():
        return True
    
    # Install if needed
    return installer.install_yt_dlp(auto_download=auto_download)


def get_yt_dlp_status() -> Dict[str, any]:
    """Get current yt-dlp installation status"""
    installer = YtDlpInstaller()
    return installer.get_installation_status()


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='yt-dlp installer for MFFUHijack')
    parser.add_argument('--no-auto-download', action='store_true',
                       help='Skip automatic executable download')
    parser.add_argument('--status-only', action='store_true',
                       help='Only check installation status')
    
    args = parser.parse_args()
    
    if args.status_only:
        status = get_yt_dlp_status()
        print("📊 yt-dlp Installation Status:")
        print("=" * 40)
        for key, value in status.items():
            print(f"{key}: {value}")
    else:
        auto_download = not args.no_auto_download
        success = install_yt_dlp_if_needed(auto_download=auto_download)
        sys.exit(0 if success else 1)
