#!/usr/bin/env python3
"""
Test script to demonstrate OCR improvements
Shows the difference between heavy preprocessing and raw image processing
"""

import cv2
import numpy as np
import sys
import os

# Add the support directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'MFFUHijack_Support'))

try:
    from ocr_utils import ocr_manager
    print("✅ OCR Manager loaded successfully")
except ImportError as e:
    print(f"❌ Failed to import OCR Manager: {e}")
    sys.exit(1)

def create_test_image():
    """Create a test image with typical livestream text"""
    # Create a black background
    img = np.zeros((100, 400, 3), dtype=np.uint8)
    
    # Add white text similar to livestream codes
    font = cv2.FONT_HERSHEY_SIMPLEX
    text = "FREE RESETS USE CODE: RESET123"
    
    # Put text on image
    cv2.putText(img, text, (10, 50), font, 0.7, (255, 255, 255), 2)
    
    return img

def test_ocr_methods():
    """Test different OCR processing methods"""
    print("🧪 Testing OCR Methods")
    print("=" * 50)
    
    # Create test image
    test_img = create_test_image()
    print(f"📷 Created test image: {test_img.shape}")
    
    # Test 1: Raw image processing (new method)
    print("\n🔍 Test 1: Raw Image Processing (NEW)")
    print("-" * 30)
    try:
        results_raw = ocr_manager.extract_text_from_image(test_img, "full", use_raw_image=True)
        print(f"Results: {len(results_raw)} text regions found")
        for i, result in enumerate(results_raw):
            print(f"  {i+1}. '{result.get('text', '')}' (confidence: {result.get('confidence', 0):.3f})")
    except Exception as e:
        print(f"❌ Raw processing failed: {e}")
    
    # Test 2: With preprocessing (old method)
    print("\n🔍 Test 2: With Preprocessing (OLD)")
    print("-" * 30)
    try:
        results_processed = ocr_manager.extract_text_from_image(test_img, "full", use_raw_image=False)
        print(f"Results: {len(results_processed)} text regions found")
        for i, result in enumerate(results_processed):
            print(f"  {i+1}. '{result.get('text', '')}' (confidence: {result.get('confidence', 0):.3f})")
    except Exception as e:
        print(f"❌ Processed method failed: {e}")
    
    # Test 3: Code detection
    print("\n🎯 Test 3: Code Detection")
    print("-" * 30)
    try:
        codes = ocr_manager.process_frame_for_codes(test_img, "full")
        print(f"Codes found: {len(codes)}")
        for i, code in enumerate(codes):
            print(f"  {i+1}. Code: '{code.get('code', '')}' Type: '{code.get('type', '')}' Confidence: {code.get('confidence', 0):.3f}")
    except Exception as e:
        print(f"❌ Code detection failed: {e}")

if __name__ == "__main__":
    test_ocr_methods()
    print("\n✅ OCR testing completed!")
