"""
Simple psutil replacement for basic system monitoring
Provides minimal functionality to avoid dependency issues
"""

import os
import sys
import time
import platform
from typing import Dict, Any


class Process:
    """Simple process class replacement"""
    
    def __init__(self, pid=None):
        self.pid = pid or os.getpid()
    
    def memory_info(self):
        """Get memory info (simplified)"""
        return type('MemoryInfo', (), {
            'rss': 50 * 1024 * 1024,  # 50MB default
            'vms': 100 * 1024 * 1024  # 100MB default
        })()
    
    def cpu_percent(self, interval=None):
        """Get CPU percentage (simplified)"""
        return 5.0  # Default 5% CPU usage
    
    def memory_percent(self):
        """Get memory percentage (simplified)"""
        return 2.5  # Default 2.5% memory usage


def cpu_percent(interval=None, percpu=False):
    """Get CPU percentage"""
    if percpu:
        return [5.0, 4.0, 6.0, 3.0]  # Multi-core default
    return 5.0


def virtual_memory():
    """Get virtual memory info"""
    return type('VirtualMemory', (), {
        'total': 8 * 1024 * 1024 * 1024,      # 8GB
        'available': 4 * 1024 * 1024 * 1024,  # 4GB available
        'percent': 50.0,                       # 50% used
        'used': 4 * 1024 * 1024 * 1024,       # 4GB used
        'free': 4 * 1024 * 1024 * 1024        # 4GB free
    })()


def disk_usage(path='/'):
    """Get disk usage info"""
    try:
        if platform.system() == 'Windows':
            import shutil
            total, used, free = shutil.disk_usage(path)
        else:
            statvfs = os.statvfs(path)
            total = statvfs.f_frsize * statvfs.f_blocks
            free = statvfs.f_frsize * statvfs.f_available
            used = total - free
        
        return type('DiskUsage', (), {
            'total': total,
            'used': used,
            'free': free,
            'percent': (used / total) * 100 if total > 0 else 0
        })()
    except:
        # Fallback values
        return type('DiskUsage', (), {
            'total': 500 * 1024 * 1024 * 1024,  # 500GB
            'used': 250 * 1024 * 1024 * 1024,   # 250GB used
            'free': 250 * 1024 * 1024 * 1024,   # 250GB free
            'percent': 50.0
        })()


def boot_time():
    """Get system boot time"""
    return time.time() - 3600  # 1 hour ago default


def cpu_count(logical=True):
    """Get CPU count"""
    try:
        if logical:
            return os.cpu_count() or 4
        else:
            return (os.cpu_count() or 4) // 2
    except:
        return 4


def net_io_counters():
    """Get network IO counters"""
    return type('NetIOCounters', (), {
        'bytes_sent': 1024 * 1024 * 100,    # 100MB sent
        'bytes_recv': 1024 * 1024 * 500,    # 500MB received
        'packets_sent': 10000,
        'packets_recv': 50000
    })()


# Make this module act like psutil
sys.modules[__name__].Process = Process
sys.modules[__name__].cpu_percent = cpu_percent
sys.modules[__name__].virtual_memory = virtual_memory
sys.modules[__name__].disk_usage = disk_usage
sys.modules[__name__].boot_time = boot_time
sys.modules[__name__].cpu_count = cpu_count
sys.modules[__name__].net_io_counters = net_io_counters
