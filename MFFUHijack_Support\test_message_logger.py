#!/usr/bin/env python3
"""
Test script for the message logger functionality
"""

import sys
import os
from PyQt6.QtWidgets import <PERSON>A<PERSON><PERSON>, QPushButton, QVBoxLayout, QWidget, QMainWindow
from PyQt6.QtCore import QTimer

# Add current directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from gui import MessageLogger, MessageLoggerWindow


class TestWindow(QMainWindow):
    """Simple test window to demonstrate message logging"""
    
    def __init__(self, message_logger):
        super().__init__()
        self.message_logger = message_logger
        self.init_ui()
        
        # Auto-generate some test messages
        self.timer = QTimer()
        self.timer.timeout.connect(self.generate_test_message)
        self.message_count = 0
    
    def init_ui(self):
        """Initialize the test window UI"""
        self.setWindowTitle("Message Logger Test")
        self.setGeometry(100, 100, 400, 300)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout()
        central_widget.setLayout(layout)
        
        # Test buttons
        info_btn = QPushButton("Log Info Message")
        info_btn.clicked.connect(lambda: self.message_logger.info("This is an info message"))
        layout.addWidget(info_btn)
        
        warning_btn = QPushButton("Log Warning Message")
        warning_btn.clicked.connect(lambda: self.message_logger.warning("This is a warning message"))
        layout.addWidget(warning_btn)
        
        error_btn = QPushButton("Log Error Message")
        error_btn.clicked.connect(lambda: self.message_logger.error("This is an error message"))
        layout.addWidget(error_btn)
        
        status_btn = QPushButton("Log Status Message")
        status_btn.clicked.connect(lambda: self.message_logger.status("This is a status message"))
        layout.addWidget(status_btn)
        
        success_btn = QPushButton("Log Success Message")
        success_btn.clicked.connect(lambda: self.message_logger.success("This is a success message"))
        layout.addWidget(success_btn)
        
        auto_btn = QPushButton("Start Auto Messages")
        auto_btn.clicked.connect(self.start_auto_messages)
        layout.addWidget(auto_btn)
        
        stop_btn = QPushButton("Stop Auto Messages")
        stop_btn.clicked.connect(self.stop_auto_messages)
        layout.addWidget(stop_btn)
    
    def start_auto_messages(self):
        """Start generating automatic test messages"""
        self.timer.start(2000)  # Every 2 seconds
        self.message_logger.info("Started automatic message generation")
    
    def stop_auto_messages(self):
        """Stop generating automatic test messages"""
        self.timer.stop()
        self.message_logger.info("Stopped automatic message generation")
    
    def generate_test_message(self):
        """Generate a test message"""
        self.message_count += 1
        
        message_types = [
            ("info", f"Auto-generated info message #{self.message_count}"),
            ("warning", f"Auto-generated warning #{self.message_count}"),
            ("error", f"Auto-generated error #{self.message_count}"),
            ("status", f"Auto-generated status #{self.message_count}"),
            ("success", f"Auto-generated success #{self.message_count}")
        ]
        
        msg_type, msg_text = message_types[self.message_count % len(message_types)]
        
        if msg_type == "info":
            self.message_logger.info(msg_text)
        elif msg_type == "warning":
            self.message_logger.warning(msg_text)
        elif msg_type == "error":
            self.message_logger.error(msg_text)
        elif msg_type == "status":
            self.message_logger.status(msg_text)
        elif msg_type == "success":
            self.message_logger.success(msg_text)


def main():
    """Test the message logger functionality"""
    print("🧪 Testing Message Logger...")
    
    # Create QApplication
    app = QApplication(sys.argv)
    
    # Create message logger
    message_logger = MessageLogger()
    
    # Create message logger window
    logger_window = MessageLoggerWindow(message_logger)
    logger_window.show()
    
    # Create test window
    test_window = TestWindow(message_logger)
    test_window.show()
    
    # Log initial messages
    message_logger.info("Message logger test started")
    message_logger.status("System initialized")
    message_logger.success("All components loaded successfully")
    message_logger.warning("This is a test warning")
    message_logger.error("This is a test error")
    
    # Start event loop
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
