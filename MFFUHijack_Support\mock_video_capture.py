#!/usr/bin/env python3
"""
Mock Video Capture for Testing MFFUHijack GUI and Logging
Creates synthetic video frames with test codes for development and testing
"""

import cv2
import numpy as np
import time
import random
from typing import Optional
from datetime import datetime


class MockVideoCapture:
    """Mock video capture that generates synthetic frames with test codes"""
    
    def __init__(self):
        self.is_active = False
        self.frame_count = 0
        self.width = 854
        self.height = 480
        
        # Test codes to randomly display (using the new pattern format)
        self.test_codes = [
            "FREE 50 STARTER: ABC123",
            "x5 FREE RESETS USE CODE: RESET3J",
            "STARTER PLUS ACCOUNT USE CODE: XYZ789",
            "EXPERT CHALLENGE USE CODE: EXPERT99",
            "FREE RESET with CODE: NEWBIE42",
            "STARTER+ BONUS CODE: START55",
            "Get your EXPERT account CODE: ELITE88",
            "Multiple RESETS available CODE: MULTI99"
        ]
        
        # Probability of showing a code (10% chance per frame)
        self.code_probability = 0.1
        
    def start_capture(self, youtube_url: str) -> bool:
        """Start mock capture (always succeeds)"""
        print(f"🎭 Starting MOCK video capture for: {youtube_url}")
        print(f"📺 Mock video properties:")
        print(f"   Resolution: {self.width}x{self.height}")
        print(f"   FPS: 10 (simulated)")
        print(f"   Test codes available: {len(self.test_codes)}")
        
        self.is_active = True
        self.frame_count = 0
        return True
    
    def get_frame(self) -> Optional[np.ndarray]:
        """Generate a synthetic frame"""
        if not self.is_active:
            return None
        
        self.frame_count += 1
        
        # Create a base frame (dark blue background)
        frame = np.full((self.height, self.width, 3), (40, 40, 100), dtype=np.uint8)
        
        # Add some visual elements to make it look like a stream
        # Add a header bar
        cv2.rectangle(frame, (0, 0), (self.width, 60), (20, 20, 60), -1)
        cv2.putText(frame, "MOCK LIVESTREAM - MFFUHijack Test", (20, 35), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
        
        # Add frame counter
        cv2.putText(frame, f"Frame: {self.frame_count}", (20, 80), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (200, 200, 200), 1)
        
        # Add timestamp
        timestamp = datetime.now().strftime("%H:%M:%S")
        cv2.putText(frame, f"Time: {timestamp}", (20, 110), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (200, 200, 200), 1)
        
        # Add some animated elements
        # Moving circle
        circle_x = int((self.frame_count * 3) % self.width)
        cv2.circle(frame, (circle_x, 150), 10, (0, 255, 0), -1)
        
        # Add OCR region indicator (red box) FIRST
        # Bottom third region (67% down, 33% height)
        region_y_start = int(self.height * 0.67)
        cv2.rectangle(frame, (0, region_y_start), (self.width, self.height), (0, 0, 255), 1)

        # Randomly add test codes (in the bottom third where OCR looks)
        if random.random() < self.code_probability:
            code = random.choice(self.test_codes)

            # Position in the OCR scan region for better detection
            y_pos = region_y_start + 40 + random.randint(0, 50)  # Within OCR region
            x_pos = 20  # Left aligned for better OCR

            # Add large background rectangle for maximum visibility
            text_size = cv2.getTextSize(code, cv2.FONT_HERSHEY_SIMPLEX, 1.0, 3)[0]
            cv2.rectangle(frame,
                         (x_pos - 15, y_pos - text_size[1] - 15),
                         (x_pos + text_size[0] + 15, y_pos + 15),
                         (0, 0, 0), -1)  # Black background

            # Add white border for contrast
            cv2.rectangle(frame,
                         (x_pos - 15, y_pos - text_size[1] - 15),
                         (x_pos + text_size[0] + 15, y_pos + 15),
                         (255, 255, 255), 2)  # White border

            # Add the code text in bright white with bold styling (simulating Montserrat SemiBold/Bold)
            # Use larger font size and thicker lines to simulate bold Montserrat
            cv2.putText(frame, code, (x_pos, y_pos),
                       cv2.FONT_HERSHEY_SIMPLEX, 1.2, (255, 255, 255), 4)  # Larger size, thicker lines

            # Add a second layer slightly offset to make it even bolder (simulating SemiBold/Bold)
            cv2.putText(frame, code, (x_pos + 1, y_pos),
                       cv2.FONT_HERSHEY_SIMPLEX, 1.2, (255, 255, 255), 2)

            print(f"🎯 MOCK: Generated frame {self.frame_count} with code: {code}")
            print(f"      Position: x={x_pos}, y={y_pos} (within OCR region {region_y_start}-{self.height})")

        # Add small OCR region label (less intrusive)
        cv2.putText(frame, "OCR REGION", (self.width - 120, region_y_start + 15),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 0, 255), 1)
        
        return frame
    
    def stop_capture(self):
        """Stop mock capture"""
        self.is_active = False
        print(f"🎭 Mock video capture stopped after {self.frame_count} frames")


class MockLivestreamCapture:
    """Drop-in replacement for LivestreamCapture that uses mock video"""
    
    def __init__(self):
        self.mock_capture = MockVideoCapture()
        self.stream_url = None
        self.video_url = None
        self.cap = None
        self.is_active = False
    
    def get_stream_url(self, youtube_url: str) -> Optional[str]:
        """Mock URL extraction (always succeeds)"""
        print(f"🎭 MOCK: Simulating yt-dlp URL extraction for: {youtube_url}")
        print(f"🛠️  MOCK: Running command: python -m yt_dlp --get-url --format best[height<=720] {youtube_url}")
        print(f"📤 MOCK: yt-dlp return code: 0")
        print(f"📤 MOCK: yt-dlp stdout: https://mock-stream-url.example.com/video.m3u8")
        print(f"✅ MOCK: Successfully extracted stream URL")
        
        # Simulate some delay
        time.sleep(1)
        
        return "https://mock-stream-url.example.com/video.m3u8"
    
    def start_capture(self, youtube_url: str) -> bool:
        """Start mock capture"""
        print(f"🎭 MOCK: Starting video capture for: {youtube_url}")
        self.stream_url = youtube_url
        
        # Mock URL extraction
        self.video_url = self.get_stream_url(youtube_url)
        if not self.video_url:
            return False
        
        # Start mock capture
        success = self.mock_capture.start_capture(youtube_url)
        if success:
            self.is_active = True
            print("✅ MOCK: Video capture started successfully!")
        
        return success
    
    def get_frame(self) -> Optional[np.ndarray]:
        """Get mock frame"""
        if not self.is_active:
            return None
        
        # Add small delay to simulate realistic frame rate
        time.sleep(0.1)  # 10 FPS
        
        return self.mock_capture.get_frame()
    
    def stop_capture(self):
        """Stop mock capture"""
        self.is_active = False
        self.mock_capture.stop_capture()
        print("🎭 MOCK: Video capture stopped")


def enable_mock_mode():
    """Enable mock mode by replacing the real capture class"""
    import live_bot
    
    # Replace the real LivestreamCapture with our mock
    live_bot.LivestreamCapture = MockLivestreamCapture
    
    print("🎭 MOCK MODE ENABLED")
    print("   Real yt-dlp and video capture replaced with mock implementations")
    print("   This will generate synthetic frames with test codes for development")


if __name__ == "__main__":
    # Test the mock capture directly
    print("🧪 Testing Mock Video Capture")
    print("=" * 40)
    
    capture = MockVideoCapture()
    
    if capture.start_capture("https://mock-url.com"):
        print("📹 Generating 10 test frames...")
        
        for i in range(10):
            frame = capture.get_frame()
            if frame is not None:
                print(f"   Frame {i+1}: {frame.shape}")
            time.sleep(0.2)
        
        capture.stop_capture()
        print("✅ Mock capture test completed!")
    else:
        print("❌ Mock capture test failed!")
