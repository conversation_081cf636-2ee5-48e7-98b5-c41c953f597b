#!/usr/bin/env python3
"""
Test script for Live Stream Testing Mode
Demonstrates testing on previous livestreams with configurable intervals
"""

import sys
import os
import time
import cv2
import numpy as np
from datetime import datetime
from PyQt6.QtWidgets import *
from PyQt6.QtCore import *
from PyQt6.QtGui import *

from livestream_testing_gui import LiveStreamTestingWindow
from livestream_testing_mode import (
    CodeValidationTester, StreamProcessor, YouTubeStreamDownloader, TestingStatistics
)


class TestingModeDemo(QMainWindow):
    """Demo window for testing mode features"""
    
    def __init__(self):
        super().__init__()
        self.testing_window = None
        self.init_ui()
        self.create_sample_video()
    
    def init_ui(self):
        """Initialize demo UI"""
        self.setWindowTitle("Live Stream Testing Mode - Demo")
        self.setGeometry(100, 100, 800, 600)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # Header
        header_label = QLabel("🧪 Live Stream Testing Mode Demo")
        header_label.setFont(QFont("Arial", 18, QFont.Weight.Bold))
        header_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(header_label)
        
        # Description
        desc_text = """
This demo showcases the Live Stream Testing Mode for MFFUHijack.

Features:
• Test on previous livestreams or recorded videos
• Configurable frame scanning intervals (5s, 10s, 15s, etc.)
• Code validation without purchasing (test mode)
• Real-time OCR processing with EasyOCR/PaddleOCR
• Comprehensive testing statistics and results
• YouTube stream downloading for testing
        """
        
        desc_label = QLabel(desc_text.strip())
        desc_label.setWordWrap(True)
        desc_label.setStyleSheet("padding: 10px; background-color: #f0f0f0; border-radius: 5px;")
        layout.addWidget(desc_label)
        
        # Demo controls
        demo_group = QGroupBox("🎮 Demo Controls")
        demo_layout = QVBoxLayout(demo_group)
        
        # Create sample video button
        sample_btn = QPushButton("🎥 Create Sample Test Video")
        sample_btn.clicked.connect(self.create_sample_video)
        demo_layout.addWidget(sample_btn)
        
        # Open testing mode button
        testing_btn = QPushButton("🧪 Open Live Stream Testing Mode")
        testing_btn.clicked.connect(self.open_testing_mode)
        demo_layout.addWidget(testing_btn)
        
        # Test individual components
        components_layout = QHBoxLayout()
        
        test_ocr_btn = QPushButton("🔍 Test OCR")
        test_ocr_btn.clicked.connect(self.test_ocr_component)
        components_layout.addWidget(test_ocr_btn)
        
        test_validation_btn = QPushButton("✅ Test Validation")
        test_validation_btn.clicked.connect(self.test_validation_component)
        components_layout.addWidget(test_validation_btn)
        
        test_stream_btn = QPushButton("📺 Test Stream Processing")
        test_stream_btn.clicked.connect(self.test_stream_processing)
        components_layout.addWidget(test_stream_btn)
        
        demo_layout.addLayout(components_layout)
        
        layout.addWidget(demo_group)
        
        # Instructions
        instructions_group = QGroupBox("📋 Instructions")
        instructions_layout = QVBoxLayout(instructions_group)
        
        instructions_text = """
1. Click 'Create Sample Test Video' to generate a test video with codes
2. Click 'Open Live Stream Testing Mode' to launch the testing interface
3. In the testing mode:
   • Select the sample video from the stream list
   • Configure frame interval (try 2-5 seconds for demo)
   • Set OCR region (default is fine for sample video)
   • Enable/disable code validation
   • Click 'Start Testing' to begin
4. Watch real-time results and statistics
5. Use individual component tests to verify functionality

Sample Video Features:
• Contains various code formats (START123, EXPERT456, etc.)
• Codes appear at different timestamps
• Different account types represented
• Realistic livestream simulation
        """
        
        instructions_label = QLabel(instructions_text.strip())
        instructions_label.setWordWrap(True)
        instructions_layout.addWidget(instructions_label)
        
        layout.addWidget(instructions_group)
        
        # Status
        self.status_label = QLabel("Ready for testing")
        self.status_label.setStyleSheet("padding: 5px; background-color: #e8f5e8; border-radius: 3px;")
        layout.addWidget(self.status_label)
    
    def create_sample_video(self):
        """Create a sample video with codes for testing"""
        try:
            # Create test_streams directory
            os.makedirs("test_streams", exist_ok=True)
            
            # Video parameters
            width, height = 854, 480
            fps = 30
            duration = 60  # 60 seconds
            total_frames = fps * duration
            
            # Create video writer
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            video_path = "test_streams/sample_test_video.mp4"
            out = cv2.VideoWriter(video_path, fourcc, fps, (width, height))
            
            # Sample codes to display at different times
            sample_codes = [
                (5, "FREE 50 STARTER: START123", "Starter"),
                (15, "EXPERT ACCOUNT: EXPERT456", "Expert"),
                (25, "USE CODE: RESET789", "Reset"),
                (35, "STARTER PLUS: PLUS999", "Starter Plus"),
                (45, "FREE RESET CODE: FREE555", "Free Reset Code"),
                (55, "SPECIAL OFFER: SPECIAL777", "Expert")
            ]
            
            self.status_label.setText("Creating sample video...")
            QApplication.processEvents()
            
            for frame_num in range(total_frames):
                # Create frame
                frame = np.ones((height, width, 3), dtype=np.uint8) * 40  # Dark background
                
                # Add title
                cv2.putText(frame, "MFFU LIVE STREAM SIMULATION", (200, 50), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
                
                # Add timestamp
                current_time = frame_num / fps
                timestamp = f"Time: {int(current_time//60):02d}:{int(current_time%60):02d}"
                cv2.putText(frame, timestamp, (50, height - 50), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (200, 200, 200), 1)
                
                # Add code if it's time
                for code_time, code_text, code_type in sample_codes:
                    if abs(current_time - code_time) < 2:  # Show for 2 seconds
                        # Calculate position in OCR region (bottom third)
                        y_pos = int(height * 0.75)
                        
                        # Add background rectangle for better visibility
                        text_size = cv2.getTextSize(code_text, cv2.FONT_HERSHEY_SIMPLEX, 0.7, 2)[0]
                        cv2.rectangle(frame, 
                                    (200, y_pos - 30), 
                                    (200 + text_size[0] + 20, y_pos + 10), 
                                    (0, 0, 0), -1)
                        
                        # Add code text
                        cv2.putText(frame, code_text, (210, y_pos), 
                                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
                        
                        # Add account type indicator
                        cv2.putText(frame, f"Account Type: {code_type}", (210, y_pos + 25), 
                                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 0), 1)
                
                # Add frame number for debugging
                cv2.putText(frame, f"Frame: {frame_num}", (width - 150, 30), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (150, 150, 150), 1)
                
                # Write frame
                out.write(frame)
            
            # Release video writer
            out.release()
            
            self.status_label.setText(f"✅ Sample video created: {video_path}")
            QMessageBox.information(self, "Success", 
                                  f"Sample test video created successfully!\n\n"
                                  f"Location: {video_path}\n"
                                  f"Duration: {duration} seconds\n"
                                  f"Codes: {len(sample_codes)} codes at different timestamps\n\n"
                                  f"You can now use this video in the testing mode.")
            
        except Exception as e:
            self.status_label.setText(f"❌ Error creating video: {e}")
            QMessageBox.critical(self, "Error", f"Failed to create sample video: {e}")
    
    def open_testing_mode(self):
        """Open the testing mode window"""
        try:
            if not self.testing_window:
                self.testing_window = LiveStreamTestingWindow()
            
            self.testing_window.show()
            self.testing_window.raise_()
            self.testing_window.activateWindow()
            
            self.status_label.setText("🧪 Testing mode opened")
            
        except Exception as e:
            self.status_label.setText(f"❌ Error opening testing mode: {e}")
            QMessageBox.critical(self, "Error", f"Failed to open testing mode: {e}")
    
    def test_ocr_component(self):
        """Test OCR component individually"""
        try:
            # Create a simple test image with text
            test_image = np.ones((100, 400, 3), dtype=np.uint8) * 255  # White background
            cv2.putText(test_image, "TEST CODE: ABC123", (50, 50), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 2)
            
            # Test with available OCR engines
            results = []
            
            try:
                import easyocr
                reader = easyocr.Reader(['en'])
                ocr_results = reader.readtext(test_image)
                results.append(f"EasyOCR: {ocr_results}")
            except ImportError:
                results.append("EasyOCR: Not available")
            
            try:
                import paddleocr
                reader = paddleocr.PaddleOCR(use_angle_cls=True, lang='en')
                ocr_results = reader.ocr(test_image, cls=True)
                results.append(f"PaddleOCR: {ocr_results}")
            except ImportError:
                results.append("PaddleOCR: Not available")
            
            result_text = "\n".join(results)
            QMessageBox.information(self, "OCR Test Results", result_text)
            self.status_label.setText("✅ OCR component tested")
            
        except Exception as e:
            QMessageBox.critical(self, "OCR Test Error", f"OCR test failed: {e}")
            self.status_label.setText(f"❌ OCR test failed: {e}")
    
    def test_validation_component(self):
        """Test code validation component"""
        try:
            # Note: This is a demo - actual validation requires browser setup
            QMessageBox.information(self, "Validation Test", 
                                  "Code validation test would normally:\n\n"
                                  "1. Open headless browser\n"
                                  "2. Navigate to MFFU checkout page\n"
                                  "3. Enter test code\n"
                                  "4. Check for validation response\n"
                                  "5. Return result without purchasing\n\n"
                                  "This requires proper browser setup and network access.")
            
            self.status_label.setText("ℹ️ Validation component info displayed")
            
        except Exception as e:
            QMessageBox.critical(self, "Validation Test Error", f"Validation test failed: {e}")
    
    def test_stream_processing(self):
        """Test stream processing component"""
        try:
            # Check if sample video exists
            video_path = "test_streams/sample_test_video.mp4"
            if not os.path.exists(video_path):
                QMessageBox.warning(self, "No Sample Video", 
                                  "Please create a sample video first by clicking 'Create Sample Test Video'")
                return
            
            # Test stream processor
            processor = StreamProcessor()
            if processor.load_stream(video_path):
                info_text = f"Stream loaded successfully!\n\n"
                info_text += f"Total frames: {processor.total_frames}\n"
                info_text += f"FPS: {processor.fps:.1f}\n"
                info_text += f"Duration: {processor.duration:.1f} seconds\n"
                
                # Test getting a frame
                frame_result = processor.get_frame_at_interval(5.0)
                if frame_result:
                    info_text += f"\nFrame extraction test: ✅ Success"
                else:
                    info_text += f"\nFrame extraction test: ❌ Failed"
                
                processor.close()
                
                QMessageBox.information(self, "Stream Processing Test", info_text)
                self.status_label.setText("✅ Stream processing tested")
            else:
                QMessageBox.critical(self, "Stream Processing Error", "Failed to load sample video")
                self.status_label.setText("❌ Stream processing test failed")
                
        except Exception as e:
            QMessageBox.critical(self, "Stream Processing Error", f"Stream processing test failed: {e}")
            self.status_label.setText(f"❌ Stream processing test failed: {e}")


def main():
    """Main demo function"""
    print("🧪 Starting Live Stream Testing Mode Demo")
    print("=" * 50)
    
    app = QApplication(sys.argv)
    
    demo_window = TestingModeDemo()
    demo_window.show()
    
    print("📋 Demo Instructions:")
    print("1. Create a sample test video with embedded codes")
    print("2. Open the Live Stream Testing Mode")
    print("3. Configure testing parameters:")
    print("   • Frame interval (5-15 seconds recommended)")
    print("   • OCR engine (EasyOCR or PaddleOCR)")
    print("   • OCR region (default works for sample video)")
    print("   • Code validation (enable for full testing)")
    print("4. Start testing and monitor results")
    print("5. View comprehensive statistics")
    print()
    print("🎯 Testing Mode Features:")
    print("• Configurable frame scanning intervals")
    print("• Real-time OCR processing")
    print("• Code validation without purchasing")
    print("• Comprehensive testing statistics")
    print("• YouTube stream downloading")
    print("• Previous livestream analysis")
    
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
