# MFFUHijack Automatic Installation System

## 🎯 Overview

MFFUHijack now features a comprehensive automatic installation system that handles all required dependencies and external tools when you first run the application. No more manual dependency management!

## 🚀 How It Works

### First-Time Startup
When you run MFF<PERSON>Hijack for the first time:

1. **Automatic Detection**: The system scans for missing dependencies
2. **Smart Installation**: Automatically installs missing Python packages
3. **External Tools**: Attempts to install FFmpeg and other external tools
4. **User-Friendly**: Shows progress and handles errors gracefully
5. **One-Time Setup**: Remembers what's installed for future runs

### What Gets Installed Automatically

#### ✅ Python Packages (via pip)
- **PyQt6** - GUI framework
- **opencv-python** - Computer vision
- **numpy** - Numerical computing
- **requests** - HTTP library
- **Pillow** - Image processing
- **yt-dlp** - YouTube video downloader
- **easyocr** - OCR engine (recommended)
- **torch** - Machine learning framework
- **torchvision** - Computer vision utilities
- **tqdm** - Progress bars

#### 🛠️ External Tools
- **FFmpeg** - Video processing (platform-specific installation)

## 📋 Usage Methods

### Method 1: Automatic (Recommended)
Simply run the application - it will handle everything automatically:

```bash
python main.py
```

The first time you run this, it will:
- Check for missing dependencies
- Automatically install everything needed
- Show progress in real-time
- Start the application when complete

### Method 2: Manual Installation Check
Run the automatic installer separately:

```bash
python auto_installer.py
```

Options:
- `--no-optional` - Skip optional packages
- `--status-only` - Only check what's installed

### Method 3: Test Installation Status
Check what's currently installed:

```bash
python test_auto_installer.py
```

## 🔧 Platform-Specific Behavior

### Windows
- **Python packages**: Installed via pip
- **FFmpeg**: Downloaded and extracted automatically from GitHub releases
- **Local installation**: FFmpeg installed to `./ffmpeg/` directory
- **PATH management**: Automatically added to session PATH

### macOS
- **Python packages**: Installed via pip
- **FFmpeg**: Installed via Homebrew (if available)
- **Fallback**: Manual installation instructions provided

### Linux
- **Python packages**: Installed via pip
- **FFmpeg**: Attempts installation via system package manager
- **Supported**: apt, yum, dnf, pacman, zypper
- **Fallback**: Manual installation instructions provided

## ⚙️ Configuration

### Automatic Installation Settings
The system creates a configuration file `startup_config.json` to remember:
- First run completion status
- User preferences
- Previously installed packages
- Skip dependency check setting

### Customization Options
You can modify the automatic installation behavior by editing:
- `auto_installer.py` - Main installation logic
- `ffmpeg_installer.py` - FFmpeg-specific installation
- `startup_checker.py` - Startup dependency checking

## 🚨 Error Handling

### Common Issues and Solutions

#### 1. **Permission Errors**
```
❌ Failed to install package: Permission denied
```
**Solution**: Run with administrator/sudo privileges or use virtual environment

#### 2. **Network Issues**
```
❌ Failed to download FFmpeg: Connection timeout
```
**Solution**: Check internet connection, try again later, or install manually

#### 3. **Package Installation Failures**
```
❌ Failed to install torch: Disk space insufficient
```
**Solution**: Free up disk space (PyTorch requires ~1GB+)

#### 4. **FFmpeg Installation Issues**
```
❌ Could not install FFmpeg automatically
```
**Solution**: Follow manual installation instructions provided

### Fallback Options
If automatic installation fails:
1. **Manual Installation**: Follow provided instructions
2. **Partial Functionality**: App runs with limited features
3. **Retry Option**: Run installer again after fixing issues

## 📊 Installation Status

### Check Current Status
```bash
python auto_installer.py --status-only
```

Example output:
```
📊 Installation Status:
======================
✅ PyQt6
✅ opencv-python
✅ numpy
✅ requests
✅ Pillow
✅ yt-dlp
✅ easyocr
❌ torch
❌ torchvision
❌ ffmpeg
```

### Detailed Testing
```bash
python test_auto_installer.py
```

This runs comprehensive tests of:
- Installation status checking
- FFmpeg installer functionality
- Startup dependency verification

## 🎛️ Advanced Usage

### Skip Automatic Installation
```bash
python main.py --skip-deps
```

### Console-Only Installation
```bash
python launch.py --no-gui-installer
```

### Install Only (Don't Start App)
```bash
python launch.py --install-only
```

## 🔍 Troubleshooting

### Debug Installation Issues
1. **Check Python version**: Requires Python 3.8+
2. **Check pip**: Ensure pip is working: `pip --version`
3. **Check internet**: Some packages require internet access
4. **Check disk space**: PyTorch and other packages are large
5. **Check permissions**: May need admin rights for system-wide installation

### Manual Fallback
If automatic installation completely fails:

```bash
# Install required packages manually
pip install PyQt6 opencv-python numpy requests Pillow yt-dlp

# Install optional packages
pip install easyocr torch torchvision tqdm

# Install FFmpeg manually (see platform-specific instructions)
```

### Reset Installation State
Delete `startup_config.json` to reset first-run status and re-trigger automatic installation.

## 💡 Tips for Success

1. **Stable Internet**: Ensure good internet connection for downloads
2. **Sufficient Space**: Have at least 2GB free disk space
3. **Updated pip**: Run `pip install --upgrade pip` first
4. **Virtual Environment**: Consider using venv for clean installation
5. **Administrator Rights**: May be needed for system-wide installation

## 🎉 Benefits

- **Zero Manual Setup**: Just run and go
- **Cross-Platform**: Works on Windows, macOS, and Linux
- **Error Recovery**: Handles failures gracefully
- **User-Friendly**: Clear progress and error messages
- **Efficient**: Only installs what's missing
- **Persistent**: Remembers installation state

The automatic installation system ensures that MFFUHijack works out of the box for all users, regardless of their technical expertise or platform!
