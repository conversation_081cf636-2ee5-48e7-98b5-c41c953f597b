#!/bin/bash

echo "========================================="
echo " MFFUHijack - Auto-Start"
echo "========================================="
echo
echo "🚀 Starting MFFUHijack with automatic setup..."
echo
echo "First-time users: All dependencies will be"
echo "installed automatically. This may take a"
echo "few minutes depending on your internet speed."
echo

# Try different Python commands
if command -v python3 &> /dev/null; then
    echo "✅ Python3 found"
    echo
    echo "🎯 Launching MFFUHijack..."
    python3 main.py
elif command -v python &> /dev/null; then
    echo "✅ Python found"
    echo
    echo "🎯 Launching MFFUHijack..."
    python main.py
else
    echo "❌ Python not found in PATH"
    echo "Please install Python 3.8 or higher"
    echo
    echo "Installation commands:"
    echo "  Ubuntu/Debian: sudo apt install python3 python3-pip"
    echo "  CentOS/RHEL:   sudo yum install python3 python3-pip"
    echo "  macOS:         brew install python3"
    echo
    echo "Alternative options:"
    echo "  python3 auto_installer.py  - Run installer separately"
    echo "  python3 test_auto_installer.py - Test installation status"
    echo
    exit 1
fi

echo
echo "👋 MFFUHijack has closed."
