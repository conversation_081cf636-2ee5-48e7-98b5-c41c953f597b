"""
Frame Selector Widget for MFFUHijack
Allows users to select specific frames from videos with time slider
"""

import cv2
import numpy as np
import subprocess
import os
import tempfile
from typing import Optional, Callable
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QSlider, QSpinBox, QGroupBox, QProgressBar, QLineEdit
)
from PyQt6.QtCore import Qt, pyqtSignal, QThread, QTimer
from PyQt6.QtGui import QPixmap, QImage

from region_selector import RegionSelectorWidget


class VideoFrameExtractor(QThread):
    """Thread for extracting frames from video"""
    
    frame_extracted = pyqtSignal(np.ndarray, int)  # frame, frame_number
    progress_update = pyqtSignal(int)  # progress percentage
    extraction_complete = pyqtSignal(int)  # total frames
    error_occurred = pyqtSignal(str)  # error message
    
    def __init__(self, video_url: str, max_frames: int = 100):
        super().__init__()
        self.video_url = video_url
        self.max_frames = max_frames
        self.frames = []
        self.is_running = False
    
    def run(self):
        """Extract frames from video"""
        self.is_running = True
        
        try:
            # Get direct video URL using yt-dlp
            self.progress_update.emit(10)
            direct_url = self.get_direct_video_url()
            if not direct_url:
                self.error_occurred.emit("Failed to get video URL")
                return
            
            self.progress_update.emit(20)
            
            # Open video with OpenCV
            cap = cv2.VideoCapture(direct_url)
            if not cap.isOpened():
                self.error_occurred.emit("Failed to open video")
                return
            
            # Get video properties
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            fps = cap.get(cv2.CAP_PROP_FPS) or 30
            
            # Calculate frame interval to get desired number of frames
            if total_frames > self.max_frames:
                frame_interval = total_frames // self.max_frames
            else:
                frame_interval = 1
            
            self.progress_update.emit(30)
            
            frame_count = 0
            extracted_count = 0
            
            while self.is_running and extracted_count < self.max_frames:
                ret, frame = cap.read()
                if not ret:
                    break
                
                # Extract frame at intervals
                if frame_count % frame_interval == 0:
                    self.frames.append(frame.copy())
                    self.frame_extracted.emit(frame.copy(), extracted_count)
                    extracted_count += 1
                    
                    # Update progress
                    progress = 30 + int((extracted_count / self.max_frames) * 60)
                    self.progress_update.emit(progress)
                
                frame_count += 1
            
            cap.release()
            self.progress_update.emit(100)
            self.extraction_complete.emit(len(self.frames))
            
        except Exception as e:
            self.error_occurred.emit(f"Frame extraction error: {str(e)}")
    
    def get_direct_video_url(self) -> Optional[str]:
        """Get direct video URL using yt-dlp"""
        try:
            cmd = [
                'python', '-m', 'yt_dlp',
                '--get-url',
                '--format', 'best[height<=720]',
                self.video_url
            ]

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0 and result.stdout.strip():
                return result.stdout.strip()
            else:
                return None
                
        except Exception:
            return None
    
    def stop(self):
        """Stop frame extraction"""
        self.is_running = False


class FrameSelectorWidget(QWidget):
    """Widget for selecting frames from videos with time slider"""
    
    frame_selected = pyqtSignal(np.ndarray)  # Emits selected frame
    region_changed = pyqtSignal(tuple)  # Emits region selection
    
    def __init__(self, title: str = "Frame & Region Selector"):
        super().__init__()
        self.title = title
        self.frames = []
        self.current_frame_index = 0
        self.extractor_thread = None
        
        self.init_ui()
    
    def init_ui(self):
        """Initialize the user interface"""
        layout = QVBoxLayout()
        
        # Title
        title_label = QLabel(self.title)
        title_label.setStyleSheet("font-weight: bold; font-size: 14px; color: #333;")
        layout.addWidget(title_label)
        
        # Video URL input
        url_group = QGroupBox("Video Source")
        url_layout = QVBoxLayout()
        
        url_input_layout = QHBoxLayout()
        url_input_layout.addWidget(QLabel("YouTube URL:"))
        
        self.url_input = QLineEdit()
        self.url_input.setPlaceholderText("https://www.youtube.com/watch?v=...")
        self.url_input.setText("https://www.youtube.com/@MyFundedFuturesPropFirm/streams")
        url_input_layout.addWidget(self.url_input)
        
        self.load_button = QPushButton("Load Video")
        self.load_button.setStyleSheet("QPushButton { background-color: #2196F3; color: white; font-weight: bold; }")
        self.load_button.clicked.connect(self.load_video)
        url_input_layout.addWidget(self.load_button)
        
        url_layout.addLayout(url_input_layout)
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        url_layout.addWidget(self.progress_bar)
        
        # Status label
        self.status_label = QLabel("Enter a YouTube URL and click 'Load Video'")
        self.status_label.setStyleSheet("color: #666; font-size: 11px;")
        url_layout.addWidget(self.status_label)
        
        url_group.setLayout(url_layout)
        layout.addWidget(url_group)
        
        # Frame selection
        frame_group = QGroupBox("Frame Selection")
        frame_layout = QVBoxLayout()
        
        # Frame slider
        slider_layout = QHBoxLayout()
        slider_layout.addWidget(QLabel("Frame:"))
        
        self.frame_slider = QSlider(Qt.Orientation.Horizontal)
        self.frame_slider.setMinimum(0)
        self.frame_slider.setMaximum(0)
        self.frame_slider.valueChanged.connect(self.on_frame_changed)
        self.frame_slider.setEnabled(False)
        slider_layout.addWidget(self.frame_slider)
        
        self.frame_spinbox = QSpinBox()
        self.frame_spinbox.setMinimum(0)
        self.frame_spinbox.setMaximum(0)
        self.frame_spinbox.valueChanged.connect(self.on_frame_changed)
        self.frame_spinbox.setEnabled(False)
        slider_layout.addWidget(self.frame_spinbox)
        
        frame_layout.addLayout(slider_layout)
        
        # Frame info
        self.frame_info = QLabel("No frames loaded")
        self.frame_info.setStyleSheet("color: #666; font-size: 11px;")
        frame_layout.addWidget(self.frame_info)
        
        frame_group.setLayout(frame_layout)
        layout.addWidget(frame_group)
        
        # Region selector
        self.region_selector = RegionSelectorWidget("Select OCR Scanning Region")
        self.region_selector.region_changed.connect(self.on_region_changed)
        layout.addWidget(self.region_selector)
        
        self.setLayout(layout)
    
    def load_video(self):
        """Load video and extract frames"""
        url = self.url_input.text().strip()
        if not url:
            self.status_label.setText("Please enter a YouTube URL")
            return
        
        # Reset state
        self.frames = []
        self.current_frame_index = 0
        self.frame_slider.setEnabled(False)
        self.frame_spinbox.setEnabled(False)
        
        # Show progress
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.status_label.setText("Loading video...")
        self.load_button.setEnabled(False)
        
        # Start extraction thread
        self.extractor_thread = VideoFrameExtractor(url, max_frames=50)
        self.extractor_thread.frame_extracted.connect(self.on_frame_extracted)
        self.extractor_thread.progress_update.connect(self.progress_bar.setValue)
        self.extractor_thread.extraction_complete.connect(self.on_extraction_complete)
        self.extractor_thread.error_occurred.connect(self.on_extraction_error)
        self.extractor_thread.start()
    
    def on_frame_extracted(self, frame: np.ndarray, frame_number: int):
        """Handle extracted frame"""
        self.frames.append(frame)
        self.status_label.setText(f"Extracted frame {frame_number + 1}...")
        
        # Update first frame immediately
        if frame_number == 0:
            self.region_selector.set_image(frame)
            self.frame_selected.emit(frame)
    
    def on_extraction_complete(self, total_frames: int):
        """Handle extraction completion"""
        self.progress_bar.setVisible(False)
        self.load_button.setEnabled(True)
        
        if total_frames > 0:
            self.status_label.setText(f"Loaded {total_frames} frames successfully")
            
            # Enable frame controls
            self.frame_slider.setMaximum(total_frames - 1)
            self.frame_slider.setValue(0)
            self.frame_slider.setEnabled(True)
            
            self.frame_spinbox.setMaximum(total_frames - 1)
            self.frame_spinbox.setValue(0)
            self.frame_spinbox.setEnabled(True)
            
            # Update frame info
            self.update_frame_info()
            
            # Set first frame
            if self.frames:
                self.region_selector.set_image(self.frames[0])
                self.frame_selected.emit(self.frames[0])
        else:
            self.status_label.setText("No frames extracted")
    
    def on_extraction_error(self, error_message: str):
        """Handle extraction error"""
        self.progress_bar.setVisible(False)
        self.load_button.setEnabled(True)
        self.status_label.setText(f"Error: {error_message}")
    
    def on_frame_changed(self, frame_index: int):
        """Handle frame selection change"""
        if 0 <= frame_index < len(self.frames):
            self.current_frame_index = frame_index
            
            # Update both slider and spinbox
            if self.frame_slider.value() != frame_index:
                self.frame_slider.setValue(frame_index)
            if self.frame_spinbox.value() != frame_index:
                self.frame_spinbox.setValue(frame_index)
            
            # Update frame display
            frame = self.frames[frame_index]
            self.region_selector.set_image(frame)
            self.frame_selected.emit(frame)
            
            # Update frame info
            self.update_frame_info()
    
    def update_frame_info(self):
        """Update frame information display"""
        if self.frames:
            total = len(self.frames)
            current = self.current_frame_index + 1
            self.frame_info.setText(f"Frame {current} of {total}")
        else:
            self.frame_info.setText("No frames loaded")
    
    def on_region_changed(self, region_percent: tuple):
        """Handle region selection change"""
        self.region_changed.emit(region_percent)
    
    def get_current_frame(self) -> Optional[np.ndarray]:
        """Get currently selected frame"""
        if 0 <= self.current_frame_index < len(self.frames):
            return self.frames[self.current_frame_index]
        return None
    
    def get_region_percent(self) -> tuple:
        """Get current region selection as percentages"""
        return self.region_selector.get_region_percent()
    
    def get_region_pixels(self, image_width: int, image_height: int) -> tuple:
        """Get current region in pixel coordinates"""
        return self.region_selector.get_region_pixels(image_width, image_height)


if __name__ == "__main__":
    # Test the frame selector
    from PyQt6.QtWidgets import QApplication
    import sys
    
    app = QApplication(sys.argv)
    
    widget = FrameSelectorWidget("Test Frame Selector")
    widget.show()
    
    sys.exit(app.exec())
