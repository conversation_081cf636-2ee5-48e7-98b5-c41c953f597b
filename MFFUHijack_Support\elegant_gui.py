"""
Elegant PyQt6 GUI for MFFUHijack
Simple, clean design with optimized sizing and smooth animations
"""

import logging
from datetime import datetime
from PyQt6.QtWidgets import (
    QMainWindow, QTabWidget, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QLineEdit, QPushButton, QComboBox, QTextEdit,
    QProgressBar, QGroupBox, QGridLayout, QCheckBox, QFrame
)
from PyQt6.QtCore import Qt, pyqtSignal, QObject, QTimer, QPropertyAnimation, QEasingCurve, QRect
from PyQt6.QtGui import QPixmap, QAction, QFont

# Import our modules
from ocr_utils import ocr_manager

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


# Elegant UI Constants
class ElegantUI:
    """Elegant UI design constants with optimized sizing"""
    
    # Colors - Clean palette
    PRIMARY = "#3B82F6"        # Blue 500
    SECONDARY = "#F59E0B"      # Amber 500
    SUCCESS = "#10B981"        # Emerald 500
    WARNING = "#F59E0B"        # Amber 500
    ERROR = "#EF4444"          # Red 500
    BACKGROUND = "#F8FAFC"     # Slate 50
    SURFACE = "#FFFFFF"        # White
    TEXT_PRIMARY = "#1E293B"   # Slate 800
    TEXT_SECONDARY = "#64748B" # Slate 500
    BORDER = "#E2E8F0"         # Slate 200
    
    # Typography
    FONT = "Segoe UI, system-ui, sans-serif"
    SIZE_LARGE = 16
    SIZE_MEDIUM = 14
    SIZE_SMALL = 12
    
    # Spacing
    SPACE_XL = 24
    SPACE_L = 16
    SPACE_M = 12
    SPACE_S = 8
    SPACE_XS = 4
    
    # Component sizes
    BUTTON_H = 36
    INPUT_H = 32
    RADIUS = 6


def get_elegant_style():
    """Get elegant stylesheet with proper sizing"""
    return f"""
    QMainWindow {{
        background-color: {ElegantUI.BACKGROUND};
        color: {ElegantUI.TEXT_PRIMARY};
        font-family: {ElegantUI.FONT};
        font-size: {ElegantUI.SIZE_MEDIUM}px;
    }}
    
    QTabWidget::pane {{
        border: 1px solid {ElegantUI.BORDER};
        background-color: {ElegantUI.SURFACE};
        border-radius: {ElegantUI.RADIUS}px;
    }}
    
    QTabBar::tab {{
        background-color: transparent;
        color: {ElegantUI.TEXT_SECONDARY};
        padding: {ElegantUI.SPACE_S}px {ElegantUI.SPACE_M}px;
        margin-right: {ElegantUI.SPACE_XS}px;
        border-radius: {ElegantUI.RADIUS}px;
        font-weight: 500;
        min-width: 80px;
        min-height: {ElegantUI.BUTTON_H - 8}px;
    }}
    
    QTabBar::tab:selected {{
        background-color: {ElegantUI.PRIMARY};
        color: white;
        font-weight: 600;
    }}
    
    QTabBar::tab:hover:!selected {{
        background-color: {ElegantUI.BORDER};
    }}
    
    QGroupBox {{
        font-weight: 600;
        color: {ElegantUI.TEXT_PRIMARY};
        border: 1px solid {ElegantUI.BORDER};
        border-radius: {ElegantUI.RADIUS}px;
        margin-top: {ElegantUI.SPACE_S}px;
        padding-top: {ElegantUI.SPACE_M}px;
        background-color: {ElegantUI.SURFACE};
    }}
    
    QGroupBox::title {{
        subcontrol-origin: margin;
        left: {ElegantUI.SPACE_S}px;
        padding: 0 {ElegantUI.SPACE_S}px;
        background-color: {ElegantUI.SURFACE};
        color: {ElegantUI.PRIMARY};
    }}
    
    QPushButton {{
        background-color: {ElegantUI.PRIMARY};
        color: white;
        border: none;
        border-radius: {ElegantUI.RADIUS}px;
        padding: {ElegantUI.SPACE_S}px {ElegantUI.SPACE_M}px;
        font-weight: 500;
        min-height: {ElegantUI.BUTTON_H}px;
        min-width: 80px;
    }}
    
    QPushButton:hover {{
        background-color: #2563EB;
    }}
    
    QPushButton:pressed {{
        background-color: #1D4ED8;
    }}
    
    QPushButton:disabled {{
        background-color: {ElegantUI.BORDER};
        color: {ElegantUI.TEXT_SECONDARY};
    }}
    
    QLineEdit {{
        border: 1px solid {ElegantUI.BORDER};
        border-radius: {ElegantUI.RADIUS}px;
        padding: {ElegantUI.SPACE_S}px;
        background-color: {ElegantUI.SURFACE};
        min-height: {ElegantUI.INPUT_H}px;
        color: {ElegantUI.TEXT_PRIMARY};
    }}
    
    QLineEdit:focus {{
        border-color: {ElegantUI.PRIMARY};
        border-width: 2px;
    }}
    
    QComboBox {{
        border: 1px solid {ElegantUI.BORDER};
        border-radius: {ElegantUI.RADIUS}px;
        padding: {ElegantUI.SPACE_S}px;
        background-color: {ElegantUI.SURFACE};
        min-height: {ElegantUI.INPUT_H}px;
        color: {ElegantUI.TEXT_PRIMARY};
    }}
    
    QComboBox:focus {{
        border-color: {ElegantUI.PRIMARY};
        border-width: 2px;
    }}
    
    QTextEdit {{
        border: 1px solid {ElegantUI.BORDER};
        border-radius: {ElegantUI.RADIUS}px;
        background-color: {ElegantUI.SURFACE};
        font-family: 'Consolas', monospace;
        font-size: {ElegantUI.SIZE_SMALL}px;
        padding: {ElegantUI.SPACE_S}px;
        color: {ElegantUI.TEXT_PRIMARY};
    }}
    
    QTextEdit:focus {{
        border-color: {ElegantUI.PRIMARY};
        border-width: 2px;
    }}
    
    QProgressBar {{
        border: 1px solid {ElegantUI.BORDER};
        border-radius: {ElegantUI.RADIUS}px;
        text-align: center;
        background-color: {ElegantUI.BACKGROUND};
        color: {ElegantUI.TEXT_PRIMARY};
        min-height: 20px;
    }}
    
    QProgressBar::chunk {{
        background-color: {ElegantUI.PRIMARY};
        border-radius: {ElegantUI.RADIUS - 1}px;
        margin: 1px;
    }}
    
    QLabel {{
        color: {ElegantUI.TEXT_PRIMARY};
    }}
    
    QCheckBox {{
        color: {ElegantUI.TEXT_PRIMARY};
        spacing: {ElegantUI.SPACE_S}px;
    }}
    
    QCheckBox::indicator {{
        width: 16px;
        height: 16px;
        border: 1px solid {ElegantUI.BORDER};
        border-radius: 3px;
        background-color: {ElegantUI.SURFACE};
    }}
    
    QCheckBox::indicator:checked {{
        background-color: {ElegantUI.PRIMARY};
        border-color: {ElegantUI.PRIMARY};
    }}
    """


class MessageLogger(QObject):
    """Centralized message logger"""
    
    message_logged = pyqtSignal(str, str)  # message, level
    
    def __init__(self):
        super().__init__()
    
    def log(self, message: str, level: str = "INFO"):
        """Log a message"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {level}: {message}"
        self.message_logged.emit(formatted_message, level)
    
    def info(self, message: str):
        self.log(message, "INFO")
    
    def warning(self, message: str):
        self.log(message, "WARNING")
    
    def error(self, message: str):
        self.log(message, "ERROR")
    
    def success(self, message: str):
        self.log(message, "SUCCESS")


# Global message logger
message_logger = MessageLogger()


class ElegantMessageLoggerWindow(QMainWindow):
    """Elegant message logger window"""

    def __init__(self, logger: MessageLogger):
        super().__init__()
        self.logger = logger
        self.messages = []
        self.init_ui()
        self.connect_signals()

    def init_ui(self):
        """Initialize logger window"""
        self.setWindowTitle("MFFUHijack - Message Logger")
        self.setMinimumSize(800, 600)
        self.resize(800, 600)

        # Central widget
        central = QWidget()
        self.setCentralWidget(central)

        # Layout
        layout = QVBoxLayout(central)
        layout.setContentsMargins(ElegantUI.SPACE_L, ElegantUI.SPACE_L,
                                ElegantUI.SPACE_L, ElegantUI.SPACE_L)
        layout.setSpacing(ElegantUI.SPACE_M)

        # Header
        header = QLabel("📋 Message Logger")
        header.setStyleSheet(f"""
            QLabel {{
                font-size: {ElegantUI.SIZE_LARGE + 2}px;
                font-weight: 700;
                color: {ElegantUI.PRIMARY};
                padding: {ElegantUI.SPACE_M}px 0;
            }}
        """)
        layout.addWidget(header)

        # Controls
        controls = QHBoxLayout()

        clear_btn = QPushButton("🗑️ Clear")
        clear_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {ElegantUI.ERROR};
                min-width: 80px;
            }}
            QPushButton:hover {{
                background-color: #DC2626;
            }}
        """)
        clear_btn.clicked.connect(self.clear_messages)
        controls.addWidget(clear_btn)

        auto_scroll = QCheckBox("📜 Auto-scroll")
        auto_scroll.setChecked(True)
        controls.addWidget(auto_scroll)
        self.auto_scroll = auto_scroll

        controls.addStretch()
        layout.addLayout(controls)

        # Message display
        self.display = QTextEdit()
        self.display.setReadOnly(True)
        self.display.setStyleSheet(f"""
            QTextEdit {{
                background-color: #1E1E1E;
                color: #FFFFFF;
                font-family: 'Consolas', monospace;
                font-size: {ElegantUI.SIZE_SMALL}px;
                line-height: 1.4;
            }}
        """)
        layout.addWidget(self.display, 1)

        # Status
        self.status = QLabel("Messages: 0")
        self.status.setStyleSheet(f"color: {ElegantUI.TEXT_SECONDARY}; font-size: {ElegantUI.SIZE_SMALL}px;")
        layout.addWidget(self.status)

    def connect_signals(self):
        """Connect signals"""
        self.logger.message_logged.connect(self.add_message)

    def add_message(self, message: str, level: str):
        """Add message to display"""
        self.messages.append((message, level))

        # Color mapping
        colors = {
            "ERROR": "#FF6B6B",
            "WARNING": "#FFB347",
            "SUCCESS": "#51CF66",
            "INFO": "#E9ECEF"
        }

        color = colors.get(level, "#E9ECEF")

        # Format message
        html = f'''
        <div style="margin: 4px 0; padding: 8px; background-color: rgba(255,255,255,0.05);
                   border-left: 3px solid {color}; border-radius: 4px;">
            <span style="color: {color}; font-weight: bold; font-size: 10px;">[{level}]</span>
            <span style="color: #E9ECEF; margin-left: 8px;">{message.split('] ', 1)[-1] if '] ' in message else message}</span>
        </div>
        '''

        self.display.append(html)

        if self.auto_scroll.isChecked():
            scrollbar = self.display.verticalScrollBar()
            scrollbar.setValue(scrollbar.maximum())

        self.status.setText(f"Messages: {len(self.messages)}")

    def clear_messages(self):
        """Clear all messages"""
        self.messages.clear()
        self.display.clear()
        self.status.setText("Messages: 0")


class ElegantLivestreamTab(QWidget):
    """Elegant livestream bot interface"""

    # Signals
    start_bot_signal = pyqtSignal(str, str, float)
    stop_bot_signal = pyqtSignal()

    def __init__(self):
        super().__init__()
        self.ocr_region = (0, 67, 100, 33)
        self.init_ui()

    def init_ui(self):
        """Initialize livestream tab"""
        # Main layout
        layout = QVBoxLayout(self)
        layout.setContentsMargins(ElegantUI.SPACE_L, ElegantUI.SPACE_L,
                                ElegantUI.SPACE_L, ElegantUI.SPACE_L)
        layout.setSpacing(ElegantUI.SPACE_M)

        # Configuration section
        config_group = QGroupBox("🔧 Configuration")
        config_layout = QGridLayout(config_group)
        config_layout.setSpacing(ElegantUI.SPACE_M)

        # URL input
        config_layout.addWidget(QLabel("📺 Stream URL:"), 0, 0)
        self.url_input = QLineEdit()
        self.url_input.setPlaceholderText("https://www.youtube.com/watch?v=...")
        self.url_input.setText("https://www.youtube.com/@MyFundedFuturesPropFirm/streams")
        config_layout.addWidget(self.url_input, 0, 1, 1, 3)

        # OCR settings
        config_layout.addWidget(QLabel("🔍 OCR Engine:"), 1, 0)
        self.ocr_combo = QComboBox()
        self.ocr_combo.addItems(["EasyOCR", "Custom"])
        config_layout.addWidget(self.ocr_combo, 1, 1)

        config_layout.addWidget(QLabel("⏱️ Interval (s):"), 1, 2)
        self.interval_input = QLineEdit("1.0")
        self.interval_input.setMaximumWidth(80)
        config_layout.addWidget(self.interval_input, 1, 3)

        # Pattern and region buttons
        self.pattern_btn = QPushButton("⚙️ Configure Patterns")
        self.pattern_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {ElegantUI.SECONDARY};
                min-width: 140px;
            }}
            QPushButton:hover {{
                background-color: #D97706;
            }}
        """)
        self.pattern_btn.clicked.connect(self.configure_patterns)
        config_layout.addWidget(self.pattern_btn, 2, 0)

        self.region_btn = QPushButton("🎯 Select Region")
        self.region_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {ElegantUI.SUCCESS};
                min-width: 120px;
            }}
            QPushButton:hover {{
                background-color: #059669;
            }}
        """)
        self.region_btn.clicked.connect(self.select_region)
        config_layout.addWidget(self.region_btn, 2, 1)

        # Status labels
        self.account_types_label = QLabel("Account Types: Loading...")
        self.account_types_label.setStyleSheet(f"""
            QLabel {{
                color: {ElegantUI.TEXT_SECONDARY};
                font-size: {ElegantUI.SIZE_SMALL}px;
                padding: {ElegantUI.SPACE_S}px;
                background-color: {ElegantUI.BACKGROUND};
                border-radius: {ElegantUI.RADIUS}px;
                border: 1px solid {ElegantUI.BORDER};
            }}
        """)
        config_layout.addWidget(self.account_types_label, 2, 2, 1, 2)

        self.region_label = QLabel("Region: Bottom third (0%, 67%, 100% × 33%)")
        self.region_label.setStyleSheet(f"""
            QLabel {{
                color: {ElegantUI.TEXT_SECONDARY};
                font-size: {ElegantUI.SIZE_SMALL}px;
                padding: {ElegantUI.SPACE_S}px;
                background-color: {ElegantUI.BACKGROUND};
                border-radius: {ElegantUI.RADIUS}px;
                border: 1px solid {ElegantUI.BORDER};
            }}
        """)
        config_layout.addWidget(self.region_label, 3, 0, 1, 4)

        layout.addWidget(config_group)

        # Control section
        control_group = QGroupBox("🎮 Control")
        control_layout = QHBoxLayout(control_group)
        control_layout.setSpacing(ElegantUI.SPACE_L)

        # Start button
        self.start_btn = QPushButton("▶️ Start Bot")
        self.start_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {ElegantUI.SUCCESS};
                min-height: {ElegantUI.BUTTON_H + 8}px;
                min-width: 120px;
                font-weight: 600;
            }}
            QPushButton:hover {{
                background-color: #059669;
            }}
        """)
        self.start_btn.clicked.connect(self.start_bot)
        control_layout.addWidget(self.start_btn)

        # Stop button
        self.stop_btn = QPushButton("⏹️ Stop Bot")
        self.stop_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {ElegantUI.ERROR};
                min-height: {ElegantUI.BUTTON_H + 8}px;
                min-width: 120px;
                font-weight: 600;
            }}
            QPushButton:hover {{
                background-color: #DC2626;
            }}
        """)
        self.stop_btn.setEnabled(False)
        self.stop_btn.clicked.connect(self.stop_bot)
        control_layout.addWidget(self.stop_btn)

        # Status
        self.status_label = QLabel("🔴 Status: Idle")
        self.status_label.setStyleSheet(f"""
            QLabel {{
                font-weight: 600;
                color: {ElegantUI.TEXT_PRIMARY};
                padding: {ElegantUI.SPACE_M}px;
                background-color: {ElegantUI.BACKGROUND};
                border-radius: {ElegantUI.RADIUS}px;
                border: 1px solid {ElegantUI.BORDER};
                min-height: {ElegantUI.BUTTON_H}px;
            }}
        """)
        control_layout.addWidget(self.status_label)
        control_layout.addStretch()

        layout.addWidget(control_group)

        # Preview section
        preview_group = QGroupBox("📺 Live Preview")
        preview_layout = QVBoxLayout(preview_group)

        self.frame_preview = QLabel("🎬 No frame loaded - Start bot to see preview")
        self.frame_preview.setMinimumSize(480, 270)
        self.frame_preview.setStyleSheet(f"""
            QLabel {{
                border: 2px dashed {ElegantUI.BORDER};
                background-color: {ElegantUI.BACKGROUND};
                border-radius: {ElegantUI.RADIUS}px;
                color: {ElegantUI.TEXT_SECONDARY};
                font-weight: 500;
            }}
        """)
        self.frame_preview.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.frame_preview.setScaledContents(True)
        preview_layout.addWidget(self.frame_preview)

        layout.addWidget(preview_group, 1)

        # Activity log
        activity_group = QGroupBox("📋 Activity")
        activity_layout = QVBoxLayout(activity_group)

        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(120)
        self.log_text.setReadOnly(True)
        self.log_text.setPlaceholderText("Activity messages will appear here...")
        activity_layout.addWidget(self.log_text)

        layout.addWidget(activity_group)

        # Update displays
        self.update_account_types_display()

    def update_account_types_display(self):
        """Update account types display"""
        try:
            enabled_types = ocr_manager.get_enabled_account_types()
            if enabled_types:
                types_text = ", ".join(enabled_types)
                self.account_types_label.setText(f"✅ Enabled: {types_text}")
            else:
                self.account_types_label.setText("⚠️ No account types configured")
        except Exception:
            self.account_types_label.setText("❌ Error loading account types")

    def configure_patterns(self):
        """Configure detection patterns"""
        try:
            from pattern_config_dialog import show_pattern_config_dialog
            new_patterns = show_pattern_config_dialog(self)
            if new_patterns:
                ocr_manager.update_pattern_config(new_patterns)
                self.update_account_types_display()
                self.log_message("✅ Pattern configuration updated")
        except Exception as e:
            self.log_message(f"❌ Error configuring patterns: {str(e)}")

    def select_region(self):
        """Select OCR region"""
        try:
            from region_selector import RegionSelectorWidget
            from PyQt6.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout

            dialog = QDialog(self)
            dialog.setWindowTitle("Select OCR Region")
            dialog.setModal(True)
            dialog.resize(700, 500)

            layout = QVBoxLayout(dialog)

            region_selector = RegionSelectorWidget()
            region_selector.set_region_percent(*self.ocr_region)

            # Create sample frame
            import numpy as np
            import cv2
            sample_frame = np.ones((720, 1280, 3), dtype=np.uint8) * 240
            sample_frame[:, :160] = 0
            sample_frame[:, 1120:] = 0
            cv2.putText(sample_frame, "SAMPLE LIVESTREAM", (400, 100),
                       cv2.FONT_HERSHEY_SIMPLEX, 1.5, (255, 255, 255), 2)
            cv2.putText(sample_frame, "x5 FREE RESETS USE CODE: RESET3J", (300, 600),
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)

            region_selector.set_frame(sample_frame)
            layout.addWidget(region_selector)

            # Buttons
            button_layout = QHBoxLayout()
            apply_btn = QPushButton("Apply Region")
            apply_btn.clicked.connect(lambda: self.apply_region(region_selector, dialog))
            button_layout.addWidget(apply_btn)

            cancel_btn = QPushButton("Cancel")
            cancel_btn.clicked.connect(dialog.reject)
            button_layout.addWidget(cancel_btn)

            layout.addLayout(button_layout)
            dialog.exec()

        except Exception as e:
            self.log_message(f"❌ Error opening region selector: {str(e)}")

    def apply_region(self, region_selector, dialog):
        """Apply selected region"""
        self.ocr_region = region_selector.get_region_percent()
        x, y, w, h = self.ocr_region
        self.region_label.setText(f"Region: {x:.1f}%, {y:.1f}%, {w:.1f}% × {h:.1f}%")
        self.log_message(f"✅ OCR region updated: {x:.1f}%, {y:.1f}%, {w:.1f}% × {h:.1f}%")
        dialog.accept()

    def start_bot(self):
        """Start the bot"""
        url = self.url_input.text().strip()
        if not url:
            self.log_message("❌ ERROR: Please enter a livestream URL")
            return

        ocr_engine = self.ocr_combo.currentText()
        try:
            interval = float(self.interval_input.text())
        except ValueError:
            interval = 1.0
            self.interval_input.setText("1.0")

        # Update UI
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        self.status_label.setText("🟡 Status: Starting...")
        self.status_label.setStyleSheet(f"""
            QLabel {{
                font-weight: 600;
                color: {ElegantUI.WARNING};
                padding: {ElegantUI.SPACE_M}px;
                background-color: {ElegantUI.BACKGROUND};
                border-radius: {ElegantUI.RADIUS}px;
                border: 1px solid {ElegantUI.WARNING};
                min-height: {ElegantUI.BUTTON_H}px;
            }}
        """)

        # Emit signal
        self.start_bot_signal.emit(url, ocr_engine, interval)
        self.log_message(f"🚀 Starting bot with URL: {url}")

    def stop_bot(self):
        """Stop the bot"""
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.status_label.setText("🔴 Status: Stopping...")
        self.stop_bot_signal.emit()
        self.log_message("⏹️ Stopping bot...")

    def update_status(self, status: str):
        """Update status label"""
        if "running" in status.lower():
            color = ElegantUI.SUCCESS
            icon = "🟢"
        elif "error" in status.lower():
            color = ElegantUI.ERROR
            icon = "🔴"
        else:
            color = ElegantUI.TEXT_SECONDARY
            icon = "🔴"

        self.status_label.setText(f"{icon} Status: {status}")
        self.status_label.setStyleSheet(f"""
            QLabel {{
                font-weight: 600;
                color: {color};
                padding: {ElegantUI.SPACE_M}px;
                background-color: {ElegantUI.BACKGROUND};
                border-radius: {ElegantUI.RADIUS}px;
                border: 1px solid {color};
                min-height: {ElegantUI.BUTTON_H}px;
            }}
        """)

    def update_frame_preview(self, pixmap: QPixmap):
        """Update frame preview"""
        self.frame_preview.setPixmap(pixmap)

    def log_message(self, message: str):
        """Add message to activity log"""
        self.log_text.append(message)
        scrollbar = self.log_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())

        # Send to centralized logger
        if "ERROR" in message.upper() or "❌" in message:
            message_logger.error(message)
        elif "WARNING" in message.upper() or "⚠️" in message:
            message_logger.warning(message)
        elif "✅" in message:
            message_logger.success(message)
        else:
            message_logger.info(message)


class ElegantDatasetTab(QWidget):
    """Elegant dataset building interface"""

    # Signals
    start_dataset_signal = pyqtSignal(str, int, str)
    start_training_signal = pyqtSignal()

    def __init__(self):
        super().__init__()
        self.init_ui()

    def init_ui(self):
        """Initialize dataset tab"""
        # Main layout
        layout = QVBoxLayout(self)
        layout.setContentsMargins(ElegantUI.SPACE_L, ElegantUI.SPACE_L,
                                ElegantUI.SPACE_L, ElegantUI.SPACE_L)
        layout.setSpacing(ElegantUI.SPACE_M)

        # Dataset building section
        dataset_group = QGroupBox("📊 Dataset Building")
        dataset_layout = QGridLayout(dataset_group)
        dataset_layout.setSpacing(ElegantUI.SPACE_M)

        # Channel URL
        dataset_layout.addWidget(QLabel("📺 YouTube Channel:"), 0, 0)
        self.channel_input = QLineEdit()
        self.channel_input.setPlaceholderText("https://www.youtube.com/@channel")
        dataset_layout.addWidget(self.channel_input, 0, 1, 1, 2)

        # Target frames
        dataset_layout.addWidget(QLabel("🖼️ Target Frames:"), 1, 0)
        self.frames_input = QLineEdit("100")
        self.frames_input.setMaximumWidth(80)
        dataset_layout.addWidget(self.frames_input, 1, 1)

        # Region selection
        dataset_layout.addWidget(QLabel("📐 Region:"), 1, 2)
        self.region_combo = QComboBox()
        self.region_combo.addItems(["Full Frame", "Bottom Third", "Custom"])
        self.region_combo.setCurrentText("Bottom Third")
        dataset_layout.addWidget(self.region_combo, 1, 3)

        # Start button
        self.start_dataset_btn = QPushButton("🚀 Start Dataset Building")
        self.start_dataset_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {ElegantUI.PRIMARY};
                min-height: {ElegantUI.BUTTON_H}px;
                min-width: 160px;
                font-weight: 600;
            }}
            QPushButton:hover {{
                background-color: #2563EB;
            }}
        """)
        self.start_dataset_btn.clicked.connect(self.start_dataset_building)
        dataset_layout.addWidget(self.start_dataset_btn, 2, 0, 1, 2)

        # Progress bar
        self.dataset_progress = QProgressBar()
        self.dataset_progress.setVisible(False)
        dataset_layout.addWidget(self.dataset_progress, 2, 2, 1, 2)

        layout.addWidget(dataset_group)

        # Training section
        training_group = QGroupBox("🧠 Model Training")
        training_layout = QGridLayout(training_group)
        training_layout.setSpacing(ElegantUI.SPACE_M)

        # Training parameters
        training_layout.addWidget(QLabel("🔄 Epochs:"), 0, 0)
        self.epochs_input = QLineEdit("10")
        self.epochs_input.setMaximumWidth(60)
        training_layout.addWidget(self.epochs_input, 0, 1)

        training_layout.addWidget(QLabel("📦 Batch Size:"), 0, 2)
        self.batch_input = QLineEdit("32")
        self.batch_input.setMaximumWidth(60)
        training_layout.addWidget(self.batch_input, 0, 3)

        # Start training button
        self.start_training_btn = QPushButton("🎯 Start Training")
        self.start_training_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {ElegantUI.SUCCESS};
                min-height: {ElegantUI.BUTTON_H}px;
                min-width: 120px;
                font-weight: 600;
            }}
            QPushButton:hover {{
                background-color: #059669;
            }}
        """)
        self.start_training_btn.clicked.connect(self.start_training)
        training_layout.addWidget(self.start_training_btn, 1, 0, 1, 2)

        # Training progress
        self.training_progress = QProgressBar()
        self.training_progress.setVisible(False)
        training_layout.addWidget(self.training_progress, 1, 2, 1, 2)

        layout.addWidget(training_group)

        # Progress log
        log_group = QGroupBox("📈 Progress Log")
        log_layout = QVBoxLayout(log_group)

        self.progress_log = QTextEdit()
        self.progress_log.setReadOnly(True)
        self.progress_log.setPlaceholderText("Progress messages will appear here...")
        log_layout.addWidget(self.progress_log)

        layout.addWidget(log_group, 1)

    def start_dataset_building(self):
        """Start dataset building"""
        channel_url = self.channel_input.text().strip()
        if not channel_url:
            self.log_progress("❌ ERROR: Please enter a YouTube channel URL")
            return

        try:
            num_frames = int(self.frames_input.text())
        except ValueError:
            num_frames = 100
            self.frames_input.setText("100")

        region = self.region_combo.currentText()

        # Update UI
        self.start_dataset_btn.setEnabled(False)
        self.dataset_progress.setVisible(True)
        self.dataset_progress.setValue(0)

        # Emit signal
        self.start_dataset_signal.emit(channel_url, num_frames, region)
        self.log_progress(f"🚀 Starting dataset building for: {channel_url}")

    def start_training(self):
        """Start model training"""
        try:
            epochs = int(self.epochs_input.text())
            batch_size = int(self.batch_input.text())
        except ValueError:
            epochs = 10
            batch_size = 32
            self.epochs_input.setText("10")
            self.batch_input.setText("32")

        # Update UI
        self.start_training_btn.setEnabled(False)
        self.training_progress.setVisible(True)
        self.training_progress.setValue(0)

        # Emit signal
        self.start_training_signal.emit()
        self.log_progress(f"🧠 Starting model training (Epochs: {epochs}, Batch: {batch_size})")

    def reset_ui_state(self):
        """Reset UI to initial state"""
        self.start_dataset_btn.setEnabled(True)
        self.start_training_btn.setEnabled(True)
        self.dataset_progress.setVisible(False)
        self.training_progress.setVisible(False)

    def update_dataset_progress(self, value: int):
        """Update dataset progress"""
        self.dataset_progress.setValue(value)

    def update_training_progress(self, value: int):
        """Update training progress"""
        self.training_progress.setValue(value)

    def log_progress(self, message: str):
        """Add message to progress log"""
        self.progress_log.append(message)
        scrollbar = self.progress_log.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())

        # Send to centralized logger
        if "ERROR" in message.upper() or "❌" in message:
            message_logger.error(message)
        elif "WARNING" in message.upper() or "⚠️" in message:
            message_logger.warning(message)
        elif "✅" in message or "complete" in message.lower():
            message_logger.success(message)
        else:
            message_logger.info(message)


class ElegantMFFUHijackGUI(QMainWindow):
    """Elegant main application window with optimized sizing"""

    def __init__(self):
        super().__init__()
        self.message_logger_window = None
        self.live_scan_monitor = None
        self.init_ui()
        self.apply_styling()
        self.connect_signals()

    def init_ui(self):
        """Initialize elegant main window"""
        self.setWindowTitle("MFFUHijack - Real-Time OCR Livestream Code Detection")
        self.setMinimumSize(900, 650)
        self.resize(900, 650)

        # Create menu bar
        self.create_menu_bar()

        # Central widget
        central = QWidget()
        self.setCentralWidget(central)

        # Main layout with optimized spacing
        layout = QVBoxLayout(central)
        layout.setContentsMargins(ElegantUI.SPACE_M, ElegantUI.SPACE_S,
                                ElegantUI.SPACE_M, ElegantUI.SPACE_S)
        layout.setSpacing(ElegantUI.SPACE_S)

        # Header
        header = self.create_header()
        layout.addWidget(header)

        # Tab widget with proper sizing
        self.tab_widget = QTabWidget()
        self.tab_widget.setMinimumHeight(550)

        # Create tabs
        self.livestream_tab = ElegantLivestreamTab()
        self.dataset_tab = ElegantDatasetTab()

        # Add tabs
        self.tab_widget.addTab(self.livestream_tab, "🎯 Livestream")
        self.tab_widget.addTab(self.dataset_tab, "📊 Dataset")

        layout.addWidget(self.tab_widget, 1)

        # Create message logger window
        self.message_logger_window = ElegantMessageLoggerWindow(message_logger)
        self.message_logger_window.show()
        self.logger_action.setChecked(True)

        # Position logger window
        self.position_logger_window()

        # Log startup
        message_logger.info("MFFUHijack started with elegant interface")

    def create_header(self):
        """Create elegant header"""
        header = QFrame()
        header.setMaximumHeight(60)
        header.setStyleSheet(f"""
            QFrame {{
                background-color: {ElegantUI.SURFACE};
                border: 1px solid {ElegantUI.BORDER};
                border-radius: {ElegantUI.RADIUS}px;
            }}
        """)

        layout = QHBoxLayout(header)
        layout.setContentsMargins(ElegantUI.SPACE_L, ElegantUI.SPACE_M,
                                ElegantUI.SPACE_L, ElegantUI.SPACE_M)

        # Title
        title = QLabel("🎯 MFFUHijack")
        title.setStyleSheet(f"""
            QLabel {{
                font-size: {ElegantUI.SIZE_LARGE + 2}px;
                font-weight: 700;
                color: {ElegantUI.PRIMARY};
            }}
        """)
        layout.addWidget(title)

        # Subtitle
        subtitle = QLabel("Real-Time OCR Livestream Code Detection")
        subtitle.setStyleSheet(f"""
            QLabel {{
                font-size: {ElegantUI.SIZE_SMALL}px;
                color: {ElegantUI.TEXT_SECONDARY};
                margin-left: {ElegantUI.SPACE_M}px;
            }}
        """)
        layout.addWidget(subtitle)

        layout.addStretch()

        # Status indicator
        self.status_indicator = QLabel("🟢 Ready")
        self.status_indicator.setStyleSheet(f"""
            QLabel {{
                font-weight: 500;
                color: {ElegantUI.SUCCESS};
                padding: {ElegantUI.SPACE_S}px {ElegantUI.SPACE_M}px;
                background-color: {ElegantUI.BACKGROUND};
                border-radius: {ElegantUI.RADIUS}px;
                border: 1px solid {ElegantUI.BORDER};
            }}
        """)
        layout.addWidget(self.status_indicator)

        return header

    def create_menu_bar(self):
        """Create elegant menu bar"""
        menubar = self.menuBar()
        menubar.setStyleSheet(f"""
            QMenuBar {{
                background-color: {ElegantUI.SURFACE};
                color: {ElegantUI.TEXT_PRIMARY};
                border-bottom: 1px solid {ElegantUI.BORDER};
                padding: {ElegantUI.SPACE_XS}px;
            }}
            QMenuBar::item {{
                padding: {ElegantUI.SPACE_S}px {ElegantUI.SPACE_M}px;
                border-radius: {ElegantUI.RADIUS}px;
            }}
            QMenuBar::item:selected {{
                background-color: {ElegantUI.PRIMARY};
                color: white;
            }}
        """)

        # View menu
        view_menu = menubar.addMenu("View")

        self.logger_action = QAction("Message Logger", self)
        self.logger_action.setCheckable(True)
        self.logger_action.triggered.connect(self.toggle_logger)
        view_menu.addAction(self.logger_action)

        # Help menu
        help_menu = menubar.addMenu("Help")

        about_action = QAction("About", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

    def position_logger_window(self):
        """Position logger window relative to main window"""
        main_geometry = self.geometry()
        logger_x = main_geometry.x() + main_geometry.width() + 20
        logger_y = main_geometry.y()
        self.message_logger_window.setGeometry(logger_x, logger_y, 800, 600)

    def apply_styling(self):
        """Apply elegant styling"""
        self.setStyleSheet(get_elegant_style())

    def connect_signals(self):
        """Connect tab signals"""
        # Create Live Scan Monitor window
        try:
            from live_scan_monitor import LiveScanMonitorWindow
            self.live_scan_monitor = LiveScanMonitorWindow()
        except ImportError:
            message_logger.warning("Live Scan Monitor not available")

        # Livestream tab signals
        self.livestream_tab.start_bot_signal.connect(self.start_livestream_bot)
        self.livestream_tab.stop_bot_signal.connect(self.stop_livestream_bot)

        # Dataset tab signals
        self.dataset_tab.start_dataset_signal.connect(self.start_dataset_builder)
        self.dataset_tab.start_training_signal.connect(self.start_model_training)

    def toggle_logger(self):
        """Toggle message logger window"""
        if self.logger_action.isChecked():
            self.message_logger_window.show()
            self.position_logger_window()
        else:
            self.message_logger_window.hide()

    def show_about(self):
        """Show about dialog"""
        from PyQt6.QtWidgets import QMessageBox
        QMessageBox.about(self, "About MFFUHijack",
                         "MFFUHijack v2.0\n\n"
                         "Real-Time OCR Livestream Code Detection\n"
                         "Built with PyQt6 and modern design principles")

    # Placeholder methods for signal connections
    def start_livestream_bot(self, url: str, engine: str, interval: float):
        """Start livestream bot (placeholder)"""
        message_logger.info(f"Starting livestream bot: {url}")

    def stop_livestream_bot(self):
        """Stop livestream bot (placeholder)"""
        message_logger.info("Stopping livestream bot")

    def start_dataset_builder(self, url: str, frames: int, region: str):
        """Start dataset builder (placeholder)"""
        message_logger.info(f"Starting dataset builder: {url}")

    def start_model_training(self):
        """Start model training (placeholder)"""
        message_logger.info("Starting model training")
